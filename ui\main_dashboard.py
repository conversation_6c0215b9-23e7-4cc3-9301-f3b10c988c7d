from PyQt5.QtWidgets import (QMainWindow, QWidget, QVBoxLayout, QHBoxLayout, 
                            QMenuBar, QMenu, QAction, QStatusBar, QLabel,
                            QStackedWidget, QPushButton, QFrame, QMessageBox,
                            QToolBar, QSizePolicy, QGridLayout, QGroupBox)
from PyQt5.QtCore import Qt, QTimer, pyqtSignal
from PyQt5.QtGui import QFont, QIcon, QPixmap
from datetime import datetime
import json

# Import all modules
from ui.modules.customers_module import CustomersModule
from ui.modules.trucks_module import TrucksModule
from ui.modules.blocks_module import BlocksModule
from ui.modules.sales_module import SalesModule
from ui.modules.inventory_module import InventoryModule
from ui.modules.expenses_module import ExpensesModule
from ui.modules.reports_module import ReportsModule
from ui.modules.production_planning_module import ProductionPlanningModule
from ui.modules.material_procurement_module import MaterialProcurementModule
from ui.modules.quality_control_module import QualityControlModule
from ui.modules.maintenance_module import MaintenanceModule
from ui.modules.sales_quotation_module import SalesQuotationModule
from ui.modules.user_management_module import UserManagementModule
from ui.modules.backup_restore_module import BackupRestoreModule
from ui.modules.advanced_reports_module import AdvancedReportsModule
from ui.styles.modern_theme import ModernTheme
from ui.styles.components import ModernCard, StatsCard, ModernButton

class MainDashboard(QMainWindow):
    logout_requested = pyqtSignal()
    
    def __init__(self, db_manager, user_data, translator, font_manager, locale_manager, cultural_manager):
        super().__init__()
        self.db_manager = db_manager
        self.user_data = user_data
        self.translator = translator
        self.font_manager = font_manager
        self.locale_manager = locale_manager
        self.cultural_manager = cultural_manager
        
        self.init_ui()
        self.setup_modules()
        self.setup_timer()
        self.load_dashboard_data()
        
    def init_ui(self):
        """Initialize user interface with modern styling"""
        self.setWindowTitle("🏭 نظام إدارة مصنع الحسن ستون | Al-Hassan Stone Factory Management System")
        self.setGeometry(100, 100, 1600, 1000)
        self.setMinimumSize(1400, 900)

        # Apply modern theme
        self.setStyleSheet(ModernTheme.get_complete_stylesheet())

        # Set window icon
        self.setWindowIcon(QIcon('assets/icon.png'))
        
        # Create central widget
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        # Main layout
        main_layout = QVBoxLayout(central_widget)
        main_layout.setContentsMargins(0, 0, 0, 0)
        
        # Create menu bar
        self.create_menu_bar()
        
        # Create toolbar
        self.create_toolbar()
        
        # Create main content area
        content_layout = QHBoxLayout()
        
        # Create sidebar
        self.create_sidebar(content_layout)
        
        # Create main content stack
        self.content_stack = QStackedWidget()
        content_layout.addWidget(self.content_stack, 4)
        
        main_layout.addLayout(content_layout)
        
        # Create status bar
        self.create_status_bar()
        
        # Apply stylesheet
        self.apply_stylesheet()
        
    def create_menu_bar(self):
        """Create application menu bar"""
        menubar = self.menuBar()
        
        # File menu
        file_menu = menubar.addMenu("ملف / File")
        
        # New submenu
        new_menu = file_menu.addMenu("جديد / New")
        
        new_customer_action = QAction("عميل جديد / New Customer", self)
        new_customer_action.triggered.connect(lambda: self.switch_to_module('customers'))
        new_menu.addAction(new_customer_action)
        
        new_truck_action = QAction("شاحنة جديدة / New Truck", self)
        new_truck_action.triggered.connect(lambda: self.switch_to_module('trucks'))
        new_menu.addAction(new_truck_action)
        
        new_sale_action = QAction("فاتورة مبيعات / New Sale", self)
        new_sale_action.triggered.connect(lambda: self.switch_to_module('sales'))
        new_menu.addAction(new_sale_action)
        
        file_menu.addSeparator()
        
        # Import/Export
        import_action = QAction("استيراد / Import", self)
        import_action.triggered.connect(self.import_data)
        file_menu.addAction(import_action)
        
        export_action = QAction("تصدير / Export", self)
        export_action.triggered.connect(self.export_data)
        file_menu.addAction(export_action)
        
        file_menu.addSeparator()
        
        # Logout and Exit
        logout_action = QAction("تسجيل الخروج / Logout", self)
        logout_action.triggered.connect(self.logout)
        file_menu.addAction(logout_action)
        
        exit_action = QAction("خروج / Exit", self)
        exit_action.triggered.connect(self.close)
        file_menu.addAction(exit_action)
        
        # Tools menu
        tools_menu = menubar.addMenu("أدوات / Tools")
        
        backup_action = QAction("نسخة احتياطية / Backup", self)
        backup_action.triggered.connect(lambda: self.switch_to_module('backup'))
        tools_menu.addAction(backup_action)
        
        settings_action = QAction("الإعدادات / Settings", self)
        settings_action.triggered.connect(lambda: self.switch_to_module('users'))
        tools_menu.addAction(settings_action)
        
        tools_menu.addSeparator()
        
        calculator_action = QAction("حاسبة / Calculator", self)
        calculator_action.triggered.connect(self.open_calculator)
        tools_menu.addAction(calculator_action)
        
        # Reports menu
        reports_menu = menubar.addMenu("تقارير / Reports")
        
        financial_reports_action = QAction("التقارير المالية / Financial Reports", self)
        financial_reports_action.triggered.connect(lambda: self.switch_to_module('advanced_reports'))
        reports_menu.addAction(financial_reports_action)
        
        production_reports_action = QAction("تقارير الإنتاج / Production Reports", self)
        production_reports_action.triggered.connect(lambda: self.switch_to_module('advanced_reports'))
        reports_menu.addAction(production_reports_action)
        
        inventory_reports_action = QAction("تقارير المخزون / Inventory Reports", self)
        inventory_reports_action.triggered.connect(lambda: self.switch_to_module('reports'))
        reports_menu.addAction(inventory_reports_action)
        
        # Help menu
        help_menu = menubar.addMenu("مساعدة / Help")
        
        user_manual_action = QAction("دليل المستخدم / User Manual", self)
        user_manual_action.triggered.connect(self.show_user_manual)
        help_menu.addAction(user_manual_action)
        
        shortcuts_action = QAction("اختصارات لوحة المفاتيح / Keyboard Shortcuts", self)
        shortcuts_action.triggered.connect(self.show_shortcuts)
        help_menu.addAction(shortcuts_action)
        
        help_menu.addSeparator()
        
        about_action = QAction("حول البرنامج / About", self)
        about_action.triggered.connect(self.show_about)
        help_menu.addAction(about_action)
        
    def create_toolbar(self):
        """Create application toolbar"""
        toolbar = QToolBar()
        toolbar.setToolButtonStyle(Qt.ToolButtonTextUnderIcon)
        self.addToolBar(toolbar)
        
        # Quick access buttons
        dashboard_action = QAction(QIcon('assets/dashboard.png'), "لوحة التحكم\nDashboard", self)
        dashboard_action.triggered.connect(lambda: self.switch_to_module('dashboard'))
        toolbar.addAction(dashboard_action)
        
        toolbar.addSeparator()
        
        customers_action = QAction(QIcon('assets/customers.png'), "العملاء\nCustomers", self)
        customers_action.triggered.connect(lambda: self.switch_to_module('customers'))
        toolbar.addAction(customers_action)
        
        sales_action = QAction(QIcon('assets/sales.png'), "المبيعات\nSales", self)
        sales_action.triggered.connect(lambda: self.switch_to_module('sales'))
        toolbar.addAction(sales_action)
        
        inventory_action = QAction(QIcon('assets/inventory.png'), "المخزون\nInventory", self)
        inventory_action.triggered.connect(lambda: self.switch_to_module('inventory'))
        toolbar.addAction(inventory_action)
        
        toolbar.addSeparator()
        
        production_action = QAction(QIcon('assets/production.png'), "الإنتاج\nProduction", self)
        production_action.triggered.connect(lambda: self.switch_to_module('production'))
        toolbar.addAction(production_action)
        
        quality_action = QAction(QIcon('assets/quality.png'), "الجودة\nQuality", self)
        quality_action.triggered.connect(lambda: self.switch_to_module('quality'))
        toolbar.addAction(quality_action)
        
        toolbar.addSeparator()
        
        reports_action = QAction(QIcon('assets/reports.png'), "التقارير\nReports", self)
        reports_action.triggered.connect(lambda: self.switch_to_module('reports'))
        toolbar.addAction(reports_action)
        
    def create_sidebar(self, parent_layout):
        """Create modern navigation sidebar"""
        sidebar = QFrame()
        sidebar.setObjectName("sidebar")
        sidebar.setFixedWidth(280)
        sidebar.setStyleSheet(f"""
            QFrame#sidebar {{
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 {ModernTheme.COLORS['primary']},
                    stop:1 {ModernTheme.COLORS['primary_dark']});
                border: none;
                border-radius: 0px;
            }}
        """)
        
        sidebar_layout = QVBoxLayout(sidebar)
        sidebar_layout.setSpacing(5)
        sidebar_layout.setContentsMargins(10, 10, 10, 10)
        
        # User info section
        user_info = self.create_user_info_section()
        sidebar_layout.addWidget(user_info)
        
        # Navigation buttons
        nav_buttons = [
            ("لوحة التحكم / Dashboard", "dashboard", "#0078d4"),
            ("العملاء / Customers", "customers", "#28a745"),
            ("الموردين / Suppliers", "suppliers", "#17a2b8"),
            ("الشاحنات / Trucks", "trucks", "#ffc107"),
            ("الكتل / Blocks", "blocks", "#6f42c1"),
            ("المبيعات / Sales", "sales", "#28a745"),
            ("المخزون / Inventory", "inventory", "#fd7e14"),
            ("المصروفات / Expenses", "expenses", "#dc3545"),
            ("تخطيط الإنتاج / Production Planning", "production", "#20c997"),
            ("المشتريات / Procurement", "procurement", "#6610f2"),
            ("مراقبة الجودة / Quality Control", "quality", "#e83e8c"),
            ("الصيانة / Maintenance", "maintenance", "#fd7e14"),
            ("عروض الأسعار / Quotations", "quotations", "#17a2b8"),
            ("التقارير / Reports", "reports", "#6c757d"),
            ("التقارير المتقدمة / Advanced Reports", "advanced_reports", "#495057"),
        ]
        
        # Add admin-only modules
        if self.user_data.get('role') == 'admin':
            nav_buttons.extend([
                ("إدارة المستخدمين / User Management", "users", "#dc3545"),
                ("النسخ الاحتياطي / Backup & Restore", "backup", "#6c757d"),
            ])
        
        self.nav_buttons = {}
        
        for text, module_key, color in nav_buttons:
            btn = QPushButton(text)
            btn.setObjectName("nav-button")
            btn.setStyleSheet(f"""
                QPushButton {{
                    background-color: transparent;
                    color: white;
                    border: none;
                    border-radius: 8px;
                    padding: 16px 20px;
                    text-align: left;
                    font-size: 11pt;
                    font-weight: 500;
                    margin: 4px 8px;
                }}
                QPushButton:hover {{
                    background-color: rgba(255, 255, 255, 0.1);
                    transform: translateX(4px);
                }}
                QPushButton:pressed {{
                    background-color: rgba(255, 255, 255, 0.2);
                }}
                QPushButton.active {{
                    background-color: white;
                    color: {ModernTheme.COLORS['primary']};
                    font-weight: 600;
                }}
            """)
            btn.clicked.connect(lambda checked, key=module_key: self.switch_to_module(key))

            self.nav_buttons[module_key] = btn
            sidebar_layout.addWidget(btn)
        
        sidebar_layout.addStretch()
        
        # Quick stats section
        stats_section = self.create_quick_stats_section()
        sidebar_layout.addWidget(stats_section)
        
        parent_layout.addWidget(sidebar, 1)
        
    def create_user_info_section(self):
        """Create user information section"""
        user_frame = QFrame()
        user_frame.setStyleSheet("""
            QFrame {
                background-color: #404040;
                border-radius: 8px;
                padding: 15px;
            }
        """)
        
        layout = QVBoxLayout(user_frame)
        
        # Welcome message
        welcome_label = QLabel("مرحباً / Welcome")
        welcome_label.setFont(QFont("Arial", 10))
        welcome_label.setStyleSheet("color: #cccccc;")
        layout.addWidget(welcome_label)
        
        # User name
        user_name = QLabel(self.user_data.get('full_name', 'User'))
        user_name.setFont(QFont("Arial", 14, QFont.Bold))
        user_name.setStyleSheet("color: #0078d4;")
        layout.addWidget(user_name)
        
        # User role
        role_label = QLabel(f"الصلاحية / Role: {self.user_data.get('role', 'user').title()}")
        role_label.setFont(QFont("Arial", 9))
        role_label.setStyleSheet("color: #888888;")
        layout.addWidget(role_label)
        
        # Last login
        last_login = self.user_data.get('last_login', 'N/A')
        if last_login and last_login != 'N/A':
            login_label = QLabel(f"آخر دخول / Last Login:\n{last_login}")
            login_label.setFont(QFont("Arial", 8))
            login_label.setStyleSheet("color: #888888;")
            layout.addWidget(login_label)
        
        return user_frame
        
    def create_quick_stats_section(self):
        """Create quick statistics section"""
        stats_frame = QFrame()
        stats_frame.setStyleSheet("""
            QFrame {
                background-color: #404040;
                border-radius: 8px;
                padding: 10px;
            }
        """)
        
        layout = QVBoxLayout(stats_frame)
        
        title_label = QLabel("إحصائيات سريعة / Quick Stats")
        title_label.setFont(QFont("Arial", 11, QFont.Bold))
        title_label.setStyleSheet("color: #ffffff;")
        layout.addWidget(title_label)
        
        # Stats will be populated by timer
        self.stats_labels = {}
        
        stats_data = [
            ("مبيعات اليوم / Today's Sales", "today_sales", "#28a745"),
            ("العملاء / Customers", "customers_count", "#17a2b8"),
            ("المخزون / Inventory Items", "inventory_count", "#ffc107"),
            ("الكتل المتاحة / Available Blocks", "available_blocks", "#6f42c1")
        ]
        
        for title, key, color in stats_data:
            stat_widget = self.create_stat_widget(title, "0", color)
            self.stats_labels[key] = stat_widget.findChild(QLabel, "value_label")
            layout.addWidget(stat_widget)
        
        # Current time
        self.time_label = QLabel()
        self.time_label.setAlignment(Qt.AlignCenter)
        self.time_label.setFont(QFont("Arial", 10))
        self.time_label.setStyleSheet("color: #cccccc; margin-top: 10px;")
        layout.addWidget(self.time_label)
        
        return stats_frame
        
    def create_stat_widget(self, title, value, color):
        """Create a statistics widget"""
        widget = QFrame()
        widget.setStyleSheet(f"""
            QFrame {{
                background-color: {color};
                border-radius: 6px;
                padding: 8px;
                margin: 2px;
            }}
        """)
        
        layout = QVBoxLayout(widget)
        layout.setContentsMargins(8, 6, 8, 6)
        
        title_label = QLabel(title)
        title_label.setFont(QFont("Arial", 8))
        title_label.setStyleSheet("color: white;")
        title_label.setWordWrap(True)
        layout.addWidget(title_label)
        
        value_label = QLabel(value)
        value_label.setObjectName("value_label")
        value_label.setFont(QFont("Arial", 12, QFont.Bold))
        value_label.setStyleSheet("color: white;")
        value_label.setAlignment(Qt.AlignCenter)
        layout.addWidget(value_label)
        
        return widget
        
    def create_status_bar(self):
        """Create status bar"""
        status_bar = QStatusBar()
        self.setStatusBar(status_bar)
        
        # Status message
        self.status_message = QLabel("جاهز / Ready")
        status_bar.addWidget(self.status_message)
        
        # Connection status
        self.connection_status = QLabel("متصل / Connected")
        self.connection_status.setStyleSheet("color: #28a745;")
        status_bar.addPermanentWidget(self.connection_status)
        
        # Current user
        user_status = QLabel(f"المستخدم / User: {self.user_data.get('username', 'Unknown')}")
        status_bar.addPermanentWidget(user_status)
        
    def setup_modules(self):
        """Setup all application modules"""
        self.modules = {}
        
        # Dashboard (home page)
        dashboard_widget = self.create_dashboard_widget()
        self.modules['dashboard'] = dashboard_widget
        self.content_stack.addWidget(dashboard_widget)
        
        # Core modules
        self.modules['customers'] = CustomersModule(self.db_manager, self.user_data)
        self.content_stack.addWidget(self.modules['customers'])
        
        self.modules['trucks'] = TrucksModule(self.db_manager, self.user_data)
        self.content_stack.addWidget(self.modules['trucks'])
        
        self.modules['blocks'] = BlocksModule(self.db_manager, self.user_data)
        self.content_stack.addWidget(self.modules['blocks'])
        
        self.modules['sales'] = SalesModule(self.db_manager, self.user_data)
        self.content_stack.addWidget(self.modules['sales'])
        
        self.modules['inventory'] = InventoryModule(self.db_manager, self.user_data)
        self.content_stack.addWidget(self.modules['inventory'])
        
        self.modules['expenses'] = ExpensesModule(self.db_manager, self.user_data)
        self.content_stack.addWidget(self.modules['expenses'])
        
        self.modules['reports'] = ReportsModule(self.db_manager, self.user_data)
        self.content_stack.addWidget(self.modules['reports'])
        
        # New advanced modules
        self.modules['production'] = ProductionPlanningModule(self.db_manager, self.user_data)
        self.content_stack.addWidget(self.modules['production'])
        
        self.modules['procurement'] = MaterialProcurementModule(self.db_manager, self.user_data)
        self.content_stack.addWidget(self.modules['procurement'])
        
        self.modules['quality'] = QualityControlModule(self.db_manager, self.user_data)
        self.content_stack.addWidget(self.modules['quality'])
        
        self.modules['maintenance'] = MaintenanceModule(self.db_manager, self.user_data)
        self.content_stack.addWidget(self.modules['maintenance'])
        
        self.modules['quotations'] = SalesQuotationModule(self.db_manager, self.user_data)
        self.content_stack.addWidget(self.modules['quotations'])
        
        self.modules['advanced_reports'] = AdvancedReportsModule(self.db_manager, self.user_data)
        self.content_stack.addWidget(self.modules['advanced_reports'])
        
        # Admin-only modules
        if self.user_data.get('role') == 'admin':
            self.modules['users'] = UserManagementModule(self.db_manager, self.user_data)
            self.content_stack.addWidget(self.modules['users'])
            
            self.modules['backup'] = BackupRestoreModule(self.db_manager, self.user_data)
            self.content_stack.addWidget(self.modules['backup'])
        
        # Set dashboard as default
        self.switch_to_module('dashboard')
        
    def create_dashboard_widget(self):
        """Create modern dashboard widget with beautiful cards"""
        widget = QWidget()
        layout = QVBoxLayout(widget)
        layout.setSpacing(20)
        layout.setContentsMargins(20, 20, 20, 20)

        # Welcome section with modern styling
        welcome_frame = QFrame()
        welcome_frame.setObjectName("welcomeCard")
        welcome_frame.setStyleSheet(f"""
            QFrame#welcomeCard {{
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 {ModernTheme.COLORS['gradient_start']},
                    stop:1 {ModernTheme.COLORS['gradient_end']});
                border: none;
                border-radius: 16px;
                padding: 30px;
                margin: 10px;
            }}
        """)

        welcome_layout = QVBoxLayout(welcome_frame)
        welcome_layout.setSpacing(12)

        title = QLabel("🏭 مرحباً بك في نظام إدارة مصنع الحسن ستون")
        title.setFont(QFont("Arial", 24, QFont.Bold))
        title.setStyleSheet("color: white; margin-bottom: 8px;")
        title.setAlignment(Qt.AlignCenter)
        welcome_layout.addWidget(title)

        subtitle = QLabel("Welcome to Al-Hassan Stone Factory Management System")
        subtitle.setFont(QFont("Arial", 16))
        subtitle.setStyleSheet("color: rgba(255, 255, 255, 0.9);")
        subtitle.setAlignment(Qt.AlignCenter)
        welcome_layout.addWidget(subtitle)

        description = QLabel("نظام شامل لإدارة جميع عمليات مصانع الأحجار الطبيعية والتحكم في الإنتاج والمبيعات")
        description.setFont(QFont("Arial", 12))
        description.setStyleSheet("color: rgba(255, 255, 255, 0.8); margin-top: 8px;")
        description.setAlignment(Qt.AlignCenter)
        description.setWordWrap(True)
        welcome_layout.addWidget(description)

        layout.addWidget(welcome_frame)

        # Statistics cards section
        stats_frame = QFrame()
        stats_layout = QHBoxLayout(stats_frame)
        stats_layout.setSpacing(20)

        # Create statistics cards
        stats_data = [
            ("العملاء", "150", "#3498db"),
            ("الشاحنات", "45", "#2ecc71"),
            ("المبيعات اليوم", "25", "#e74c3c"),
            ("المخزون", "320", "#f39c12")
        ]

        for label, number, color in stats_data:
            stats_card = StatsCard(number, label, color)
            stats_layout.addWidget(stats_card)

        layout.addWidget(stats_frame)
        
        # Quick actions section with modern cards
        actions_title = QLabel("🚀 الإجراءات السريعة | Quick Actions")
        actions_title.setFont(QFont("Arial", 18, QFont.Bold))
        actions_title.setStyleSheet(f"color: {ModernTheme.COLORS['text_primary']}; margin: 20px 0 10px 0;")
        layout.addWidget(actions_title)

        actions_frame = QFrame()
        actions_layout = QGridLayout(actions_frame)
        actions_layout.setSpacing(20)

        # Quick action cards
        quick_actions = [
            ("إضافة عميل جديد", "Add New Customer", "customers"),
            ("تسجيل شاحنة جديدة", "Register New Truck", "trucks"),
            ("إنشاء فاتورة مبيعات", "Create Sales Invoice", "sales"),
            ("عرض المخزون", "View Inventory", "inventory"),
            ("تخطيط الإنتاج", "Production Planning", "production"),
            ("مراقبة الجودة", "Quality Control", "quality"),
            ("إدارة الصيانة", "Maintenance Management", "maintenance"),
            ("التقارير المتقدمة", "Advanced Reports", "advanced_reports")
        ]

        for i, (title_ar, title_en, module) in enumerate(quick_actions):
            # Create modern card for each action
            card = ModernCard(title_ar, title_en)
            card.clicked.connect(lambda m=module: self.switch_to_module(m))

            row = i // 4
            col = i % 4
            actions_layout.addWidget(card, row, col)

        layout.addWidget(actions_frame)
        
        # Recent activity section
        activity_frame = QGroupBox("النشاط الأخير / Recent Activity")
        activity_layout = QVBoxLayout(activity_frame)
        
        self.activity_list = QLabel("جاري تحميل النشاط الأخير...\nLoading recent activity...")
        self.activity_list.setStyleSheet("color: #cccccc; padding: 20px;")
        activity_layout.addWidget(self.activity_list)
        
        layout.addWidget(activity_frame)
        
        layout.addStretch()
        
        return widget
        
    def setup_timer(self):
        """Setup timer for updating dashboard"""
        self.timer = QTimer()
        self.timer.timeout.connect(self.update_dashboard)
        self.timer.start(30000)  # Update every 30 seconds
        self.update_dashboard()
        
    def update_dashboard(self):
        """Update dashboard data"""
        # Update time
        current_time = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        self.time_label.setText(current_time)
        
        # Update quick stats
        self.update_quick_stats()
        
        # Update recent activity
        self.update_recent_activity()
        
    def update_quick_stats(self):
        """Update quick statistics"""
        try:
            conn = self.db_manager.get_connection()
            cursor = conn.cursor()
            
            # Today's sales
            cursor.execute("""
                SELECT COALESCE(SUM(final_amount), 0) as today_sales
                FROM sales
                WHERE DATE(sale_date) = DATE('now')
            """)
            today_sales = cursor.fetchone()['today_sales']
            if 'today_sales' in self.stats_labels:
                self.stats_labels['today_sales'].setText(f"{today_sales:.0f}")
            
            # Customers count
            cursor.execute("SELECT COUNT(*) as count FROM customers")
            customers_count = cursor.fetchone()['count']
            if 'customers_count' in self.stats_labels:
                self.stats_labels['customers_count'].setText(str(customers_count))
            
            # Inventory count
            cursor.execute("SELECT COUNT(*) as count FROM slabs WHERE status = 'in_stock'")
            inventory_count = cursor.fetchone()['count']
            if 'inventory_count' in self.stats_labels:
                self.stats_labels['inventory_count'].setText(str(inventory_count))
            
            # Available blocks
            cursor.execute("SELECT COUNT(*) as count FROM blocks WHERE status = 'available'")
            blocks_count = cursor.fetchone()['count']
            if 'available_blocks' in self.stats_labels:
                self.stats_labels['available_blocks'].setText(str(blocks_count))
            
            conn.close()
            
        except Exception as e:
            print(f"Error updating stats: {e}")
            
    def update_recent_activity(self):
        """Update recent activity display"""
        try:
            conn = self.db_manager.get_connection()
            cursor = conn.cursor()
            
            cursor.execute("""
                SELECT al.action, u.full_name, al.timestamp
                FROM activity_logs al
                LEFT JOIN users u ON al.user_id = u.id
                ORDER BY al.timestamp DESC
                LIMIT 5
            """)
            
            activities = cursor.fetchall()
            
            if activities:
                activity_text = ""
                for activity in activities:
                    user_name = activity['full_name'] or 'System'
                    action = activity['action']
                    timestamp = activity['timestamp']
                    activity_text += f"• {user_name}: {action} ({timestamp})\n"
                
                self.activity_list.setText(activity_text)
            else:
                self.activity_list.setText("لا يوجد نشاط حديث\nNo recent activity")
                
            conn.close()
            
        except Exception as e:
            print(f"Error updating activity: {e}")
            
    def load_dashboard_data(self):
        """Load initial dashboard data"""
        self.update_dashboard()
        
    def switch_to_module(self, module_key):
        """Switch to specified module"""
        if module_key in self.modules:
            self.content_stack.setCurrentWidget(self.modules[module_key])
            
            # Update navigation button states
            for key, btn in self.nav_buttons.items():
                if key == module_key:
                    btn.setStyleSheet(btn.styleSheet() + "QPushButton { border: 2px solid #ffffff; }")
                else:
                    # Remove border by resetting stylesheet
                    original_style = btn.styleSheet().replace("QPushButton { border: 2px solid #ffffff; }", "")
                    btn.setStyleSheet(original_style)
            
            # Update status
            module_names = {
                'dashboard': 'لوحة التحكم / Dashboard',
                'customers': 'العملاء / Customers',
                'trucks': 'الشاحنات / Trucks',
                'blocks': 'الكتل / Blocks',
                'sales': 'المبيعات / Sales',
                'inventory': 'المخزون / Inventory',
                'expenses': 'المصروفات / Expenses',
                'reports': 'التقارير / Reports',
                'production': 'تخطيط الإنتاج / Production Planning',
                'procurement': 'المشتريات / Procurement',
                'quality': 'مراقبة الجودة / Quality Control',
                'maintenance': 'الصيانة / Maintenance',
                'quotations': 'عروض الأسعار / Quotations',
                'advanced_reports': 'التقارير المتقدمة / Advanced Reports',
                'users': 'إدارة المستخدمين / User Management',
                'backup': 'النسخ الاحتياطي / Backup & Restore'
            }
            
            module_name = module_names.get(module_key, module_key)
            self.status_message.setText(f"عرض: {module_name} / Viewing: {module_name}")
            
    def darken_color(self, color, factor=0.2):
        """Darken a hex color"""
        # Simple color darkening - in production, use proper color manipulation
        color_map = {
            '#0078d4': '#005a9e',
            '#28a745': '#1e7e34',
            '#17a2b8': '#138496',
            '#ffc107': '#e0a800',
            '#6f42c1': '#59359a',
            '#dc3545': '#c82333',
            '#fd7e14': '#e8650e',
            '#20c997': '#1aa179',
            '#6610f2': '#520dc2',
            '#e83e8c': '#d91a72',
            '#6c757d': '#545b62',
            '#495057': '#383d41'
        }
        return color_map.get(color, color)
        
    def apply_stylesheet(self):
        """Apply application stylesheet"""
        style = """
        QMainWindow {
            background-color: #2b2b2b;
            color: #ffffff;
        }
        
        QWidget {
            background-color: #2b2b2b;
            color: #ffffff;
            font-family: 'Segoe UI', Arial, sans-serif;
            font-size: 10pt;
        }
        
        QMenuBar {
            background-color: #404040;
            border-bottom: 1px solid #555555;
            padding: 4px;
        }
        
        QMenuBar::item {
            background-color: transparent;
            padding: 8px 12px;
            border-radius: 4px;
        }
        
        QMenuBar::item:selected {
            background-color: #0078d4;
        }
        
        QMenu {
            background-color: #404040;
            border: 1px solid #555555;
            border-radius: 4px;
        }
        
        QMenu::item {
            padding: 8px 20px;
            border-radius: 4px;
        }
        
        QMenu::item:selected {
            background-color: #0078d4;
        }
        
        QToolBar {
            background-color: #353535;
            border-bottom: 1px solid #555555;
            spacing: 3px;
            padding: 4px;
        }
        
        QToolBar QToolButton {
            background-color: transparent;
            border: none;
            padding: 8px;
            border-radius: 4px;
            color: white;
        }
        
        QToolBar QToolButton:hover {
            background-color: #0078d4;
        }
        
        QStatusBar {
            background-color: #353535;
            border-top: 1px solid #555555;
            color: #cccccc;
        }
        
        QGroupBox {
            font-weight: bold;
            border: 2px solid #555555;
            border-radius: 8px;
            margin-top: 1ex;
            padding-top: 10px;
        }
        
        QGroupBox::title {
            subcontrol-origin: margin;
            left: 10px;
            padding: 0 5px 0 5px;
            color: #0078d4;
        }
        """
        
        self.setStyleSheet(style)
        
    def logout(self):
        """Handle user logout"""
        reply = QMessageBox.question(
            self, "تسجيل الخروج / Logout",
            "هل تريد تسجيل الخروج من النظام؟\nDo you want to logout from the system?",
            QMessageBox.Yes | QMessageBox.No,
            QMessageBox.No
        )
        
        if reply == QMessageBox.Yes:
            # Log logout activity
            self.db_manager.log_activity(
                self.user_data['id'], 
                f"User logged out: {self.user_data['username']}"
            )
            
            self.logout_requested.emit()
            self.close()
            
    def closeEvent(self, event):
        """Handle window close event"""
        reply = QMessageBox.question(
            self, "إغلاق البرنامج / Close Application",
            "هل تريد إغلاق البرنامج؟\nDo you want to close the application?",
            QMessageBox.Yes | QMessageBox.No,
            QMessageBox.No
        )
        
        if reply == QMessageBox.Yes:
            # Log application close
            self.db_manager.log_activity(
                self.user_data['id'], 
                "Application closed"
            )
            event.accept()
        else:
            event.ignore()
            
    # Menu action handlers
    def import_data(self):
        """Import data from file"""
        QMessageBox.information(self, "استيراد / Import", 
                              "وظيفة الاستيراد ستتوفر قريباً\nImport feature coming soon")
        
    def export_data(self):
        """Export data to file"""
        QMessageBox.information(self, "تصدير / Export", 
                              "وظيفة التصدير ستتوفر قريباً\nExport feature coming soon")
        
    def open_calculator(self):
        """Open calculator"""
        import subprocess
        try:
            subprocess.Popen('calc.exe')
        except:
            QMessageBox.information(self, "حاسبة / Calculator", 
                                  "لا يمكن فتح الحاسبة\nCannot open calculator")
            
    def show_user_manual(self):
        """Show user manual"""
        QMessageBox.information(self, "دليل المستخدم / User Manual", 
                              "دليل المستخدم سيتوفر قريباً\nUser manual coming soon")
        
    def show_shortcuts(self):
        """Show keyboard shortcuts"""
        shortcuts_text = """
اختصارات لوحة المفاتيح / Keyboard Shortcuts:

Ctrl+N - عميل جديد / New Customer
Ctrl+S - حفظ / Save
Ctrl+P - طباعة / Print
Ctrl+F - بحث / Find
Ctrl+Q - خروج / Quit
F1 - مساعدة / Help
F5 - تحديث / Refresh
Alt+F4 - إغلاق / Close
        """
        
        QMessageBox.information(self, "اختصارات لوحة المفاتيح / Keyboard Shortcuts", shortcuts_text)
        
    def show_about(self):
        """Show about dialog"""
        about_text = """
نظام إدارة مصنع الحسن ستون
Al-Hassan Stone Factory Management System

الإصدار / Version: 2.0.0
تاريخ الإصدار / Release Date: 2024

نظام شامل لإدارة جميع عمليات مصانع الأحجار الطبيعية
Comprehensive management system for natural stone factories

الميزات الجديدة / New Features:
• تخطيط الإنتاج المتقدم / Advanced Production Planning
• إدارة المشتريات والمواد / Material Procurement Management
• مراقبة الجودة الشاملة / Comprehensive Quality Control
• إدارة الصيانة الوقائية / Preventive Maintenance Management
• عروض الأسعار المتقدمة / Advanced Sales Quotations
• التقارير والتحليلات المتقدمة / Advanced Reports & Analytics
• إدارة المستخدمين والصلاحيات / User Management & Permissions
• النسخ الاحتياطي التلقائي / Automatic Backup System
• دعم متعدد اللغات والثقافات / Multi-language & Cultural Support

© 2024 Al-Hassan Stone Factory. All rights reserved.
        """
        
        QMessageBox.about(self, "حول البرنامج / About", about_text)
