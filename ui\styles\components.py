#!/usr/bin/env python3
"""
Advanced UI Components with Modern Styling
مكونات واجهة المستخدم المتقدمة مع التنسيق الحديث
"""

from PyQt5.QtWidgets import (QFrame, QLabel, QPushButton, QVBoxLayout, 
                            QHBoxLayout, QWidget, QGraphicsDropShadowEffect)
from PyQt5.QtCore import Qt, pyqtSignal, QPropertyAnimation, QEasingCurve, QRect
from PyQt5.QtGui import QFont, QColor, QPainter, QPen, QBrush

class ModernCard(QFrame):
    """Modern card component with shadow and hover effects"""
    
    clicked = pyqtSignal()
    
    def __init__(self, title="", subtitle="", parent=None):
        super().__init__(parent)
        self.setup_ui(title, subtitle)
        self.setup_effects()
        
    def setup_ui(self, title, subtitle):
        """Setup card UI"""
        self.setObjectName("modernCard")
        self.setFixedSize(280, 160)
        self.setCursor(Qt.PointingHandCursor)
        
        # Main layout
        layout = QVBoxLayout()
        layout.setContentsMargins(20, 20, 20, 20)
        layout.setSpacing(12)
        
        # Title
        if title:
            self.title_label = QLabel(title)
            self.title_label.setFont(QFont("Arial", 14, QFont.Bold))
            self.title_label.setStyleSheet("color: #2c3e50; margin-bottom: 8px;")
            layout.addWidget(self.title_label)
        
        # Subtitle
        if subtitle:
            self.subtitle_label = QLabel(subtitle)
            self.subtitle_label.setFont(QFont("Arial", 10))
            self.subtitle_label.setStyleSheet("color: #7f8c8d;")
            self.subtitle_label.setWordWrap(True)
            layout.addWidget(self.subtitle_label)
        
        layout.addStretch()
        self.setLayout(layout)
        
        # Styling
        self.setStyleSheet("""
            QFrame#modernCard {
                background-color: white;
                border: 1px solid #e1e8ed;
                border-radius: 12px;
                padding: 16px;
            }
            QFrame#modernCard:hover {
                border-color: #3498db;
                background-color: #f8f9fa;
            }
        """)
    
    def setup_effects(self):
        """Setup shadow and animation effects"""
        # Drop shadow
        self.shadow = QGraphicsDropShadowEffect()
        self.shadow.setBlurRadius(15)
        self.shadow.setColor(QColor(0, 0, 0, 30))
        self.shadow.setOffset(0, 4)
        self.setGraphicsEffect(self.shadow)
        
        # Animation
        self.animation = QPropertyAnimation(self, b"geometry")
        self.animation.setDuration(200)
        self.animation.setEasingCurve(QEasingCurve.OutCubic)
    
    def mousePressEvent(self, event):
        """Handle mouse press"""
        if event.button() == Qt.LeftButton:
            self.clicked.emit()
        super().mousePressEvent(event)
    
    def enterEvent(self, event):
        """Handle mouse enter"""
        self.shadow.setBlurRadius(20)
        self.shadow.setOffset(0, 6)
        super().enterEvent(event)
    
    def leaveEvent(self, event):
        """Handle mouse leave"""
        self.shadow.setBlurRadius(15)
        self.shadow.setOffset(0, 4)
        super().leaveEvent(event)

class StatsCard(QFrame):
    """Statistics card with number and label"""
    
    def __init__(self, number="0", label="", color="#3498db", parent=None):
        super().__init__(parent)
        self.setup_ui(number, label, color)
        
    def setup_ui(self, number, label, color):
        """Setup stats card UI"""
        self.setFixedSize(200, 120)
        
        # Main layout
        layout = QVBoxLayout()
        layout.setContentsMargins(20, 20, 20, 20)
        layout.setSpacing(8)
        
        # Number
        self.number_label = QLabel(number)
        self.number_label.setFont(QFont("Arial", 24, QFont.Bold))
        self.number_label.setAlignment(Qt.AlignCenter)
        self.number_label.setStyleSheet(f"color: {color};")
        layout.addWidget(self.number_label)
        
        # Label
        self.text_label = QLabel(label)
        self.text_label.setFont(QFont("Arial", 10))
        self.text_label.setAlignment(Qt.AlignCenter)
        self.text_label.setStyleSheet("color: #7f8c8d;")
        self.text_label.setWordWrap(True)
        layout.addWidget(self.text_label)
        
        self.setLayout(layout)
        
        # Styling
        self.setStyleSheet(f"""
            QFrame {{
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 {color}, stop:1 {self.darken_color(color)});
                border: none;
                border-radius: 12px;
            }}
        """)
        
        # Shadow effect
        shadow = QGraphicsDropShadowEffect()
        shadow.setBlurRadius(10)
        shadow.setColor(QColor(0, 0, 0, 40))
        shadow.setOffset(0, 3)
        self.setGraphicsEffect(shadow)
    
    def darken_color(self, color):
        """Darken a color for gradient effect"""
        color_map = {
            "#3498db": "#2980b9",
            "#e74c3c": "#c0392b",
            "#2ecc71": "#27ae60",
            "#f39c12": "#e67e22",
            "#9b59b6": "#8e44ad",
            "#1abc9c": "#16a085"
        }
        return color_map.get(color, "#2c3e50")
    
    def update_number(self, number):
        """Update the number displayed"""
        self.number_label.setText(str(number))

class ModernButton(QPushButton):
    """Modern button with enhanced styling"""
    
    def __init__(self, text="", button_type="primary", parent=None):
        super().__init__(text, parent)
        self.button_type = button_type
        self.setup_styling()
        
    def setup_styling(self):
        """Setup button styling based on type"""
        base_style = """
            QPushButton {
                border: none;
                border-radius: 8px;
                padding: 12px 24px;
                font-weight: 600;
                font-size: 10pt;
                min-height: 20px;
            }
        """
        
        type_styles = {
            "primary": """
                QPushButton {
                    background-color: #3498db;
                    color: white;
                }
                QPushButton:hover {
                    background-color: #2980b9;
                }
                QPushButton:pressed {
                    background-color: #21618c;
                }
            """,
            "success": """
                QPushButton {
                    background-color: #2ecc71;
                    color: white;
                }
                QPushButton:hover {
                    background-color: #27ae60;
                }
                QPushButton:pressed {
                    background-color: #1e8449;
                }
            """,
            "warning": """
                QPushButton {
                    background-color: #f39c12;
                    color: white;
                }
                QPushButton:hover {
                    background-color: #e67e22;
                }
                QPushButton:pressed {
                    background-color: #d35400;
                }
            """,
            "danger": """
                QPushButton {
                    background-color: #e74c3c;
                    color: white;
                }
                QPushButton:hover {
                    background-color: #c0392b;
                }
                QPushButton:pressed {
                    background-color: #a93226;
                }
            """,
            "outline": """
                QPushButton {
                    background-color: transparent;
                    color: #3498db;
                    border: 2px solid #3498db;
                }
                QPushButton:hover {
                    background-color: #3498db;
                    color: white;
                }
                QPushButton:pressed {
                    background-color: #2980b9;
                }
            """
        }
        
        self.setStyleSheet(base_style + type_styles.get(self.button_type, type_styles["primary"]))

class ModernProgressBar(QWidget):
    """Modern progress bar with animation"""
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.value = 0
        self.maximum = 100
        self.setFixedHeight(8)
        self.setMinimumWidth(200)
        
    def setValue(self, value):
        """Set progress value"""
        self.value = max(0, min(value, self.maximum))
        self.update()
        
    def setMaximum(self, maximum):
        """Set maximum value"""
        self.maximum = maximum
        self.update()
        
    def paintEvent(self, event):
        """Paint the progress bar"""
        painter = QPainter(self)
        painter.setRenderHint(QPainter.Antialiasing)
        
        # Background
        painter.setPen(Qt.NoPen)
        painter.setBrush(QBrush(QColor("#ecf0f1")))
        painter.drawRoundedRect(self.rect(), 4, 4)
        
        # Progress
        if self.value > 0:
            progress_width = int((self.value / self.maximum) * self.width())
            progress_rect = QRect(0, 0, progress_width, self.height())
            
            gradient = QColor("#3498db")
            painter.setBrush(QBrush(gradient))
            painter.drawRoundedRect(progress_rect, 4, 4)
