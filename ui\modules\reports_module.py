from PyQt5.QtWidgets import (QWidget, QVBoxLayout, QHBoxLayout, QPushButton, 
                            QTableWidget, QTableWidgetItem, QLineEdit, QLabel,
                            QFormLayout, QDialog, QMessageBox, QComboBox,
                            QGroupBox, QDateEdit, QTextEdit, QHeaderView,
                            QSpinBox, QDoubleSpinBox, QTabWidget, QProgressBar,
                            QSplitter, QListWidget, QListWidgetItem, QCheckBox)
from PyQt5.QtCore import Qt, QDate, QThread, pyqtSignal
from PyQt5.QtGui import QFont, QPixmap
import sqlite3
from datetime import datetime, timedelta
import json

class ReportGeneratorThread(QThread):
    progress_updated = pyqtSignal(int)
    report_generated = pyqtSignal(dict)
    error_occurred = pyqtSignal(str)
    
    def __init__(self, db_manager, report_type, parameters):
        super().__init__()
        self.db_manager = db_manager
        self.report_type = report_type
        self.parameters = parameters
        
    def run(self):
        try:
            if self.report_type == "profit_loss":
                result = self.generate_profit_loss_report()
            elif self.report_type == "production_efficiency":
                result = self.generate_production_efficiency_report()
            elif self.report_type == "stock_levels":
                result = self.generate_stock_levels_report()
            elif self.report_type == "quality_metrics":
                result = self.generate_quality_metrics_report()
            elif self.report_type == "custom":
                result = self.generate_custom_report()
            else:
                result = {"error": "نوع التقرير غير مدعوم"}
                
            self.report_generated.emit(result)
            
        except Exception as e:
            self.error_occurred.emit(str(e))
            
    def generate_profit_loss_report(self):
        conn = self.db_manager.get_connection()
        cursor = conn.cursor()
        
        start_date = self.parameters.get('start_date')
        end_date = self.parameters.get('end_date')
        
        self.progress_updated.emit(20)
        
        # Revenue
        cursor.execute("""
            SELECT SUM(total) FROM sales 
            WHERE sale_date BETWEEN ? AND ?
        """, (start_date, end_date))
        revenue = cursor.fetchone()[0] or 0
        
        self.progress_updated.emit(40)
        
        # Cost of Goods Sold
        cursor.execute("""
            SELECT SUM(si.quantity * b.purchase_price)
            FROM sale_items si
            JOIN sales s ON si.sale_id = s.id
            JOIN blocks b ON si.block_id = b.id
            WHERE s.sale_date BETWEEN ? AND ?
        """, (start_date, end_date))
        cogs = cursor.fetchone()[0] or 0
        
        self.progress_updated.emit(60)
        
        # Operating Expenses
        cursor.execute("""
            SELECT SUM(amount) FROM expenses 
            WHERE expense_date BETWEEN ? AND ?
        """, (start_date, end_date))
        expenses = cursor.fetchone()[0] or 0
        
        self.progress_updated.emit(80)
        
        gross_profit = revenue - cogs
        net_profit = gross_profit - expenses
        
        # Monthly breakdown
        cursor.execute("""
            SELECT strftime('%Y-%m', sale_date) as month,
                   SUM(total) as monthly_revenue
            FROM sales 
            WHERE sale_date BETWEEN ? AND ?
            GROUP BY strftime('%Y-%m', sale_date)
            ORDER BY month
        """, (start_date, end_date))
        monthly_revenue = cursor.fetchall()
        
        cursor.execute("""
            SELECT strftime('%Y-%m', expense_date) as month,
                   SUM(amount) as monthly_expenses
            FROM expenses 
            WHERE expense_date BETWEEN ? AND ?
            GROUP BY strftime('%Y-%m', expense_date)
            ORDER BY month
        """, (start_date, end_date))
        monthly_expenses = cursor.fetchall()
        
        self.progress_updated.emit(100)
        
        conn.close()
        
        return {
            'type': 'profit_loss',
            'period': f"{start_date} إلى {end_date}",
            'revenue': revenue,
            'cogs': cogs,
            'gross_profit': gross_profit,
            'expenses': expenses,
            'net_profit': net_profit,
            'monthly_revenue': monthly_revenue,
            'monthly_expenses': monthly_expenses
        }
        
    def generate_production_efficiency_report(self):
        conn = self.db_manager.get_connection()
        cursor = conn.cursor()
        
        start_date = self.parameters.get('start_date')
        end_date = self.parameters.get('end_date')
        
        self.progress_updated.emit(25)
        
        # Production orders completed
        cursor.execute("""
            SELECT COUNT(*) FROM production_orders 
            WHERE status = 'مكتمل' AND completion_date BETWEEN ? AND ?
        """, (start_date, end_date))
        completed_orders = cursor.fetchone()[0] or 0
        
        self.progress_updated.emit(50)
        
        # Average completion time
        cursor.execute("""
            SELECT AVG(julianday(completion_date) - julianday(start_date)) as avg_days
            FROM production_orders 
            WHERE status = 'مكتمل' AND completion_date BETWEEN ? AND ?
        """, (start_date, end_date))
        avg_completion_time = cursor.fetchone()[0] or 0
        
        self.progress_updated.emit(75)
        
        # Quality metrics
        cursor.execute("""
            SELECT 
                COUNT(*) as total_inspections,
                SUM(CASE WHEN result = 'مقبول' THEN 1 ELSE 0 END) as passed_inspections
            FROM quality_inspections 
            WHERE inspection_date BETWEEN ? AND ?
        """, (start_date, end_date))
        quality_data = cursor.fetchone()
        total_inspections = quality_data[0] or 0
        passed_inspections = quality_data[1] or 0
        quality_rate = (passed_inspections / total_inspections * 100) if total_inspections > 0 else 0
        
        self.progress_updated.emit(100)
        
        conn.close()
        
        return {
            'type': 'production_efficiency',
            'period': f"{start_date} إلى {end_date}",
            'completed_orders': completed_orders,
            'avg_completion_time': avg_completion_time,
            'quality_rate': quality_rate,
            'total_inspections': total_inspections,
            'passed_inspections': passed_inspections
        }
        
    def generate_stock_levels_report(self):
        conn = self.db_manager.get_connection()
        cursor = conn.cursor()
        
        self.progress_updated.emit(33)
        
        # Current stock levels
        cursor.execute("""
            SELECT stone_type, status, COUNT(*) as count, SUM(selling_price) as value
            FROM blocks 
            GROUP BY stone_type, status
            ORDER BY stone_type, status
        """)
        stock_by_type = cursor.fetchall()
        
        self.progress_updated.emit(66)
        
        # Low stock alerts
        cursor.execute("""
            SELECT name, current_quantity, min_quantity
            FROM inventory_items
            WHERE current_quantity <= min_quantity
            ORDER BY current_quantity
        """)
        low_stock_items = cursor.fetchall()
        
        self.progress_updated.emit(100)
        
        conn.close()
        
        return {
            'type': 'stock_levels',
            'stock_by_type': stock_by_type,
            'low_stock_items': low_stock_items
        }
        
    def generate_quality_metrics_report(self):
        conn = self.db_manager.get_connection()
        cursor = conn.cursor()
        
        start_date = self.parameters.get('start_date')
        end_date = self.parameters.get('end_date')
        
        self.progress_updated.emit(25)
        
        # Quality inspections summary
        cursor.execute("""
            SELECT result, COUNT(*) as count
            FROM quality_inspections 
            WHERE inspection_date BETWEEN ? AND ?
            GROUP BY result
        """, (start_date, end_date))
        inspection_results = cursor.fetchall()
        
        self.progress_updated.emit(50)
        
        # Defect analysis
        cursor.execute("""
            SELECT defect_type, COUNT(*) as count
            FROM quality_inspections 
            WHERE inspection_date BETWEEN ? AND ? AND result = 'مرفوض'
            GROUP BY defect_type
            ORDER BY count DESC
        """, (start_date, end_date))
        defect_analysis = cursor.fetchall()
        
        self.progress_updated.emit(75)
        
        # Quality trends by month
        cursor.execute("""
            SELECT strftime('%Y-%m', inspection_date) as month,
                   COUNT(*) as total,
                   SUM(CASE WHEN result = 'مقبول' THEN 1 ELSE 0 END) as passed
            FROM quality_inspections 
            WHERE inspection_date BETWEEN ? AND ?
            GROUP BY strftime('%Y-%m', inspection_date)
            ORDER BY month
        """, (start_date, end_date))
        quality_trends = cursor.fetchall()
        
        self.progress_updated.emit(100)
        
        conn.close()
        
        return {
            'type': 'quality_metrics',
            'period': f"{start_date} إلى {end_date}",
            'inspection_results': inspection_results,
            'defect_analysis': defect_analysis,
            'quality_trends': quality_trends
        }
        
    def generate_custom_report(self):
        conn = self.db_manager.get_connection()
        cursor = conn.cursor()
        
        query = self.parameters.get('query', '')
        
        self.progress_updated.emit(50)
        
        cursor.execute(query)
        results = cursor.fetchall()
        columns = [description[0] for description in cursor.description]
        
        self.progress_updated.emit(100)
        
        conn.close()
        
        return {
            'type': 'custom',
            'query': query,
            'columns': columns,
            'results': results
        }

class CustomReportDialog(QDialog):
    def __init__(self, parent=None, translator=None):
        super().__init__(parent)
        self.translator = translator
        self.init_ui()
        
    def init_ui(self):
        self.setWindowTitle("تقرير مخصص")
        self.setModal(True)
        self.resize(600, 500)
        
        layout = QVBoxLayout()
        
        # Report name
        name_layout = QHBoxLayout()
        name_layout.addWidget(QLabel("اسم التقرير:"))
        self.name_edit = QLineEdit()
        name_layout.addWidget(self.name_edit)
        layout.addLayout(name_layout)
        
        # SQL Query
        layout.addWidget(QLabel("استعلام SQL:"))
        self.query_edit = QTextEdit()
        self.query_edit.setPlainText("""
-- مثال: تقرير المبيعات حسب نوع الحجر
SELECT 
    b.stone_type as 'نوع الحجر',
    COUNT(*) as 'عدد المبيعات',
    SUM(si.total) as 'إجمالي المبيعات'
FROM sale_items si
JOIN blocks b ON si.block_id = b.id
JOIN sales s ON si.sale_id = s.id
WHERE s.sale_date >= date('now', '-30 days')
GROUP BY b.stone_type
ORDER BY SUM(si.total) DESC;
        """)
        layout.addWidget(self.query_edit)
        
        # Buttons
        button_layout = QHBoxLayout()
        self.test_btn = QPushButton("اختبار الاستعلام")
        self.save_btn = QPushButton("حفظ")
        self.cancel_btn = QPushButton("إلغاء")
        
        self.test_btn.clicked.connect(self.test_query)
        self.save_btn.clicked.connect(self.accept)
        self.cancel_btn.clicked.connect(self.reject)
        
        button_layout.addWidget(self.test_btn)
        button_layout.addWidget(self.save_btn)
        button_layout.addWidget(self.cancel_btn)
        layout.addLayout(button_layout)
        
        self.setLayout(layout)
        
    def test_query(self):
        QMessageBox.information(self, "اختبار", "سيتم تطوير ميزة اختبار الاستعلام قريباً")
        
    def get_data(self):
        return {
            'name': self.name_edit.text(),
            'query': self.query_edit.toPlainText()
        }

class ReportsModule(QWidget):
    def __init__(self, db_manager, translator, parent=None):
        super().__init__(parent)
        self.db_manager = db_manager
        self.translator = translator
        self.current_report_data = None
        self.init_ui()
        self.load_saved_reports()
        
    def init_ui(self):
        layout = QVBoxLayout()
        
        # Header
        header_layout = QHBoxLayout()
        title_label = QLabel("التقارير والتحليلات")
        title_label.setFont(QFont("Arial", 16, QFont.Bold))
        header_layout.addWidget(title_label)
        header_layout.addStretch()
        layout.addLayout(header_layout)
        
        # Create splitter
        splitter = QSplitter(Qt.Horizontal)
        
        # Left panel - Report types
        left_panel = QWidget()
        left_layout = QVBoxLayout()
        
        # Standard reports
        standard_group = QGroupBox("التقارير المعيارية")
        standard_layout = QVBoxLayout()
        
        self.profit_loss_btn = QPushButton("تقرير الأرباح والخسائر")
        self.production_efficiency_btn = QPushButton("تقرير كفاءة الإنتاج")
        self.stock_levels_btn = QPushButton("تقرير مستويات المخزون")
        self.quality_metrics_btn = QPushButton("تقرير مقاييس الجودة")
        
        self.profit_loss_btn.clicked.connect(lambda: self.generate_report("profit_loss"))
        self.production_efficiency_btn.clicked.connect(lambda: self.generate_report("production_efficiency"))
        self.stock_levels_btn.clicked.connect(lambda: self.generate_report("stock_levels"))
        self.quality_metrics_btn.clicked.connect(lambda: self.generate_report("quality_metrics"))
        
        standard_layout.addWidget(self.profit_loss_btn)
        standard_layout.addWidget(self.production_efficiency_btn)
        standard_layout.addWidget(self.stock_levels_btn)
        standard_layout.addWidget(self.quality_metrics_btn)
        standard_group.setLayout(standard_layout)
        
        # Custom reports
        custom_group = QGroupBox("التقارير المخصصة")
        custom_layout = QVBoxLayout()
        
        self.create_custom_btn = QPushButton("إنشاء تقرير مخصص")
        self.create_custom_btn.clicked.connect(self.create_custom_report)
        
        self.saved_reports_list = QListWidget()
        self.saved_reports_list.itemDoubleClicked.connect(self.run_saved_report)
        
        custom_layout.addWidget(self.create_custom_btn)
        custom_layout.addWidget(QLabel("التقارير المحفوظة:"))
        custom_layout.addWidget(self.saved_reports_list)
        custom_group.setLayout(custom_layout)
        
        # Date range
        date_group = QGroupBox("نطاق التاريخ")
        date_layout = QFormLayout()
        
        self.start_date = QDateEdit()
        self.start_date.setDate(QDate.currentDate().addDays(-30))
        self.start_date.setCalendarPopup(True)
        
        self.end_date = QDateEdit()
        self.end_date.setDate(QDate.currentDate())
        self.end_date.setCalendarPopup(True)
        
        date_layout.addRow("من:", self.start_date)
        date_layout.addRow("إلى:", self.end_date)
        date_group.setLayout(date_layout)
        
        left_layout.addWidget(standard_group)
        left_layout.addWidget(custom_group)
        left_layout.addWidget(date_group)
        left_layout.addStretch()
        left_panel.setLayout(left_layout)
        
        # Right panel - Report display
        right_panel = QWidget()
        right_layout = QVBoxLayout()
        
        # Report controls
        controls_layout = QHBoxLayout()
        self.export_btn = QPushButton("تصدير")
        self.print_btn = QPushButton("طباعة")
        self.refresh_btn = QPushButton("تحديث")
        
        self.export_btn.clicked.connect(self.export_report)
        self.print_btn.clicked.connect(self.print_report)
        self.refresh_btn.clicked.connect(self.refresh_current_report)
        
        controls_layout.addWidget(self.export_btn)
        controls_layout.addWidget(self.print_btn)
        controls_layout.addWidget(self.refresh_btn)
        controls_layout.addStretch()
        
        # Progress bar
        self.progress_bar = QProgressBar()
        self.progress_bar.setVisible(False)
        
        # Report display area
        self.report_tabs = QTabWidget()
        
        # Summary tab
        self.summary_widget = QWidget()
        self.summary_layout = QVBoxLayout()
        self.summary_widget.setLayout(self.summary_layout)
        self.report_tabs.addTab(self.summary_widget, "الملخص")
        
        # Details tab
        self.details_table = QTableWidget()
        self.report_tabs.addTab(self.details_table, "التفاصيل")
        
        right_layout.addLayout(controls_layout)
        right_layout.addWidget(self.progress_bar)
        right_layout.addWidget(self.report_tabs)
        right_panel.setLayout(right_layout)
        
        # Add panels to splitter
        splitter.addWidget(left_panel)
        splitter.addWidget(right_panel)
        splitter.setSizes([300, 700])
        
        layout.addWidget(splitter)
        self.setLayout(layout)
        
    def load_saved_reports(self):
        try:
            conn = self.db_manager.get_connection()
            cursor = conn.cursor()
            
            cursor.execute("SELECT name, query FROM custom_reports ORDER BY name")
            reports = cursor.fetchall()
            
            self.saved_reports_list.clear()
            for report in reports:
                item = QListWidgetItem(report[0])
                item.setData(Qt.UserRole, report[1])
                self.saved_reports_list.addItem(item)
                
            conn.close()
            
        except Exception as e:
            print(f"Error loading saved reports: {e}")
            
    def generate_report(self, report_type):
        parameters = {
            'start_date': self.start_date.date().toString('yyyy-MM-dd'),
            'end_date': self.end_date.date().toString('yyyy-MM-dd')
        }
        
        self.progress_bar.setVisible(True)
        self.progress_bar.setValue(0)
        
        self.report_thread = ReportGeneratorThread(self.db_manager, report_type, parameters)
        self.report_thread.progress_updated.connect(self.progress_bar.setValue)
        self.report_thread.report_generated.connect(self.display_report)
        self.report_thread.error_occurred.connect(self.handle_report_error)
        self.report_thread.start()
        
    def create_custom_report(self):
        dialog = CustomReportDialog(self, self.translator)
        if dialog.exec_() == QDialog.Accepted:
            data = dialog.get_data()
            self.save_custom_report(data)
            
    def save_custom_report(self, data):
        try:
            conn = self.db_manager.get_connection()
            cursor = conn.cursor()
            
            cursor.execute("""
                INSERT OR REPLACE INTO custom_reports (name, query)
                VALUES (?, ?)
            """, (data['name'], data['query']))
            
            conn.commit()
            conn.close()
            
            QMessageBox.information(self, "نجح", "تم حفظ التقرير المخصص بنجاح")
            self.load_saved_reports()
            
        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"خطأ في حفظ التقرير: {str(e)}")
            
    def run_saved_report(self, item):
        query = item.data(Qt.UserRole)
        parameters = {'query': query}
        
        self.progress_bar.setVisible(True)
        self.progress_bar.setValue(0)
        
        self.report_thread = ReportGeneratorThread(self.db_manager, "custom", parameters)
        self.report_thread.progress_updated.connect(self.progress_bar.setValue)
        self.report_thread.report_generated.connect(self.display_report)
        self.report_thread.error_occurred.connect(self.handle_report_error)
        self.report_thread.start()
        
    def display_report(self, report_data):
        self.current_report_data = report_data
        self.progress_bar.setVisible(False)
        
        # Clear previous content
        for i in reversed(range(self.summary_layout.count())):
            self.summary_layout.itemAt(i).widget().setParent(None)
            
        if report_data['type'] == 'profit_loss':
            self.display_profit_loss_report(report_data)
        elif report_data['type'] == 'production_efficiency':
            self.display_production_efficiency_report(report_data)
        elif report_data['type'] == 'stock_levels':
            self.display_stock_levels_report(report_data)
        elif report_data['type'] == 'quality_metrics':
            self.display_quality_metrics_report(report_data)
        elif report_data['type'] == 'custom':
            self.display_custom_report(report_data)
            
    def display_profit_loss_report(self, data):
        # Summary
        summary_text = f"""
        <h2>تقرير الأرباح والخسائر</h2>
        <p><strong>الفترة:</strong> {data['period']}</p>
        <hr>
        <table style="width:100%; border-collapse: collapse;">
        <tr><td style="border: 1px solid #ddd; padding: 8px;"><strong>الإيرادات</strong></td>
            <td style="border: 1px solid #ddd; padding: 8px; text-align: right;">{data['revenue']:,.2f} ريال</td></tr>
        <tr><td style="border: 1px solid #ddd; padding: 8px;"><strong>تكلفة البضاعة المباعة</strong></td>
            <td style="border: 1px solid #ddd; padding: 8px; text-align: right;">{data['cogs']:,.2f} ريال</td></tr>
        <tr><td style="border: 1px solid #ddd; padding: 8px;"><strong>الربح الإجمالي</strong></td>
            <td style="border: 1px solid #ddd; padding: 8px; text-align: right;">{data['gross_profit']:,.2f} ريال</td></tr>
        <tr><td style="border: 1px solid #ddd; padding: 8px;"><strong>المصروفات التشغيلية</strong></td>
            <td style="border: 1px solid #ddd; padding: 8px; text-align: right;">{data['expenses']:,.2f} ريال</td></tr>
        <tr><td style="border: 1px solid #ddd; padding: 8px;"><strong>صافي الربح</strong></td>
            <td style="border: 1px solid #ddd; padding: 8px; text-align: right; {'color: green;' if data['net_profit'] >= 0 else 'color: red;'}">{data['net_profit']:,.2f} ريال</td></tr>
        </table>
        """
        
        summary_label = QLabel(summary_text)
        summary_label.setWordWrap(True)
        self.summary_layout.addWidget(summary_label)
        
        # Details table
        self.details_table.setColumnCount(3)
        self.details_table.setHorizontalHeaderLabels(["الشهر", "الإيرادات", "المصروفات"])
        
        # Combine monthly data
        monthly_data = {}
        for month, revenue in data['monthly_revenue']:
            monthly_data[month] = {'revenue': revenue, 'expenses': 0}
        for month, expenses in data['monthly_expenses']:
            if month in monthly_data:
                monthly_data[month]['expenses'] = expenses
            else:
                monthly_data[month] = {'revenue': 0, 'expenses': expenses}
        
        self.details_table.setRowCount(len(monthly_data))
        for row, (month, values) in enumerate(monthly_data.items()):
            self.details_table.setItem(row, 0, QTableWidgetItem(month))
            self.details_table.setItem(row, 1, QTableWidgetItem(f"{values['revenue']:,.2f} ريال"))
            self.details_table.setItem(row, 2, QTableWidgetItem(f"{values['expenses']:,.2f} ريال"))
            
    def display_production_efficiency_report(self, data):
        summary_text = f"""
        <h2>تقرير كفاءة الإنتاج</h2>
        <p><strong>الفترة:</strong> {data['period']}</p>
        <hr>
        <table style="width:100%; border-collapse: collapse;">
        <tr><td style="border: 1px solid #ddd; padding: 8px;"><strong>الطلبات المكتملة</strong></td>
            <td style="border: 1px solid #ddd; padding: 8px; text-align: right;">{data['completed_orders']}</td></tr>
        <tr><td style="border: 1px solid #ddd; padding: 8px;"><strong>متوسط وقت الإنجاز</strong></td>
            <td style="border: 1px solid #ddd; padding: 8px; text-align: right;">{data['avg_completion_time']:.1f} يوم</td></tr>
        <tr><td style="border: 1px solid #ddd; padding: 8px;"><strong>معدل الجودة</strong></td>
            <td style="border: 1px solid #ddd; padding: 8px; text-align: right;">{data['quality_rate']:.1f}%</td></tr>
        <tr><td style="border: 1px solid #ddd; padding: 8px;"><strong>إجمالي الفحوصات</strong></td>
            <td style="border: 1px solid #ddd; padding: 8px; text-align: right;">{data['total_inspections']}</td></tr>
        <tr><td style="border: 1px solid #ddd; padding: 8px;"><strong>الفحوصات المقبولة</strong></td>
            <td style="border: 1px solid #ddd; padding: 8px; text-align: right;">{data['passed_inspections']}</td></tr>
        </table>
        """
        
        summary_label = QLabel(summary_text)
        summary_label.setWordWrap(True)
        self.summary_layout.addWidget(summary_label)
        
    def display_stock_levels_report(self, data):
        summary_text = "<h2>تقرير مستويات المخزون</h2><hr>"
        
        summary_label = QLabel(summary_text)
        summary_label.setWordWrap(True)
        self.summary_layout.addWidget(summary_label)
        
        # Stock by type table
        self.details_table.setColumnCount(4)
        self.details_table.setHorizontalHeaderLabels(["نوع الحجر", "الحالة", "العدد", "القيمة"])
        
        self.details_table.setRowCount(len(data['stock_by_type']))
        for row, stock_data in enumerate(data['stock_by_type']):
            self.details_table.setItem(row, 0, QTableWidgetItem(str(stock_data[0])))
            self.details_table.setItem(row, 1, QTableWidgetItem(str(stock_data[1])))
            self.details_table.setItem(row, 2, QTableWidgetItem(str(stock_data[2])))
            self.details_table.setItem(row, 3, QTableWidgetItem(f"{stock_data[3]:,.2f} ريال"))
            
    def display_quality_metrics_report(self, data):
        summary_text = f"""
        <h2>تقرير مقاييس الجودة</h2>
        <p><strong>الفترة:</strong> {data['period']}</p>
        <hr>
        """
        
        summary_label = QLabel(summary_text)
        summary_label.setWordWrap(True)
        self.summary_layout.addWidget(summary_label)
        
        # Inspection results table
        self.details_table.setColumnCount(2)
        self.details_table.setHorizontalHeaderLabels(["النتيجة", "العدد"])
        
        self.details_table.setRowCount(len(data['inspection_results']))
        for row, result_data in enumerate(data['inspection_results']):
            self.details_table.setItem(row, 0, QTableWidgetItem(str(result_data[0])))
            self.details_table.setItem(row, 1, QTableWidgetItem(str(result_data[1])))
            
    def display_custom_report(self, data):
        summary_text = f"""
        <h2>تقرير مخصص</h2>
        <p><strong>الاستعلام:</strong></p>
        <pre>{data['query']}</pre>
        <hr>
        """
        
        summary_label = QLabel(summary_text)
        summary_label.setWordWrap(True)
        self.summary_layout.addWidget(summary_label)
        
        # Results table
        self.details_table.setColumnCount(len(data['columns']))
        self.details_table.setHorizontalHeaderLabels(data['columns'])
        
        self.details_table.setRowCount(len(data['results']))
        for row, result in enumerate(data['results']):
            for col, value in enumerate(result):
                self.details_table.setItem(row, col, QTableWidgetItem(str(value)))
                
    def handle_report_error(self, error_message):
        self.progress_bar.setVisible(False)
        QMessageBox.critical(self, "خطأ في التقرير", f"حدث خطأ أثناء إنشاء التقرير:\n{error_message}")
        
    def export_report(self):
        if not self.current_report_data:
            QMessageBox.warning(self, "تحذير", "لا يوجد تقرير لتصديره")
            return
            
        QMessageBox.information(self, "تصدير", "سيتم تطوير ميزة التصدير قريباً")
        
    def print_report(self):
        if not self.current_report_data:
            QMessageBox.warning(self, "تحذير", "لا يوجد تقرير للطباعة")
            return
            
        QMessageBox.information(self, "طباعة", "سيتم تطوير ميزة الطباعة قريباً")
        
    def refresh_current_report(self):
        QMessageBox.information(self, "تحديث", "سيتم تطوير ميزة التحديث قريباً")
