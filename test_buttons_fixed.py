#!/usr/bin/env python3
"""
Fixed Button Showcase - عرض الأزرار المُصحح
Simple and working button showcase without transform properties
"""

import sys
from PyQt5.QtWidgets import (QApplication, QMainWindow, QWidget, QVBoxLayout, 
                            QHBoxLayout, QGridLayout, QLabel, QPushButton, 
                            QFrame, QScrollArea, QMessageBox)
from PyQt5.QtCore import Qt
from PyQt5.QtGui import QFont

from ui.styles.modern_theme import ModernTheme
from ui.styles.components import ModernButton

class FixedButtonShowcase(QMainWindow):
    """Fixed button showcase without transform properties"""
    
    def __init__(self):
        super().__init__()
        self.init_ui()
        
    def init_ui(self):
        """Initialize the UI"""
        self.setWindowTitle("🔘 عرض الأزرار المُصحح | Fixed Button Showcase")
        self.setGeometry(100, 100, 1200, 800)
        
        # Apply modern theme
        self.setStyleSheet(ModernTheme.get_complete_stylesheet())
        
        # Create scroll area
        scroll_area = QScrollArea()
        scroll_widget = QWidget()
        scroll_area.setWidget(scroll_widget)
        scroll_area.setWidgetResizable(True)
        self.setCentralWidget(scroll_area)
        
        # Main layout
        main_layout = QVBoxLayout(scroll_widget)
        main_layout.setSpacing(30)
        main_layout.setContentsMargins(30, 30, 30, 30)
        
        # Title
        title = QLabel("🔘 معرض الأزرار الشامل | Complete Button Gallery")
        title.setFont(QFont("Arial", 24, QFont.Bold))
        title.setAlignment(Qt.AlignCenter)
        title.setStyleSheet(f"""
            color: {ModernTheme.COLORS['primary']};
            margin-bottom: 20px;
            padding: 20px;
            background-color: {ModernTheme.COLORS['bg_light']};
            border-radius: 15px;
            border: 2px solid {ModernTheme.COLORS['border_light']};
        """)
        main_layout.addWidget(title)
        
        # Create sections
        self.create_basic_buttons_section(main_layout)
        self.create_colored_buttons_section(main_layout)
        self.create_size_variants_section(main_layout)
        self.create_icon_buttons_section(main_layout)
        self.create_application_buttons_section(main_layout)
        
    def create_section_title(self, text):
        """Create a section title"""
        title = QLabel(text)
        title.setFont(QFont("Arial", 16, QFont.Bold))
        title.setStyleSheet(f"""
            color: {ModernTheme.COLORS['text_primary']};
            margin: 15px 0 10px 0;
            padding: 10px;
            background-color: {ModernTheme.COLORS['bg_light']};
            border-radius: 8px;
            border-left: 4px solid {ModernTheme.COLORS['primary']};
        """)
        return title
        
    def create_basic_buttons_section(self, parent_layout):
        """Create basic buttons section"""
        parent_layout.addWidget(self.create_section_title("🔹 الأزرار الأساسية | Basic Buttons"))
        
        frame = QFrame()
        layout = QHBoxLayout(frame)
        layout.setSpacing(15)
        
        basic_buttons = [
            ("زر عادي", "Normal Button"),
            ("زر مُفعل", "Enabled Button"),
            ("زر معطل", "Disabled Button"),
            ("زر افتراضي", "Default Button")
        ]
        
        for i, (text_ar, text_en) in enumerate(basic_buttons):
            btn = QPushButton(f"{text_ar}\n{text_en}")
            btn.setMinimumSize(150, 60)
            btn.clicked.connect(lambda checked, t=text_ar: self.show_button_clicked(t))
            
            if i == 2:  # Disable the third button
                btn.setEnabled(False)
            elif i == 3:  # Make the fourth button default
                btn.setDefault(True)
                btn.setStyleSheet(btn.styleSheet() + f"""
                    QPushButton {{
                        border: 2px solid {ModernTheme.COLORS['primary']};
                        font-weight: bold;
                    }}
                """)
            
            layout.addWidget(btn)
        
        parent_layout.addWidget(frame)
        
    def create_colored_buttons_section(self, parent_layout):
        """Create colored buttons section"""
        parent_layout.addWidget(self.create_section_title("🌈 الأزرار الملونة | Colored Buttons"))
        
        frame = QFrame()
        layout = QGridLayout(frame)
        layout.setSpacing(15)
        
        colored_buttons = [
            ("أساسي", "Primary", "primary"),
            ("نجاح", "Success", "success"),
            ("تحذير", "Warning", "warning"),
            ("خطر", "Danger", "danger"),
            ("معلومات", "Info", "info"),
            ("ثانوي", "Secondary", "secondary")
        ]
        
        for i, (text_ar, text_en, btn_type) in enumerate(colored_buttons):
            btn = ModernButton(f"{text_ar}\n{text_en}", btn_type)
            btn.setMinimumSize(150, 60)
            btn.clicked.connect(lambda checked, t=text_ar: self.show_button_clicked(t))
            
            row = i // 3
            col = i % 3
            layout.addWidget(btn, row, col)
        
        parent_layout.addWidget(frame)
        
    def create_size_variants_section(self, parent_layout):
        """Create size variants section"""
        parent_layout.addWidget(self.create_section_title("📏 أحجام مختلفة | Size Variants"))
        
        frame = QFrame()
        layout = QHBoxLayout(frame)
        layout.setSpacing(20)
        layout.setAlignment(Qt.AlignCenter)
        
        sizes = [
            ("صغير", "Small", (100, 30), 8),
            ("متوسط", "Medium", (130, 40), 10),
            ("كبير", "Large", (160, 50), 12),
            ("كبير جداً", "X-Large", (200, 60), 14)
        ]
        
        for text_ar, text_en, size, font_size in sizes:
            btn = ModernButton(f"{text_ar}\n{text_en}", "primary")
            btn.setFixedSize(*size)
            btn.setStyleSheet(btn.styleSheet() + f"font-size: {font_size}pt;")
            btn.clicked.connect(lambda checked, t=text_ar: self.show_button_clicked(t))
            layout.addWidget(btn)
        
        parent_layout.addWidget(frame)
        
    def create_icon_buttons_section(self, parent_layout):
        """Create icon buttons section"""
        parent_layout.addWidget(self.create_section_title("🎨 أزرار بأيقونات | Icon Buttons"))
        
        frame = QFrame()
        layout = QHBoxLayout(frame)
        layout.setSpacing(15)
        
        icon_buttons = [
            ("💾 حفظ", "💾 Save", "success"),
            ("✏️ تعديل", "✏️ Edit", "warning"),
            ("🗑️ حذف", "🗑️ Delete", "danger"),
            ("👁️ عرض", "👁️ View", "info"),
            ("📄 طباعة", "📄 Print", "secondary"),
            ("📤 تصدير", "📤 Export", "primary")
        ]
        
        for text_ar, text_en, btn_type in icon_buttons:
            btn = ModernButton(f"{text_ar}\n{text_en}", btn_type)
            btn.setMinimumSize(120, 60)
            btn.clicked.connect(lambda checked, t=text_ar: self.show_button_clicked(t))
            layout.addWidget(btn)
        
        parent_layout.addWidget(frame)
        
    def create_application_buttons_section(self, parent_layout):
        """Create application buttons section"""
        parent_layout.addWidget(self.create_section_title("🏭 أزرار التطبيق | Application Buttons"))
        
        frame = QFrame()
        layout = QGridLayout(frame)
        layout.setSpacing(15)
        
        app_buttons = [
            ("👤 إضافة عميل", "👤 Add Customer", "success"),
            ("🚛 تسجيل شاحنة", "🚛 Register Truck", "warning"),
            ("📄 إنشاء فاتورة", "📄 Create Invoice", "primary"),
            ("📦 عرض المخزون", "📦 View Inventory", "info"),
            ("📊 تقرير مبيعات", "📊 Sales Report", "secondary"),
            ("💾 نسخ احتياطي", "💾 Backup Data", "danger"),
            ("⚙️ إعدادات النظام", "⚙️ System Settings", "primary"),
            ("🚪 تسجيل خروج", "🚪 Logout", "danger")
        ]
        
        for i, (text_ar, text_en, btn_type) in enumerate(app_buttons):
            btn = ModernButton(f"{text_ar}\n{text_en}", btn_type)
            btn.setMinimumSize(180, 70)
            btn.clicked.connect(lambda checked, t=text_ar: self.show_button_clicked(t))
            
            row = i // 4
            col = i % 4
            layout.addWidget(btn, row, col)
        
        parent_layout.addWidget(frame)
        
        # Add summary
        self.create_summary_section(parent_layout)
    
    def create_summary_section(self, parent_layout):
        """Create summary section"""
        parent_layout.addWidget(self.create_section_title("📋 ملخص الأزرار | Button Summary"))
        
        summary_frame = QFrame()
        summary_frame.setStyleSheet(f"""
            QFrame {{
                background-color: {ModernTheme.COLORS['bg_primary']};
                border: 2px solid {ModernTheme.COLORS['border_light']};
                border-radius: 12px;
                padding: 20px;
                margin: 10px;
            }}
        """)
        
        summary_layout = QVBoxLayout(summary_frame)
        
        summary_text = QLabel("""
        📊 إحصائيات الأزرار المعروضة:
        
        🔹 الأزرار الأساسية: 4 أنواع
        🌈 الأزرار الملونة: 6 ألوان مختلفة
        📏 أحجام مختلفة: 4 أحجام
        🎨 أزرار بأيقونات: 6 أزرار
        🏭 أزرار التطبيق: 8 أزرار خاصة بالنظام
        
        📈 المجموع: 28 زر مختلف!
        
        ✨ جميع الأزرار تدعم:
        • تأثيرات التمرير (Hover Effects)
        • تأثيرات النقر (Click Effects)
        • تصميم متجاوب (Responsive Design)
        • ألوان متناسقة (Consistent Colors)
        """)
        
        summary_text.setFont(QFont("Arial", 11))
        summary_text.setStyleSheet(f"color: {ModernTheme.COLORS['text_primary']}; line-height: 1.6;")
        summary_text.setWordWrap(True)
        summary_layout.addWidget(summary_text)
        
        parent_layout.addWidget(summary_frame)
    
    def show_button_clicked(self, button_name):
        """Show button clicked message"""
        msg = QMessageBox()
        msg.setWindowTitle("تم النقر على الزر")
        msg.setText(f"تم النقر على: {button_name}")
        msg.setIcon(QMessageBox.Information)
        msg.exec_()

def main():
    """Main function"""
    app = QApplication(sys.argv)
    
    # Create and show showcase window
    window = FixedButtonShowcase()
    window.show()
    
    print("=== Fixed Button Showcase ===")
    print("🔘 عرض الأزرار المُصحح")
    print("✅ تم إصلاح مشكلة transform properties")
    print("🖱️ انقر على الأزرار لاختبار التفاعل")
    
    return app.exec_()

if __name__ == "__main__":
    sys.exit(main())
