from PyQt5.QtWidgets import (QWidget, QVBoxLayout, QHBoxLayout, QPushButton, 
                            QTableWidget, QTableWidgetItem, QLineEdit, QLabel,
                            QFormLayout, QDialog, QMessageBox, QComboBox,
                            QGroupBox, QDateEdit, QTextEdit, QHeaderView)
from PyQt5.QtCore import Qt, QDate
from PyQt5.QtGui import QFont
import sqlite3

class CustomerDialog(QDialog):
    def __init__(self, parent=None, customer_data=None, translator=None):
        super().__init__(parent)
        self.translator = translator
        self.customer_data = customer_data
        self.init_ui()
        
        if customer_data:
            self.populate_fields()
            
    def init_ui(self):
        self.setWindowTitle(self.tr("إضافة/تعديل عميل"))
        self.setModal(True)
        self.resize(500, 600)
        
        layout = QVBoxLayout()
        
        # Customer form
        form_layout = QFormLayout()
        
        self.name_edit = QLineEdit()
        self.phone_edit = QLineEdit()
        self.email_edit = QLineEdit()
        self.address_edit = QTextEdit()
        self.city_edit = QLineEdit()
        self.country_edit = QLineEdit()
        self.company_edit = QLineEdit()
        self.tax_number_edit = QLineEdit()
        self.credit_limit_edit = QLineEdit()
        self.payment_terms_combo = QComboBox()
        self.payment_terms_combo.addItems(["نقدي", "آجل 30 يوم", "آجل 60 يوم", "آجل 90 يوم"])
        self.notes_edit = QTextEdit()
        
        form_layout.addRow("الاسم:", self.name_edit)
        form_layout.addRow("الهاتف:", self.phone_edit)
        form_layout.addRow("البريد الإلكتروني:", self.email_edit)
        form_layout.addRow("العنوان:", self.address_edit)
        form_layout.addRow("المدينة:", self.city_edit)
        form_layout.addRow("البلد:", self.country_edit)
        form_layout.addRow("الشركة:", self.company_edit)
        form_layout.addRow("الرقم الضريبي:", self.tax_number_edit)
        form_layout.addRow("حد الائتمان:", self.credit_limit_edit)
        form_layout.addRow("شروط الدفع:", self.payment_terms_combo)
        form_layout.addRow("ملاحظات:", self.notes_edit)
        
        layout.addLayout(form_layout)
        
        # Buttons
        button_layout = QHBoxLayout()
        self.save_btn = QPushButton("حفظ")
        self.cancel_btn = QPushButton("إلغاء")
        
        self.save_btn.clicked.connect(self.accept)
        self.cancel_btn.clicked.connect(self.reject)
        
        button_layout.addWidget(self.save_btn)
        button_layout.addWidget(self.cancel_btn)
        layout.addLayout(button_layout)
        
        self.setLayout(layout)
        
    def populate_fields(self):
        if self.customer_data:
            self.name_edit.setText(str(self.customer_data.get('name', '')))
            self.phone_edit.setText(str(self.customer_data.get('phone', '')))
            self.email_edit.setText(str(self.customer_data.get('email', '')))
            self.address_edit.setPlainText(str(self.customer_data.get('address', '')))
            self.city_edit.setText(str(self.customer_data.get('city', '')))
            self.country_edit.setText(str(self.customer_data.get('country', '')))
            self.company_edit.setText(str(self.customer_data.get('company', '')))
            self.tax_number_edit.setText(str(self.customer_data.get('tax_number', '')))
            self.credit_limit_edit.setText(str(self.customer_data.get('credit_limit', '')))
            self.notes_edit.setPlainText(str(self.customer_data.get('notes', '')))
            
    def get_data(self):
        return {
            'name': self.name_edit.text(),
            'phone': self.phone_edit.text(),
            'email': self.email_edit.text(),
            'address': self.address_edit.toPlainText(),
            'city': self.city_edit.text(),
            'country': self.country_edit.text(),
            'company': self.company_edit.text(),
            'tax_number': self.tax_number_edit.text(),
            'credit_limit': float(self.credit_limit_edit.text()) if self.credit_limit_edit.text() else 0,
            'payment_terms': self.payment_terms_combo.currentText(),
            'notes': self.notes_edit.toPlainText()
        }

class CustomersModule(QWidget):
    def __init__(self, db_manager, translator, parent=None):
        super().__init__(parent)
        self.db_manager = db_manager
        self.translator = translator
        self.init_ui()
        self.load_customers()
        
    def init_ui(self):
        layout = QVBoxLayout()
        
        # Header
        header_layout = QHBoxLayout()
        title_label = QLabel("إدارة العملاء")
        title_label.setFont(QFont("Arial", 16, QFont.Bold))
        header_layout.addWidget(title_label)
        header_layout.addStretch()
        
        # Search
        self.search_edit = QLineEdit()
        self.search_edit.setPlaceholderText("البحث في العملاء...")
        self.search_edit.textChanged.connect(self.search_customers)
        header_layout.addWidget(QLabel("البحث:"))
        header_layout.addWidget(self.search_edit)
        
        layout.addLayout(header_layout)
        
        # Buttons
        button_layout = QHBoxLayout()
        self.add_btn = QPushButton("إضافة عميل جديد")
        self.edit_btn = QPushButton("تعديل")
        self.delete_btn = QPushButton("حذف")
        self.refresh_btn = QPushButton("تحديث")
        
        self.add_btn.clicked.connect(self.add_customer)
        self.edit_btn.clicked.connect(self.edit_customer)
        self.delete_btn.clicked.connect(self.delete_customer)
        self.refresh_btn.clicked.connect(self.load_customers)
        
        button_layout.addWidget(self.add_btn)
        button_layout.addWidget(self.edit_btn)
        button_layout.addWidget(self.delete_btn)
        button_layout.addWidget(self.refresh_btn)
        button_layout.addStretch()
        
        layout.addLayout(button_layout)
        
        # Table
        self.table = QTableWidget()
        self.table.setColumnCount(8)
        self.table.setHorizontalHeaderLabels([
            "الرقم", "الاسم", "الهاتف", "البريد الإلكتروني", 
            "المدينة", "الشركة", "حد الائتمان", "تاريخ الإضافة"
        ])
        
        # Set table properties
        self.table.setSelectionBehavior(QTableWidget.SelectRows)
        self.table.setAlternatingRowColors(True)
        self.table.horizontalHeader().setStretchLastSection(True)
        self.table.horizontalHeader().setSectionResizeMode(QHeaderView.Stretch)
        
        layout.addWidget(self.table)
        
        # Statistics
        stats_layout = QHBoxLayout()
        self.total_customers_label = QLabel("إجمالي العملاء: 0")
        self.active_customers_label = QLabel("العملاء النشطون: 0")
        
        stats_layout.addWidget(self.total_customers_label)
        stats_layout.addWidget(self.active_customers_label)
        stats_layout.addStretch()
        
        layout.addLayout(stats_layout)
        
        self.setLayout(layout)
        
    def load_customers(self):
        try:
            conn = self.db_manager.get_connection()
            cursor = conn.cursor()
            
            cursor.execute("""
                SELECT id, name, phone, email, city, company, credit_limit, created_at
                FROM customers 
                ORDER BY created_at DESC
            """)
            
            customers = cursor.fetchall()
            
            self.table.setRowCount(len(customers))
            
            for row, customer in enumerate(customers):
                for col, value in enumerate(customer):
                    if col == 6:  # credit_limit
                        value = f"{value:,.2f} ريال"
                    elif col == 7:  # created_at
                        value = str(value)[:10]  # Show only date part
                    
                    item = QTableWidgetItem(str(value))
                    item.setTextAlignment(Qt.AlignCenter)
                    self.table.setItem(row, col, item)
            
            # Update statistics
            self.total_customers_label.setText(f"إجمالي العملاء: {len(customers)}")
            self.active_customers_label.setText(f"العملاء النشطون: {len(customers)}")
            
            conn.close()
            
        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"خطأ في تحميل العملاء: {str(e)}")
            
    def search_customers(self):
        search_text = self.search_edit.text().lower()
        
        for row in range(self.table.rowCount()):
            show_row = False
            for col in range(self.table.columnCount()):
                item = self.table.item(row, col)
                if item and search_text in item.text().lower():
                    show_row = True
                    break
            self.table.setRowHidden(row, not show_row)
            
    def add_customer(self):
        dialog = CustomerDialog(self, translator=self.translator)
        if dialog.exec_() == QDialog.Accepted:
            data = dialog.get_data()
            self.save_customer(data)
            
    def edit_customer(self):
        current_row = self.table.currentRow()
        if current_row < 0:
            QMessageBox.warning(self, "تحذير", "يرجى اختيار عميل للتعديل")
            return
            
        customer_id = self.table.item(current_row, 0).text()
        customer_data = self.get_customer_data(customer_id)
        
        dialog = CustomerDialog(self, customer_data, self.translator)
        if dialog.exec_() == QDialog.Accepted:
            data = dialog.get_data()
            self.update_customer(customer_id, data)
            
    def delete_customer(self):
        current_row = self.table.currentRow()
        if current_row < 0:
            QMessageBox.warning(self, "تحذير", "يرجى اختيار عميل للحذف")
            return
            
        customer_name = self.table.item(current_row, 1).text()
        reply = QMessageBox.question(
            self, "تأكيد الحذف", 
            f"هل أنت متأكد من حذف العميل '{customer_name}'؟",
            QMessageBox.Yes | QMessageBox.No
        )
        
        if reply == QMessageBox.Yes:
            customer_id = self.table.item(current_row, 0).text()
            self.remove_customer(customer_id)
            
    def save_customer(self, data):
        try:
            conn = self.db_manager.get_connection()
            cursor = conn.cursor()
            
            cursor.execute("""
                INSERT INTO customers (name, phone, email, address, city, country, 
                                     company, tax_number, credit_limit, payment_terms, notes)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            """, (
                data['name'], data['phone'], data['email'], data['address'],
                data['city'], data['country'], data['company'], data['tax_number'],
                data['credit_limit'], data['payment_terms'], data['notes']
            ))
            
            conn.commit()
            conn.close()
            
            QMessageBox.information(self, "نجح", "تم إضافة العميل بنجاح")
            self.load_customers()
            
        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"خطأ في إضافة العميل: {str(e)}")
            
    def update_customer(self, customer_id, data):
        try:
            conn = self.db_manager.get_connection()
            cursor = conn.cursor()
            
            cursor.execute("""
                UPDATE customers 
                SET name=?, phone=?, email=?, address=?, city=?, country=?,
                    company=?, tax_number=?, credit_limit=?, payment_terms=?, notes=?
                WHERE id=?
            """, (
                data['name'], data['phone'], data['email'], data['address'],
                data['city'], data['country'], data['company'], data['tax_number'],
                data['credit_limit'], data['payment_terms'], data['notes'], customer_id
            ))
            
            conn.commit()
            conn.close()
            
            QMessageBox.information(self, "نجح", "تم تحديث العميل بنجاح")
            self.load_customers()
            
        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"خطأ في تحديث العميل: {str(e)}")
            
    def remove_customer(self, customer_id):
        try:
            conn = self.db_manager.get_connection()
            cursor = conn.cursor()
            
            cursor.execute("DELETE FROM customers WHERE id=?", (customer_id,))
            
            conn.commit()
            conn.close()
            
            QMessageBox.information(self, "نجح", "تم حذف العميل بنجاح")
            self.load_customers()
            
        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"خطأ في حذف العميل: {str(e)}")
            
    def get_customer_data(self, customer_id):
        try:
            conn = self.db_manager.get_connection()
            cursor = conn.cursor()
            
            cursor.execute("""
                SELECT name, phone, email, address, city, country, company, 
                       tax_number, credit_limit, payment_terms, notes
                FROM customers WHERE id=?
            """, (customer_id,))
            
            result = cursor.fetchone()
            conn.close()
            
            if result:
                return {
                    'name': result[0],
                    'phone': result[1],
                    'email': result[2],
                    'address': result[3],
                    'city': result[4],
                    'country': result[5],
                    'company': result[6],
                    'tax_number': result[7],
                    'credit_limit': result[8],
                    'payment_terms': result[9],
                    'notes': result[10]
                }
            return {}
            
        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"خطأ في جلب بيانات العميل: {str(e)}")
            return {}
