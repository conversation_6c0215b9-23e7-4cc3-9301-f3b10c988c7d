#!/usr/bin/env python3
"""
Test Buttons - اختبار الأزرار
Simple test for button showcase
"""

import sys
from PyQt5.QtWidgets import QApplication

# Import the button showcase
from ui.components.button_showcase import ButtonShowcase

def main():
    """Main function to run button showcase"""
    app = QApplication(sys.argv)
    
    # Create and show the button showcase
    showcase = ButtonShowcase()
    showcase.show()
    
    print("🔘 عرض الأزرار الشامل")
    print("Button Showcase is now running!")
    print("Click on buttons to test their functionality.")
    
    return app.exec_()

if __name__ == "__main__":
    sys.exit(main())
