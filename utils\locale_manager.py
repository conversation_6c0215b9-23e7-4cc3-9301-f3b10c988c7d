from PyQt5.QtCore import QLocale, QTranslator, QCoreApplication
import locale
import os

class LocaleManager:
    def __init__(self):
        self.current_locale = QLocale()
        self.translators = {}
        
    def set_locale(self, language_code):
        """Set application locale"""
        try:
            # Map language codes to locale names
            locale_map = {
                'ar': 'ar_SA.UTF-8',
                'en': 'en_US.UTF-8',
                'fr': 'fr_FR.UTF-8',
                'es': 'es_ES.UTF-8',
                'de': 'de_DE.UTF-8',
                'it': 'it_IT.UTF-8',
                'tr': 'tr_TR.UTF-8',
                'ru': 'ru_RU.UTF-8',
                'zh': 'zh_CN.UTF-8',
                'ur': 'ur_PK.UTF-8'
            }
            
            locale_name = locale_map.get(language_code, 'en_US.UTF-8')
            
            # Set system locale
            try:
                locale.setlocale(locale.LC_ALL, locale_name)
            except locale.Error:
                # Fallback to C locale
                locale.setlocale(locale.LC_ALL, 'C')
                
            # Set Qt locale
            qt_locale = QLocale(language_code)
            QLocale.setDefault(qt_locale)
            self.current_locale = qt_locale
            
        except Exception as e:
            print(f"Error setting locale: {e}")
            
    def format_currency(self, amount, currency_code='USD'):
        """Format currency according to locale"""
        return self.current_locale.toCurrencyString(amount, currency_code)
        
    def format_date(self, date):
        """Format date according to locale"""
        return self.current_locale.toString(date, QLocale.ShortFormat)
        
    def format_datetime(self, datetime):
        """Format datetime according to locale"""
        return self.current_locale.toString(datetime, QLocale.ShortFormat)
        
    def format_number(self, number, precision=2):
        """Format number according to locale"""
        return self.current_locale.toString(number, 'f', precision)
        
    def get_decimal_separator(self):
        """Get decimal separator for current locale"""
        return self.current_locale.decimalPoint()
        
    def get_thousand_separator(self):
        """Get thousand separator for current locale"""
        return self.current_locale.groupSeparator()

# Global locale manager instance
locale_manager = LocaleManager()
