# حل مشاكل تسجيل الدخول - نظام إدارة مصنع الحسن ستون

## 🔐 بيانات تسجيل الدخول الافتراضية

### المدير الرئيسي:
- **اسم المستخدم:** admin
- **كلمة المرور:** admin123

## ⚠️ المشاكل الشائعة وحلولها

### 1. زر تسجيل الدخول لا يستجيب

#### الأسباب المحتملة:
- عدم اتصال الزر بدالة تسجيل الدخول
- مشكلة في قاعدة البيانات
- خطأ في واجهة المستخدم

#### الحلول:
```bash
# 1. اختبار قاعدة البيانات
python test_login.py

# 2. اختبار واجهة تسجيل الدخول
python test_login_ui.py

# 3. إعادة تشغيل التطبيق
python main.py
```

### 2. رسالة خطأ "Invalid credentials"

#### تأكد من:
- كتابة اسم المستخدم بشكل صحيح: `admin`
- كتابة كلمة المرور بشكل صحيح: `admin123`
- عدم وجود مسافات إضافية
- حالة الأحرف (case-sensitive)

### 3. الحساب مقفل مؤقتاً

#### السبب:
- تجاوز عدد محاولات تسجيل الدخول الخاطئة (3 محاولات)

#### الحل:
```bash
# إعادة تعيين محاولات تسجيل الدخول
python -c "
from database.db_manager import DatabaseManager
db = DatabaseManager()
conn = db.get_connection()
cursor = conn.cursor()
cursor.execute('UPDATE users SET failed_login_attempts = 0, locked_until = NULL WHERE username = \"admin\"')
conn.commit()
conn.close()
print('تم إعادة تعيين محاولات تسجيل الدخول')
"
```

### 4. قاعدة البيانات غير موجودة

#### الحل:
```bash
# إنشاء قاعدة البيانات والمستخدم الافتراضي
python -c "
from database.db_manager import DatabaseManager
db = DatabaseManager()
db.create_tables()
db.create_default_data()
print('تم إنشاء قاعدة البيانات والمستخدم الافتراضي')
"
```

### 5. خطأ في الاتصال بقاعدة البيانات

#### تحقق من:
- وجود مجلد `database`
- صلاحيات الكتابة في مجلد المشروع
- عدم وجود ملفات قاعدة بيانات تالفة

#### الحل:
```bash
# حذف قاعدة البيانات وإعادة إنشائها
del database\*.db
python -c "
from database.db_manager import DatabaseManager
db = DatabaseManager()
db.create_tables()
db.create_default_data()
print('تم إعادة إنشاء قاعدة البيانات')
"
```

## 🔧 أدوات التشخيص

### 1. اختبار قاعدة البيانات:
```bash
python test_login.py
```

### 2. اختبار واجهة تسجيل الدخول:
```bash
python test_login_ui.py
```

### 3. فحص ملفات السجل:
```bash
# البحث عن أخطاء في ملفات السجل
dir logs\*.log
```

## 📋 خطوات استكشاف الأخطاء

### الخطوة 1: تحقق من قاعدة البيانات
```bash
python test_login.py
```
**النتيجة المتوقعة:** ✅ Login test passed!

### الخطوة 2: تحقق من واجهة المستخدم
```bash
python test_login_ui.py
```
**النتيجة المتوقعة:** نافذة تسجيل دخول مع بيانات معبأة مسبقاً

### الخطوة 3: تشغيل التطبيق الرئيسي
```bash
python main.py
```
**النتيجة المتوقعة:** نافذة تسجيل دخول تعمل بشكل صحيح

### الخطوة 4: اختبار تسجيل الدخول
1. أدخل: admin
2. أدخل: admin123
3. انقر "تسجيل الدخول"
**النتيجة المتوقعة:** الانتقال إلى لوحة التحكم الرئيسية

## 🆘 إذا لم تنجح الحلول السابقة

### إعادة تعيين كاملة:
```bash
# 1. حذف قاعدة البيانات
rmdir /s database

# 2. إعادة تثبيت المتطلبات
pip install -r requirements.txt

# 3. إعادة تشغيل التطبيق
python main.py
```

### إنشاء مستخدم جديد يدوياً:
```bash
python -c "
from database.db_manager import DatabaseManager
import hashlib
import json

db = DatabaseManager()
conn = db.get_connection()
cursor = conn.cursor()

# إنشاء مستخدم جديد
username = 'test'
password = 'test123'
password_hash = hashlib.sha256(password.encode()).hexdigest()

cursor.execute('''
    INSERT INTO users (username, password_hash, full_name, role, permissions)
    VALUES (?, ?, ?, ?, ?)
''', (username, password_hash, 'Test User', 'admin', json.dumps(['all'])))

conn.commit()
conn.close()
print(f'تم إنشاء مستخدم جديد: {username} / {password}')
"
```

## 📞 الدعم الفني

إذا استمرت المشكلة:
1. تأكد من تشغيل التطبيق كمدير (Run as Administrator)
2. تحقق من إعدادات مكافحة الفيروسات
3. تأكد من وجود جميع الملفات المطلوبة
4. راجع ملفات السجل للأخطاء التفصيلية

---

## ✅ تأكيد نجاح الإصلاح

بعد تطبيق الحلول، يجب أن تحصل على:
- ✅ نافذة تسجيل دخول تظهر بشكل صحيح
- ✅ زر تسجيل الدخول يستجيب للنقر
- ✅ الانتقال إلى لوحة التحكم بعد تسجيل الدخول الناجح
- ✅ عرض رسائل خطأ واضحة في حالة البيانات الخاطئة
