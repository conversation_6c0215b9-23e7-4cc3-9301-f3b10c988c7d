# تعليمات تحويل التطبيق إلى ملف تنفيذي (.exe)

## الطرق المتاحة لتشغيل التطبيق

### 1. تشغيل مباشر باستخدام Python
```bash
python main.py
```
أو استخدم الملف:
```bash
run_app.bat
```

### 2. إنشاء ملف تنفيذي (.exe)

#### الطريقة الأولى: استخدام الملف المُعد مسبقاً
```bash
create_executable.bat
```

#### الطريقة الثانية: استخدام PyInstaller يدوياً
```bash
# تثبيت PyInstaller
pip install pyinstaller

# إنشاء ملف تنفيذي (مجلد)
pyinstaller --name=AlHassanStoneFactory --windowed --onedir main.py

# إنشاء ملف تنفيذي واحد
pyinstaller --name=AlHassanStoneFactory --windowed --onefile main.py
```

#### الطريقة الثالثة: استخدام auto-py-to-exe (واجهة رسومية)
```bash
# تثبيت auto-py-to-exe
pip install auto-py-to-exe

# تشغيل الواجهة الرسومية
auto-py-to-exe
```

## حل مشاكل الصلاحيات

إذا واجهت مشاكل في الصلاحيات:

1. **تشغيل Command Prompt كمدير**
   - انقر بزر الماوس الأيمن على Command Prompt
   - اختر "Run as administrator"

2. **إضافة استثناء في Windows Defender**
   - افتح Windows Security
   - اذهب إلى Virus & threat protection
   - أضف مجلد المشروع كاستثناء

3. **تعطيل Real-time protection مؤقتاً**
   - أثناء عملية البناء فقط

## الملفات المُنشأة

بعد نجاح العملية ستجد:
- `dist/AlHassanStoneFactory/` - مجلد يحتوي على الملف التنفيذي
- `AlHassanStoneFactory.exe` - الملف التنفيذي الرئيسي
- `_internal/` - مجلد يحتوي على المكتبات المطلوبة

## متطلبات التشغيل

- Windows 10 أو أحدث
- Python 3.8+ (للتطوير فقط)
- PyQt5 (مُضمن في الملف التنفيذي)

## استكشاف الأخطاء

### خطأ "Permission denied"
- تأكد من إغلاق أي نسخة قيد التشغيل من التطبيق
- تشغيل Command Prompt كمدير
- إضافة استثناء في برنامج مكافحة الفيروسات

### خطأ "Module not found"
- تأكد من تثبيت جميع المتطلبات:
```bash
pip install -r requirements.txt
```

### الملف التنفيذي لا يعمل
- تأكد من وجود جميع الملفات في مجلد `_internal`
- تشغيل الملف من نفس المجلد
- فحص ملف `warn-*.txt` في مجلد `build` للأخطاء

## الدعم الفني

إذا واجهت أي مشاكل:
1. تأكد من تحديث Python و pip
2. تأكد من تثبيت جميع المتطلبات
3. جرب الطرق المختلفة المذكورة أعلاه
4. استخدم `run_app.bat` كبديل مؤقت
