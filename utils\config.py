import json
import os
from PyQt5.QtCore import QSettings

class Config:
    def __init__(self):
        self.settings = QSettings("AlHassanStone", "FactoryManagement")
        self.config_file = "config/settings.json"
        self.ensure_config_directory()
        
    def ensure_config_directory(self):
        """Create config directory if it doesn't exist"""
        os.makedirs(os.path.dirname(self.config_file), exist_ok=True)
        
    def get(self, key, default_value=None):
        """Get configuration value"""
        return self.settings.value(key, default_value)
        
    def set(self, key, value):
        """Set configuration value"""
        self.settings.setValue(key, value)
        
    def get_database_path(self):
        """Get database path"""
        return self.get("database_path", "data/al_hassan_stone.db")
        
    def set_database_path(self, path):
        """Set database path"""
        self.set("database_path", path)
        
    def get_language(self):
        """Get application language"""
        return self.get("language", "ar")
        
    def set_language(self, language):
        """Set application language"""
        self.set("language", language)
        
    def get_culture(self):
        """Get cultural preferences"""
        return self.get("culture", "ar-SA")
        
    def set_culture(self, culture):
        """Set cultural preferences"""
        self.set("culture", culture)
        
    def get_theme(self):
        """Get application theme"""
        return self.get("theme", "dark")
        
    def set_theme(self, theme):
        """Set application theme"""
        self.set("theme", theme)
        
    def get_window_geometry(self):
        """Get main window geometry"""
        return self.settings.value("window_geometry")
        
    def set_window_geometry(self, geometry):
        """Set main window geometry"""
        self.settings.setValue("window_geometry", geometry)
        
    def get_backup_directory(self):
        """Get backup directory"""
        return self.get("backup_directory", "backups/")
        
    def set_backup_directory(self, directory):
        """Set backup directory"""
        self.set("backup_directory", directory)

    def get_supported_languages(self):
        """Get list of supported languages"""
        return {
            'ar': 'العربية',
            'en': 'English', 
            'fr': 'Français',
            'es': 'Español',
            'de': 'Deutsch',
            'it': 'Italiano',
            'tr': 'Türkçe',
            'ru': 'Русский',
            'zh': '中文',
            'ur': 'اردو'
        }

    def get_rtl_languages(self):
        """Get list of RTL languages"""
        return ['ar', 'ur', 'he', 'fa']

    def is_rtl_language(self, language=None):
        """Check if language is RTL"""
        if language is None:
            language = self.get_language()
        return language in self.get_rtl_languages()
        
    def get_cultural_preference(self, key, default=None):
        """Get cultural preference"""
        return self.get(f"culture_{key}", default)
        
    def set_cultural_preference(self, key, value):
        """Set cultural preference"""
        self.set(f"culture_{key}", value)
