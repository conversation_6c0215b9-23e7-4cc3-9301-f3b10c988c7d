import json
import os
from typing import Dict, Any

class Translator:
    def __init__(self):
        self.current_language = 'ar'
        self.translations = {}
        self.load_translations()
        
    def load_translations(self):
        """Load translation files"""
        translations_dir = 'translations'
        
        if not os.path.exists(translations_dir):
            os.makedirs(translations_dir)
            self.create_default_translations()
            
        # Load all translation files
        for filename in os.listdir(translations_dir):
            if filename.endswith('.json'):
                lang_code = filename.replace('.json', '')
                filepath = os.path.join(translations_dir, filename)
                
                try:
                    with open(filepath, 'r', encoding='utf-8') as f:
                        self.translations[lang_code] = json.load(f)
                except Exception as e:
                    print(f"Error loading translation file {filename}: {e}")
                    
    def create_default_translations(self):
        """Create default translation files"""
        translations_dir = 'translations'
        
        # Arabic translations
        ar_translations = {
            "app_title": "نظام إدارة مصنع الحسن ستون",
            "login": "تسجيل الدخول",
            "logout": "تسجيل الخروج",
            "username": "اسم المستخدم",
            "password": "كلمة المرور",
            "dashboard": "لوحة التحكم",
            "customers": "العملاء",
            "suppliers": "الموردين",
            "trucks": "الشاحنات",
            "blocks": "الكتل",
            "slabs": "البلاطات",
            "sales": "المبيعات",
            "inventory": "المخزون",
            "expenses": "المصروفات",
            "reports": "التقارير",
            "production": "تخطيط الإنتاج",
            "procurement": "المشتريات",
            "quality": "مراقبة الجودة",
            "maintenance": "الصيانة",
            "quotation": "عروض الأسعار",
            "users": "إدارة المستخدمين",
            "backup": "النسخ الاحتياطي",
            "settings": "الإعدادات",
            "help": "المساعدة",
            "about": "حول البرنامج",
            "add": "إضافة",
            "edit": "تعديل",
            "delete": "حذف",
            "save": "حفظ",
            "cancel": "إلغاء",
            "search": "البحث",
            "filter": "تصفية",
            "export": "تصدير",
            "import": "استيراد",
            "print": "طباعة",
            "refresh": "تحديث",
            "close": "إغلاق",
            "yes": "نعم",
            "no": "لا",
            "ok": "موافق",
            "error": "خطأ",
            "warning": "تحذير",
            "info": "معلومات",
            "success": "نجح",
            "failed": "فشل",
            "loading": "جاري التحميل...",
            "please_wait": "يرجى الانتظار...",
            "no_data": "لا توجد بيانات",
            "select_item": "اختر عنصر",
            "confirm_delete": "تأكيد الحذف",
            "delete_confirmation": "هل أنت متأكد من الحذف؟",
            "operation_successful": "تمت العملية بنجاح",
            "operation_failed": "فشلت العملية",
            "invalid_data": "بيانات غير صحيحة",
            "required_field": "حقل مطلوب",
            "date": "التاريخ",
            "time": "الوقت",
            "name": "الاسم",
            "description": "الوصف",
            "notes": "ملاحظات",
            "status": "الحالة",
            "type": "النوع",
            "category": "الفئة",
            "quantity": "الكمية",
            "price": "السعر",
            "total": "المجموع",
            "subtotal": "المجموع الفرعي",
            "discount": "الخصم",
            "tax": "الضريبة",
            "amount": "المبلغ",
            "balance": "الرصيد",
            "phone": "الهاتف",
            "email": "البريد الإلكتروني",
            "address": "العنوان",
            "city": "المدينة",
            "country": "البلد",
            "company": "الشركة",
            "contact": "جهة الاتصال",
            "active": "نشط",
            "inactive": "غير نشط",
            "available": "متاح",
            "unavailable": "غير متاح",
            "pending": "معلق",
            "completed": "مكتمل",
            "cancelled": "ملغي",
            "approved": "موافق عليه",
            "rejected": "مرفوض",
            "new": "جديد",
            "used": "مستعمل",
            "damaged": "تالف",
            "maintenance": "صيانة",
            "repair": "إصلاح",
            "replacement": "استبدال",
            "cash": "نقدي",
            "credit": "آجل",
            "bank_transfer": "تحويل بنكي",
            "check": "شيك",
            "credit_card": "بطاقة ائتمان",
            "paid": "مدفوع",
            "unpaid": "غير مدفوع",
            "partial": "جزئي",
            "overdue": "متأخر",
            "invoice": "فاتورة",
            "receipt": "إيصال",
            "voucher": "سند",
            "statement": "كشف حساب",
            "report": "تقرير",
            "summary": "ملخص",
            "details": "تفاصيل",
            "statistics": "إحصائيات",
            "analytics": "تحليلات",
            "chart": "مخطط",
            "graph": "رسم بياني",
            "table": "جدول",
            "list": "قائمة",
            "view": "عرض",
            "preview": "معاينة",
            "full_screen": "ملء الشاشة",
            "minimize": "تصغير",
            "maximize": "تكبير",
            "restore": "استعادة",
            "language": "اللغة",
            "theme": "المظهر",
            "font": "الخط",
            "color": "اللون",
            "size": "الحجم",
            "width": "العرض",
            "height": "الارتفاع",
            "length": "الطول",
            "weight": "الوزن",
            "volume": "الحجم",
            "density": "الكثافة",
            "hardness": "الصلابة",
            "quality": "الجودة",
            "grade": "الدرجة",
            "standard": "معيار",
            "specification": "مواصفة",
            "requirement": "متطلب",
            "condition": "شرط",
            "criteria": "معايير",
            "measurement": "قياس"
        }
