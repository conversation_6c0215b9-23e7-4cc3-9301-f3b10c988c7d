from PyQt5.QtWidgets import (QWidget, QVBoxLayout, QHBoxLayout, QPushButton, 
                            QTableWidget, QTableWidgetItem, QLineEdit, QLabel,
                            QFormLayout, QDialog, QMessageBox, QComboBox,
                            QGroupBox, QDateEdit, QTextEdit, QHeaderView,
                            QSpinBox, QDoubleSpinBox, QTabWidget)
from PyQt5.QtCore import Qt, QDate
from PyQt5.QtGui import QFont, QPixmap
import sqlite3

class BlockDialog(QDialog):
    def __init__(self, parent=None, block_data=None, translator=None):
        super().__init__(parent)
        self.translator = translator
        self.block_data = block_data
        self.init_ui()
        
        if block_data:
            self.populate_fields()
            
    def init_ui(self):
        self.setWindowTitle("إضافة/تعديل كتلة حجرية")
        self.setModal(True)
        self.resize(600, 800)
        
        layout = QVBoxLayout()
        
        # Create tabs
        tab_widget = QTabWidget()
        
        # Basic Info Tab
        basic_tab = QWidget()
        basic_layout = QFormLayout()
        
        self.block_id_edit = QLineEdit()
        self.quarry_location_edit = QLineEdit()
        self.stone_type_combo = QComboBox()
        self.stone_type_combo.addItems([
            "جرانيت", "رخام", "حجر جيري", "حجر رملي", 
            "بازلت", "كوارتز", "أونيكس", "ترافرتين"
        ])
        
        self.color_edit = QLineEdit()
        self.grade_combo = QComboBox()
        self.grade_combo.addItems(["A+", "A", "B+", "B", "C"])
        
        self.length_edit = QDoubleSpinBox()
        self.length_edit.setMaximum(9999)
        self.length_edit.setSuffix(" سم")
        
        self.width_edit = QDoubleSpinBox()
        self.width_edit.setMaximum(9999)
        self.width_edit.setSuffix(" سم")
        
        self.height_edit = QDoubleSpinBox()
        self.height_edit.setMaximum(9999)
        self.height_edit.setSuffix(" سم")
        
        self.weight_edit = QDoubleSpinBox()
        self.weight_edit.setMaximum(99999)
        self.weight_edit.setSuffix(" كغ")
        
        self.volume_edit = QDoubleSpinBox()
        self.volume_edit.setMaximum(99999)
        self.volume_edit.setSuffix(" م³")
        
        basic_layout.addRow("رقم الكتلة:", self.block_id_edit)
        basic_layout.addRow("موقع المقلع:", self.quarry_location_edit)
        basic_layout.addRow("نوع الحجر:", self.stone_type_combo)
        basic_layout.addRow("اللون:", self.color_edit)
        basic_layout.addRow("الدرجة:", self.grade_combo)
        basic_layout.addRow("الطول:", self.length_edit)
        basic_layout.addRow("العرض:", self.width_edit)
        basic_layout.addRow("الارتفاع:", self.height_edit)
        basic_layout.addRow("الوزن:", self.weight_edit)
        basic_layout.addRow("الحجم:", self.volume_edit)
        
        basic_tab.setLayout(basic_layout)
        tab_widget.addTab(basic_tab, "المعلومات الأساسية")
        
        # Commercial Info Tab
        commercial_tab = QWidget()
        commercial_layout = QFormLayout()
        
        self.purchase_price_edit = QDoubleSpinBox()
        self.purchase_price_edit.setMaximum(999999)
        self.purchase_price_edit.setSuffix(" ريال")
        
        self.selling_price_edit = QDoubleSpinBox()
        self.selling_price_edit.setMaximum(999999)
        self.selling_price_edit.setSuffix(" ريال")
        
        self.status_combo = QComboBox()
        self.status_combo.addItems(["متاح", "محجوز", "مباع", "معيب", "قيد المعالجة"])
        
        self.purchase_date = QDateEdit()
        self.purchase_date.setDate(QDate.currentDate())
        self.purchase_date.setCalendarPopup(True)
        
        self.supplier_edit = QLineEdit()
        self.location_edit = QLineEdit()
        
        commercial_layout.addRow("سعر الشراء:", self.purchase_price_edit)
        commercial_layout.addRow("سعر البيع:", self.selling_price_edit)
        commercial_layout.addRow("الحالة:", self.status_combo)
        commercial_layout.addRow("تاريخ الشراء:", self.purchase_date)
        commercial_layout.addRow("المورد:", self.supplier_edit)
        commercial_layout.addRow("الموقع في المستودع:", self.location_edit)
        
        commercial_tab.setLayout(commercial_layout)
        tab_widget.addTab(commercial_tab, "المعلومات التجارية")
        
        # Quality Info Tab
        quality_tab = QWidget()
        quality_layout = QFormLayout()
        
        self.hardness_edit = QDoubleSpinBox()
        self.hardness_edit.setMaximum(10)
        self.hardness_edit.setSuffix(" موس")
        
        self.density_edit = QDoubleSpinBox()
        self.density_edit.setMaximum(10)
        self.density_edit.setSuffix(" غ/سم³")
        
        self.porosity_edit = QDoubleSpinBox()
        self.porosity_edit.setMaximum(100)
        self.porosity_edit.setSuffix(" %")
        
        self.water_absorption_edit = QDoubleSpinBox()
        self.water_absorption_edit.setMaximum(100)
        self.water_absorption_edit.setSuffix(" %")
        
        self.compressive_strength_edit = QDoubleSpinBox()
        self.compressive_strength_edit.setMaximum(9999)
        self.compressive_strength_edit.setSuffix(" MPa")
        
        self.defects_edit = QTextEdit()
        self.notes_edit = QTextEdit()
        
        quality_layout.addRow("الصلابة:", self.hardness_edit)
        quality_layout.addRow("الكثافة:", self.density_edit)
        quality_layout.addRow("المسامية:", self.porosity_edit)
        quality_layout.addRow("امتصاص الماء:", self.water_absorption_edit)
        quality_layout.addRow("قوة الضغط:", self.compressive_strength_edit)
        quality_layout.addRow("العيوب:", self.defects_edit)
        quality_layout.addRow("ملاحظات:", self.notes_edit)
        
        quality_tab.setLayout(quality_layout)
        tab_widget.addTab(quality_tab, "معلومات الجودة")
        
        layout.addWidget(tab_widget)
        
        # Buttons
        button_layout = QHBoxLayout()
        self.save_btn = QPushButton("حفظ")
        self.cancel_btn = QPushButton("إلغاء")
        self.calculate_btn = QPushButton("حساب الحجم والوزن")
        
        self.save_btn.clicked.connect(self.accept)
        self.cancel_btn.clicked.connect(self.reject)
        self.calculate_btn.clicked.connect(self.calculate_volume_weight)
        
        button_layout.addWidget(self.save_btn)
        button_layout.addWidget(self.cancel_btn)
        button_layout.addWidget(self.calculate_btn)
        layout.addLayout(button_layout)
        
        self.setLayout(layout)
        
    def calculate_volume_weight(self):
        """Calculate volume and weight based on dimensions and stone type"""
        length = self.length_edit.value() / 100  # Convert to meters
        width = self.width_edit.value() / 100
        height = self.height_edit.value() / 100
        
        volume = length * width * height
        self.volume_edit.setValue(volume)
        
        # Stone density mapping (kg/m³)
        stone_densities = {
            "جرانيت": 2700,
            "رخام": 2650,
            "حجر جيري": 2400,
            "حجر رملي": 2200,
            "بازلت": 2900,
            "كوارتز": 2650,
            "أونيكس": 2700,
            "ترافرتين": 2300
        }
        
        stone_type = self.stone_type_combo.currentText()
        density = stone_densities.get(stone_type, 2500)
        weight = volume * density
        
        self.weight_edit.setValue(weight)
        
    def populate_fields(self):
        if self.block_data:
            self.block_id_edit.setText(str(self.block_data.get('block_id', '')))
            self.quarry_location_edit.setText(str(self.block_data.get('quarry_location', '')))
            self.color_edit.setText(str(self.block_data.get('color', '')))
            self.length_edit.setValue(float(self.block_data.get('length', 0)))
            self.width_edit.setValue(float(self.block_data.get('width', 0)))
            self.height_edit.setValue(float(self.block_data.get('height', 0)))
            self.weight_edit.setValue(float(self.block_data.get('weight', 0)))
            self.volume_edit.setValue(float(self.block_data.get('volume', 0)))
            self.purchase_price_edit.setValue(float(self.block_data.get('purchase_price', 0)))
            self.selling_price_edit.setValue(float(self.block_data.get('selling_price', 0)))
            self.supplier_edit.setText(str(self.block_data.get('supplier', '')))
            self.location_edit.setText(str(self.block_data.get('location', '')))
            self.hardness_edit.setValue(float(self.block_data.get('hardness', 0)))
            self.density_edit.setValue(float(self.block_data.get('density', 0)))
            self.porosity_edit.setValue(float(self.block_data.get('porosity', 0)))
            self.water_absorption_edit.setValue(float(self.block_data.get('water_absorption', 0)))
            self.compressive_strength_edit.setValue(float(self.block_data.get('compressive_strength', 0)))
            self.defects_edit.setPlainText(str(self.block_data.get('defects', '')))
            self.notes_edit.setPlainText(str(self.block_data.get('notes', '')))
            
    def get_data(self):
        return {
            'block_id': self.block_id_edit.text(),
            'quarry_location': self.quarry_location_edit.text(),
            'stone_type': self.stone_type_combo.currentText(),
            'color': self.color_edit.text(),
            'grade': self.grade_combo.currentText(),
            'length': self.length_edit.value(),
            'width': self.width_edit.value(),
            'height': self.height_edit.value(),
            'weight': self.weight_edit.value(),
            'volume': self.volume_edit.value(),
            'purchase_price': self.purchase_price_edit.value(),
            'selling_price': self.selling_price_edit.value(),
            'status': self.status_combo.currentText(),
            'purchase_date': self.purchase_date.date().toString('yyyy-MM-dd'),
            'supplier': self.supplier_edit.text(),
            'location': self.location_edit.text(),
            'hardness': self.hardness_edit.value(),
            'density': self.density_edit.value(),
            'porosity': self.porosity_edit.value(),
            'water_absorption': self.water_absorption_edit.value(),
            'compressive_strength': self.compressive_strength_edit.value(),
            'defects': self.defects_edit.toPlainText(),
            'notes': self.notes_edit.toPlainText()
        }

class BlocksModule(QWidget):
    def __init__(self, db_manager, translator, parent=None):
        super().__init__(parent)
        self.db_manager = db_manager
        self.translator = translator
        self.init_ui()
        self.load_blocks()
        
    def init_ui(self):
        layout = QVBoxLayout()
        
        # Header
        header_layout = QHBoxLayout()
        title_label = QLabel("إدارة الكتل الحجرية")
        title_label.setFont(QFont("Arial", 16, QFont.Bold))
        header_layout.addWidget(title_label)
        header_layout.addStretch()
        
        # Search and filters
        self.search_edit = QLineEdit()
        self.search_edit.setPlaceholderText("البحث في الكتل...")
        self.search_edit.textChanged.connect(self.search_blocks)
        
        self.stone_type_filter = QComboBox()
        self.stone_type_filter.addItems([
            "الكل", "جرانيت", "رخام", "حجر جيري", "حجر رملي", 
            "بازلت", "كوارتز", "أونيكس", "ترافرتين"
        ])
        self.stone_type_filter.currentTextChanged.connect(self.filter_blocks)
        
        self.status_filter = QComboBox()
        self.status_filter.addItems(["الكل", "متاح", "محجوز", "مباع", "معيب", "قيد المعالجة"])
        self.status_filter.currentTextChanged.connect(self.filter_blocks)
        
        header_layout.addWidget(QLabel("البحث:"))
        header_layout.addWidget(self.search_edit)
        header_layout.addWidget(QLabel("النوع:"))
        header_layout.addWidget(self.stone_type_filter)
        header_layout.addWidget(QLabel("الحالة:"))
        header_layout.addWidget(self.status_filter)
        
        layout.addLayout(header_layout)
        
        # Buttons
        button_layout = QHBoxLayout()
        self.add_btn = QPushButton("إضافة كتلة جديدة")
        self.edit_btn = QPushButton("تعديل")
        self.delete_btn = QPushButton("حذف")
        self.refresh_btn = QPushButton("تحديث")
        self.reserve_btn = QPushButton("حجز")
        self.sell_btn = QPushButton("بيع")
        
        self.add_btn.clicked.connect(self.add_block)
        self.edit_btn.clicked.connect(self.edit_block)
        self.delete_btn.clicked.connect(self.delete_block)
        self.refresh_btn.clicked.connect(self.load_blocks)
        self.reserve_btn.clicked.connect(self.reserve_block)
        self.sell_btn.clicked.connect(self.sell_block)
        
        button_layout.addWidget(self.add_btn)
        button_layout.addWidget(self.edit_btn)
        button_layout.addWidget(self.delete_btn)
        button_layout.addWidget(self.refresh_btn)
        button_layout.addWidget(self.reserve_btn)
        button_layout.addWidget(self.sell_btn)
        button_layout.addStretch()
        
        layout.addLayout(button_layout)
        
        # Table
        self.table = QTableWidget()
        self.table.setColumnCount(11)
        self.table.setHorizontalHeaderLabels([
            "الرقم", "رقم الكتلة", "نوع الحجر", "اللون", "الأبعاد", 
            "الوزن", "الحجم", "سعر الشراء", "سعر البيع", "الحالة", "الموقع"
        ])
        
        # Set table properties
        self.table.setSelectionBehavior(QTableWidget.SelectRows)
        self.table.setAlternatingRowColors(True)
        self.table.horizontalHeader().setStretchLastSection(True)
        self.table.horizontalHeader().setSectionResizeMode(QHeaderView.Stretch)
        
        layout.addWidget(self.table)
        
        # Statistics
        stats_layout = QHBoxLayout()
        self.total_blocks_label = QLabel("إجمالي الكتل: 0")
        self.available_blocks_label = QLabel("المتاحة: 0")
        self.reserved_blocks_label = QLabel("المحجوزة: 0")
        self.sold_blocks_label = QLabel("المباعة: 0")
        self.total_value_label = QLabel("القيمة الإجمالية: 0 ريال")
        
        stats_layout.addWidget(self.total_blocks_label)
        stats_layout.addWidget(self.available_blocks_label)
        stats_layout.addWidget(self.reserved_blocks_label)
        stats_layout.addWidget(self.sold_blocks_label)
        stats_layout.addWidget(self.total_value_label)
        stats_layout.addStretch()
        
        layout.addLayout(stats_layout)
        
        self.setLayout(layout)
        
    def load_blocks(self):
        try:
            conn = self.db_manager.get_connection()
            cursor = conn.cursor()
            
            cursor.execute("""
                SELECT id, block_id, stone_type, color, length, width, height,
                       weight, volume, purchase_price, selling_price, status, location
                FROM blocks 
                ORDER BY created_at DESC
            """)
            
            blocks = cursor.fetchall()
            
            self.table.setRowCount(len(blocks))
            
            for row, block in enumerate(blocks):
                for col, value in enumerate(block):
                    if col == 4:  # dimensions
                        dimensions = f"{block[4]}×{block[5]}×{block[6]} سم"
                        item = QTableWidgetItem(dimensions)
                    elif col == 7:  # weight
                        value = f"{value:,.1f} كغ"
                        item = QTableWidgetItem(str(value))
                    elif col == 8:  # volume
                        value = f"{value:.2f} م³"
                        item = QTableWidgetItem(str(value))
                    elif col in [9, 10]:  # prices
                        value = f"{value:,.2f} ريال"
                        item = QTableWidgetItem(str(value))
                    elif col in [5, 6]:  # skip width and height as they're in dimensions
                        continue
                    else:
                        item = QTableWidgetItem(str(value))
                    
                    item.setTextAlignment(Qt.AlignCenter)
                    
                    # Color coding for status
                    if col == 11:  # status column
                        if value == "متاح":
                            item.setBackground(Qt.green)
                        elif value == "محجوز":
                            item.setBackground(Qt.yellow)
                        elif value == "مباع":
                            item.setBackground(Qt.blue)
                        elif value == "معيب":
                            item.setBackground(Qt.red)
                    
                    # Adjust column index for skipped columns
                    display_col = col if col <= 4 else col - 2
                    if col > 6:
                        display_col = col - 2
                    
                    self.table.setItem(row, display_col, item)
            
            # Update statistics
            self.update_statistics(blocks)
            
            conn.close()
            
        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"خطأ في تحميل الكتل: {str(e)}")
            
    def update_statistics(self, blocks):
        total = len(blocks)
        available = sum(1 for block in blocks if block[11] == "متاح")
        reserved = sum(1 for block in blocks if block[11] == "محجوز")
        sold = sum(1 for block in blocks if block[11] == "مباع")
        total_value = sum(block[10] for block in blocks if block[11] in ["متاح", "محجوز"])
        
        self.total_blocks_label.setText(f"إجمالي الكتل: {total}")
        self.available_blocks_label.setText(f"المتاحة: {available}")
        self.reserved_blocks_label.setText(f"المحجوزة: {reserved}")
        self.sold_blocks_label.setText(f"المباعة: {sold}")
        self.total_value_label.setText(f"القيمة الإجمالية: {total_value:,.2f} ريال")
        
    def search_blocks(self):
        search_text = self.search_edit.text().lower()
        
        for row in range(self.table.rowCount()):
            show_row = False
            for col in range(self.table.columnCount()):
                item = self.table.item(row, col)
                if item and search_text in item.text().lower():
                    show_row = True
                    break
            self.table.setRowHidden(row, not show_row)
            
    def filter_blocks(self):
        stone_type_filter = self.stone_type_filter.currentText()
        status_filter = self.status_filter.currentText()
        
        for row in range(self.table.rowCount()):
            show_row = True
            
            # Filter by stone type
            if stone_type_filter != "الكل":
                stone_type_item = self.table.item(row, 2)  # stone type column
                if stone_type_item and stone_type_item.text() != stone_type_filter:
                    show_row = False
            
            # Filter by status
            if status_filter != "الكل" and show_row:
                status_item = self.table.item(row, 9)  # status column
                if status_item and status_item.text() != status_filter:
                    show_row = False
            
            self.table.setRowHidden(row, not show_row)
            
    def add_block(self):
        dialog = BlockDialog(self, translator=self.translator)
        if dialog.exec_() == QDialog.Accepted:
            data = dialog.get_data()
            self.save_block(data)
            
    def edit_block(self):
        current_row = self.table.currentRow()
        if current_row < 0:
            QMessageBox.warning(self, "تحذير", "يرجى اختيار كتلة للتعديل")
            return
            
        block_id = self.table.item(current_row, 0).text()
        block_data = self.get_block_data(block_id)
        
        dialog = BlockDialog(self, block_data, self.translator)
        if dialog.exec_() == QDialog.Accepted:
            data = dialog.get_data()
            self.update_block(block_id, data)
            
    def delete_block(self):
        current_row = self.table.currentRow()
        if current_row < 0:
            QMessageBox.warning(self, "تحذير", "يرجى اختيار كتلة للحذف")
            return
            
        block_id = self.table.item(current_row, 1).text()
        reply = QMessageBox.question(
            self, "تأكيد الحذف", 
            f"هل أنت متأكد من حذف الكتلة '{block_id}'؟",
            QMessageBox.Yes | QMessageBox.No
        )
        
        if reply == QMessageBox.Yes:
            db_id = self.table.item(current_row, 0).text()
            self.remove_block(db_id)
            
    def reserve_block(self):
        current_row = self.table.currentRow()
        if current_row < 0:
            QMessageBox.warning(self, "تحذير", "يرجى اختيار كتلة للحجز")
            return
            
        block_id = self.table.item(current_row, 0).text()
        self.update_block_status(block_id, "محجوز")
        
    def sell_block(self):
        current_row = self.table.currentRow()
        if current_row < 0:
            QMessageBox.warning(self, "تحذير", "يرجى اختيار كتلة للبيع")
            return
            
        block_id = self.table.item(current_row, 0).text()
        self.update_block_status(block_id, "مباع")
        
    def save_block(self, data):
        try:
            conn = self.db_manager.get_connection()
            cursor = conn.cursor()
            
            cursor.execute("""
                INSERT INTO blocks (block_id, quarry_location, stone_type, color, grade,
                                  length, width, height, weight, volume, purchase_price,
                                  selling_price, status, purchase_date, supplier, location,
                                  hardness, density, porosity, water_absorption,
                                  compressive_strength, defects, notes)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            """, (
                data['block_id'], data['quarry_location'], data['stone_type'],
                data['color'], data['grade'], data['length'], data['width'],
                data['height'], data['weight'], data['volume'], data['purchase_price'],
                data['selling_price'], data['status'], data['purchase_date'],
                data['supplier'], data['location'], data['hardness'], data['density'],
                data['porosity'], data['water_absorption'], data['compressive_strength'],
                data['defects'], data['notes']
            ))
            
            conn.commit()
            conn.close()
            
            QMessageBox.information(self, "نجح", "تم إضافة الكتلة بنجاح")
            self.load_blocks()
            
        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"خطأ في إضافة الكتلة: {str(e)}")
            
    def update_block(self, block_id, data):
        try:
            conn = self.db_manager.get_connection()
            cursor = conn.cursor()
            
            cursor.execute("""
                UPDATE blocks 
                SET block_id=?, quarry_location=?, stone_type=?, color=?, grade=?,
                    length=?, width=?, height=?, weight=?, volume=?, purchase_price=?,
                    selling_price=?, status=?, purchase_date=?, supplier=?, location=?,
                    hardness=?, density=?, porosity=?, water_absorption=?,
                    compressive_strength=?, defects=?, notes=?
                WHERE id=?
            """, (
                data['block_id'], data['quarry_location'], data['stone_type'],
                data['color'], data['grade'], data['length'], data['width'],
                data['height'], data['weight'], data['volume'], data['purchase_price'],
                data['selling_price'], data['status'], data['purchase_date'],
                data['supplier'], data['location'], data['hardness'], data['density'],
                data['porosity'], data['water_absorption'], data['compressive_strength'],
                data['defects'], data['notes'], block_id
            ))
            
            conn.commit()
            conn.close()
            
            QMessageBox.information(self, "نجح", "تم تحديث الكتلة بنجاح")
            self.load_blocks()
            
        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"خطأ في تحديث الكتلة: {str(e)}")
            
    def update_block_status(self, block_id, status):
        try:
            conn = self.db_manager.get_connection()
            cursor = conn.cursor()
            
            cursor.execute("UPDATE blocks SET status=? WHERE id=?", (status, block_id))
            
            conn.commit()
            conn.close()
            
            QMessageBox.information(self, "نجح", f"تم تحديث حالة الكتلة إلى '{status}'")
            self.load_blocks()
            
        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"خطأ في تحديث حالة الكتلة: {str(e)}")
            
    def remove_block(self, block_id):
        try:
            conn = self.db_manager.get_connection()
            cursor = conn.cursor()
            
            cursor.execute("DELETE FROM blocks WHERE id=?", (block_id,))
            
            conn.commit()
            conn.close()
            
            QMessageBox.information(self, "نجح", "تم حذف الكتلة بنجاح")
            self.load_blocks()
            
        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"خطأ في حذف الكتلة: {str(e)}")
            
    def get_block_data(self, block_id):
        try:
            conn = self.db_manager.get_connection()
            cursor = conn.cursor()
            
            cursor.execute("""
                SELECT block_id, quarry_location, stone_type, color, grade,
                       length, width, height, weight, volume, purchase_price,
                       selling_price, status, purchase_date, supplier, location,
                       hardness, density, porosity, water_absorption,
                       compressive_strength, defects, notes
                FROM blocks WHERE id=?
            """, (block_id,))
            
            result = cursor.fetchone()
            conn.close()
            
            if result:
                return {
                    'block_id': result[0],
                    'quarry_location': result[1],
                    'stone_type': result[2],
                    'color': result[3],
                    'grade': result[4],
                    'length': result[5],
                    'width': result[6],
                    'height': result[7],
                    'weight': result[8],
                    'volume': result[9],
                    'purchase_price': result[10],
                    'selling_price': result[11],
                    'status': result[12],
                    'purchase_date': result[13],
                    'supplier': result[14],
                    'location': result[15],
                    'hardness': result[16],
                    'density': result[17],
                    'porosity': result[18],
                    'water_absorption': result[19],
                    'compressive_strength': result[20],
                    'defects': result[21],
                    'notes': result[22]
                }
            return {}
            
        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"خطأ في جلب بيانات الكتلة: {str(e)}")
            return {}
