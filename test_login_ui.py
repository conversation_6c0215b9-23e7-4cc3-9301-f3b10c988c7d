#!/usr/bin/env python3
"""
Test script for login UI functionality
"""

import sys
from PyQt5.QtWidgets import QApplication, QMessageBox
from PyQt5.QtCore import QTimer
from database.db_manager import DatabaseManager
from ui.login_window import <PERSON><PERSON>Window
from utils.translator import Translator
from utils.font_manager import FontManager
from utils.locale_manager import LocaleManager
from utils.cultural_manager import CulturalManager

def test_login_ui():
    """Test login UI functionality"""
    app = QApplication(sys.argv)
    
    # Initialize managers
    db_manager = DatabaseManager()
    translator = Translator()
    locale_manager = LocaleManager()
    cultural_manager = CulturalManager()
    font_manager = FontManager()
    
    # Create login window
    login_window = LoginWindow(db_manager, translator, font_manager, 
                              locale_manager, cultural_manager)
    
    def on_login_success(user_data):
        """Handle successful login"""
        msg = QMessageBox()
        msg.setWindowTitle("Login Successful")
        msg.setText(f"Welcome {user_data['full_name']}!\n"
                   f"Username: {user_data['username']}\n"
                   f"Role: {user_data['role']}")
        msg.exec_()
        app.quit()
    
    # Connect login success signal
    login_window.login_successful.connect(on_login_success)
    
    # Auto-fill login credentials for testing
    def auto_fill():
        login_window.username_input.setText("admin")
        login_window.password_input.setText("admin123")
        print("Auto-filled login credentials:")
        print("Username: admin")
        print("Password: admin123")
        print("Click 'Login' button to test...")
    
    # Auto-fill after 1 second
    QTimer.singleShot(1000, auto_fill)
    
    # Show login window
    login_window.show()
    
    return app.exec_()

if __name__ == "__main__":
    print("=== Login UI Test ===")
    print("This will open the login window with auto-filled credentials.")
    print("Click the 'Login' button to test the functionality.")
    
    sys.exit(test_login_ui())
