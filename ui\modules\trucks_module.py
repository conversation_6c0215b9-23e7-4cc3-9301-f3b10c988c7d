from PyQt5.QtWidgets import (QWidget, QVBoxLayout, QHBoxLayout, QPushButton, 
                            QTableWidget, QTableWidgetItem, QLineEdit, QLabel,
                            QFormLayout, QDialog, QMessageBox, QComboBox,
                            QGroupBox, QDateEdit, QTextEdit, QHeaderView,
                            QSpinBox, QDoubleSpinBox)
from PyQt5.QtCore import Qt, QDate
from PyQt5.QtGui import QFont
import sqlite3

class TruckDialog(QDialog):
    def __init__(self, parent=None, truck_data=None, translator=None):
        super().__init__(parent)
        self.translator = translator
        self.truck_data = truck_data
        self.init_ui()
        
        if truck_data:
            self.populate_fields()
            
    def init_ui(self):
        self.setWindowTitle("إضافة/تعديل شاحنة")
        self.setModal(True)
        self.resize(500, 700)
        
        layout = QVBoxLayout()
        
        # Truck form
        form_layout = QFormLayout()
        
        self.plate_number_edit = QLineEdit()
        self.driver_name_edit = QLineEdit()
        self.driver_phone_edit = QLineEdit()
        self.truck_type_combo = QComboBox()
        self.truck_type_combo.addItems(["شاحنة صغيرة", "شاحنة متوسطة", "شاحنة كبيرة", "مقطورة"])
        
        self.capacity_edit = QDoubleSpinBox()
        self.capacity_edit.setMaximum(999999)
        self.capacity_edit.setSuffix(" طن")
        
        self.status_combo = QComboBox()
        self.status_combo.addItems(["متاحة", "في رحلة", "صيانة", "خارج الخدمة"])
        
        self.insurance_date = QDateEdit()
        self.insurance_date.setDate(QDate.currentDate())
        self.insurance_date.setCalendarPopup(True)
        
        self.license_date = QDateEdit()
        self.license_date.setDate(QDate.currentDate())
        self.license_date.setCalendarPopup(True)
        
        self.maintenance_date = QDateEdit()
        self.maintenance_date.setDate(QDate.currentDate())
        self.maintenance_date.setCalendarPopup(True)
        
        self.fuel_consumption_edit = QDoubleSpinBox()
        self.fuel_consumption_edit.setMaximum(999)
        self.fuel_consumption_edit.setSuffix(" لتر/100كم")
        
        self.notes_edit = QTextEdit()
        
        form_layout.addRow("رقم اللوحة:", self.plate_number_edit)
        form_layout.addRow("اسم السائق:", self.driver_name_edit)
        form_layout.addRow("هاتف السائق:", self.driver_phone_edit)
        form_layout.addRow("نوع الشاحنة:", self.truck_type_combo)
        form_layout.addRow("الحمولة:", self.capacity_edit)
        form_layout.addRow("الحالة:", self.status_combo)
        form_layout.addRow("تاريخ انتهاء التأمين:", self.insurance_date)
        form_layout.addRow("تاريخ انتهاء الرخصة:", self.license_date)
        form_layout.addRow("تاريخ الصيانة القادمة:", self.maintenance_date)
        form_layout.addRow("استهلاك الوقود:", self.fuel_consumption_edit)
        form_layout.addRow("ملاحظات:", self.notes_edit)
        
        layout.addLayout(form_layout)
        
        # Buttons
        button_layout = QHBoxLayout()
        self.save_btn = QPushButton("حفظ")
        self.cancel_btn = QPushButton("إلغاء")
        
        self.save_btn.clicked.connect(self.accept)
        self.cancel_btn.clicked.connect(self.reject)
        
        button_layout.addWidget(self.save_btn)
        button_layout.addWidget(self.cancel_btn)
        layout.addLayout(button_layout)
        
        self.setLayout(layout)
        
    def populate_fields(self):
        if self.truck_data:
            self.plate_number_edit.setText(str(self.truck_data.get('plate_number', '')))
            self.driver_name_edit.setText(str(self.truck_data.get('driver_name', '')))
            self.driver_phone_edit.setText(str(self.truck_data.get('driver_phone', '')))
            self.capacity_edit.setValue(float(self.truck_data.get('capacity', 0)))
            self.fuel_consumption_edit.setValue(float(self.truck_data.get('fuel_consumption', 0)))
            self.notes_edit.setPlainText(str(self.truck_data.get('notes', '')))
            
    def get_data(self):
        return {
            'plate_number': self.plate_number_edit.text(),
            'driver_name': self.driver_name_edit.text(),
            'driver_phone': self.driver_phone_edit.text(),
            'truck_type': self.truck_type_combo.currentText(),
            'capacity': self.capacity_edit.value(),
            'status': self.status_combo.currentText(),
            'insurance_expiry': self.insurance_date.date().toString('yyyy-MM-dd'),
            'license_expiry': self.license_date.date().toString('yyyy-MM-dd'),
            'next_maintenance': self.maintenance_date.date().toString('yyyy-MM-dd'),
            'fuel_consumption': self.fuel_consumption_edit.value(),
            'notes': self.notes_edit.toPlainText()
        }

class TrucksModule(QWidget):
    def __init__(self, db_manager, translator, parent=None):
        super().__init__(parent)
        self.db_manager = db_manager
        self.translator = translator
        self.init_ui()
        self.load_trucks()
        
    def init_ui(self):
        layout = QVBoxLayout()
        
        # Header
        header_layout = QHBoxLayout()
        title_label = QLabel("إدارة الشاحنات")
        title_label.setFont(QFont("Arial", 16, QFont.Bold))
        header_layout.addWidget(title_label)
        header_layout.addStretch()
        
        # Search and filter
        self.search_edit = QLineEdit()
        self.search_edit.setPlaceholderText("البحث في الشاحنات...")
        self.search_edit.textChanged.connect(self.search_trucks)
        
        self.status_filter = QComboBox()
        self.status_filter.addItems(["الكل", "متاحة", "في رحلة", "صيانة", "خارج الخدمة"])
        self.status_filter.currentTextChanged.connect(self.filter_trucks)
        
        header_layout.addWidget(QLabel("البحث:"))
        header_layout.addWidget(self.search_edit)
        header_layout.addWidget(QLabel("الحالة:"))
        header_layout.addWidget(self.status_filter)
        
        layout.addLayout(header_layout)
        
        # Buttons
        button_layout = QHBoxLayout()
        self.add_btn = QPushButton("إضافة شاحنة جديدة")
        self.edit_btn = QPushButton("تعديل")
        self.delete_btn = QPushButton("حذف")
        self.refresh_btn = QPushButton("تحديث")
        self.maintenance_btn = QPushButton("جدولة صيانة")
        
        self.add_btn.clicked.connect(self.add_truck)
        self.edit_btn.clicked.connect(self.edit_truck)
        self.delete_btn.clicked.connect(self.delete_truck)
        self.refresh_btn.clicked.connect(self.load_trucks)
        self.maintenance_btn.clicked.connect(self.schedule_maintenance)
        
        button_layout.addWidget(self.add_btn)
        button_layout.addWidget(self.edit_btn)
        button_layout.addWidget(self.delete_btn)
        button_layout.addWidget(self.refresh_btn)
        button_layout.addWidget(self.maintenance_btn)
        button_layout.addStretch()
        
        layout.addLayout(button_layout)
        
        # Table
        self.table = QTableWidget()
        self.table.setColumnCount(9)
        self.table.setHorizontalHeaderLabels([
            "الرقم", "رقم اللوحة", "السائق", "النوع", "الحمولة", 
            "الحالة", "انتهاء التأمين", "انتهاء الرخصة", "الصيانة القادمة"
        ])
        
        # Set table properties
        self.table.setSelectionBehavior(QTableWidget.SelectRows)
        self.table.setAlternatingRowColors(True)
        self.table.horizontalHeader().setStretchLastSection(True)
        self.table.horizontalHeader().setSectionResizeMode(QHeaderView.Stretch)
        
        layout.addWidget(self.table)
        
        # Statistics
        stats_layout = QHBoxLayout()
        self.total_trucks_label = QLabel("إجمالي الشاحنات: 0")
        self.available_trucks_label = QLabel("الشاحنات المتاحة: 0")
        self.in_trip_trucks_label = QLabel("في رحلة: 0")
        self.maintenance_trucks_label = QLabel("في الصيانة: 0")
        
        stats_layout.addWidget(self.total_trucks_label)
        stats_layout.addWidget(self.available_trucks_label)
        stats_layout.addWidget(self.in_trip_trucks_label)
        stats_layout.addWidget(self.maintenance_trucks_label)
        stats_layout.addStretch()
        
        layout.addLayout(stats_layout)
        
        self.setLayout(layout)
        
    def load_trucks(self):
        try:
            conn = self.db_manager.get_connection()
            cursor = conn.cursor()
            
            cursor.execute("""
                SELECT id, plate_number, driver_name, truck_type, capacity, 
                       status, insurance_expiry, license_expiry, next_maintenance
                FROM trucks 
                ORDER BY plate_number
            """)
            
            trucks = cursor.fetchall()
            
            self.table.setRowCount(len(trucks))
            
            for row, truck in enumerate(trucks):
                for col, value in enumerate(truck):
                    if col == 4:  # capacity
                        value = f"{value} طن"
                    elif col in [6, 7, 8]:  # dates
                        value = str(value)[:10] if value else ""
                    
                    item = QTableWidgetItem(str(value))
                    item.setTextAlignment(Qt.AlignCenter)
                    
                    # Color coding for status
                    if col == 5:  # status column
                        if value == "متاحة":
                            item.setBackground(Qt.green)
                        elif value == "في رحلة":
                            item.setBackground(Qt.yellow)
                        elif value == "صيانة":
                            item.setBackground(Qt.red)
                    
                    self.table.setItem(row, col, item)
            
            # Update statistics
            self.update_statistics(trucks)
            
            conn.close()
            
        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"خطأ في تحميل الشاحنات: {str(e)}")
            
    def update_statistics(self, trucks):
        total = len(trucks)
        available = sum(1 for truck in trucks if truck[5] == "متاحة")
        in_trip = sum(1 for truck in trucks if truck[5] == "في رحلة")
        maintenance = sum(1 for truck in trucks if truck[5] == "صيانة")
        
        self.total_trucks_label.setText(f"إجمالي الشاحنات: {total}")
        self.available_trucks_label.setText(f"الشاحنات المتاحة: {available}")
        self.in_trip_trucks_label.setText(f"في رحلة: {in_trip}")
        self.maintenance_trucks_label.setText(f"في الصيانة: {maintenance}")
        
    def search_trucks(self):
        search_text = self.search_edit.text().lower()
        
        for row in range(self.table.rowCount()):
            show_row = False
            for col in range(self.table.columnCount()):
                item = self.table.item(row, col)
                if item and search_text in item.text().lower():
                    show_row = True
                    break
            self.table.setRowHidden(row, not show_row)
            
    def filter_trucks(self):
        status_filter = self.status_filter.currentText()
        
        for row in range(self.table.rowCount()):
            if status_filter == "الكل":
                self.table.setRowHidden(row, False)
            else:
                status_item = self.table.item(row, 5)  # status column
                if status_item:
                    show_row = status_item.text() == status_filter
                    self.table.setRowHidden(row, not show_row)
                    
    def add_truck(self):
        dialog = TruckDialog(self, translator=self.translator)
        if dialog.exec_() == QDialog.Accepted:
            data = dialog.get_data()
            self.save_truck(data)
            
    def edit_truck(self):
        current_row = self.table.currentRow()
        if current_row < 0:
            QMessageBox.warning(self, "تحذير", "يرجى اختيار شاحنة للتعديل")
            return
            
        truck_id = self.table.item(current_row, 0).text()
        truck_data = self.get_truck_data(truck_id)
        
        dialog = TruckDialog(self, truck_data, self.translator)
        if dialog.exec_() == QDialog.Accepted:
            data = dialog.get_data()
            self.update_truck(truck_id, data)
            
    def delete_truck(self):
        current_row = self.table.currentRow()
        if current_row < 0:
            QMessageBox.warning(self, "تحذير", "يرجى اختيار شاحنة للحذف")
            return
            
        plate_number = self.table.item(current_row, 1).text()
        reply = QMessageBox.question(
            self, "تأكيد الحذف", 
            f"هل أنت متأكد من حذف الشاحنة '{plate_number}'؟",
            QMessageBox.Yes | QMessageBox.No
        )
        
        if reply == QMessageBox.Yes:
            truck_id = self.table.item(current_row, 0).text()
            self.remove_truck(truck_id)
            
    def schedule_maintenance(self):
        current_row = self.table.currentRow()
        if current_row < 0:
            QMessageBox.warning(self, "تحذير", "يرجى اختيار شاحنة لجدولة الصيانة")
            return
            
        truck_id = self.table.item(current_row, 0).text()
        self.update_truck_status(truck_id, "صيانة")
        
    def save_truck(self, data):
        try:
            conn = self.db_manager.get_connection()
            cursor = conn.cursor()
            
            cursor.execute("""
                INSERT INTO trucks (plate_number, driver_name, driver_phone, truck_type,
                                  capacity, status, insurance_expiry, license_expiry,
                                  next_maintenance, fuel_consumption, notes)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            """, (
                data['plate_number'], data['driver_name'], data['driver_phone'],
                data['truck_type'], data['capacity'], data['status'],
                data['insurance_expiry'], data['license_expiry'], data['next_maintenance'],
                data['fuel_consumption'], data['notes']
            ))
            
            conn.commit()
            conn.close()
            
            QMessageBox.information(self, "نجح", "تم إضافة الشاحنة بنجاح")
            self.load_trucks()
            
        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"خطأ في إضافة الشاحنة: {str(e)}")
            
    def update_truck(self, truck_id, data):
        try:
            conn = self.db_manager.get_connection()
            cursor = conn.cursor()
            
            cursor.execute("""
                UPDATE trucks 
                SET plate_number=?, driver_name=?, driver_phone=?, truck_type=?,
                    capacity=?, status=?, insurance_expiry=?, license_expiry=?,
                    next_maintenance=?, fuel_consumption=?, notes=?
                WHERE id=?
            """, (
                data['plate_number'], data['driver_name'], data['driver_phone'],
                data['truck_type'], data['capacity'], data['status'],
                data['insurance_expiry'], data['license_expiry'], data['next_maintenance'],
                data['fuel_consumption'], data['notes'], truck_id
            ))
            
            conn.commit()
            conn.close()
            
            QMessageBox.information(self, "نجح", "تم تحديث الشاحنة بنجاح")
            self.load_trucks()
            
        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"خطأ في تحديث الشاحنة: {str(e)}")
            
    def update_truck_status(self, truck_id, status):
        try:
            conn = self.db_manager.get_connection()
            cursor = conn.cursor()
            
            cursor.execute("UPDATE trucks SET status=? WHERE id=?", (status, truck_id))
            
            conn.commit()
            conn.close()
            
            QMessageBox.information(self, "نجح", f"تم تحديث حالة الشاحنة إلى '{status}'")
            self.load_trucks()
            
        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"خطأ في تحديث حالة الشاحنة: {str(e)}")
            
    def remove_truck(self, truck_id):
        try:
            conn = self.db_manager.get_connection()
            cursor = conn.cursor()
            
            cursor.execute("DELETE FROM trucks WHERE id=?", (truck_id,))
            
            conn.commit()
            conn.close()
            
            QMessageBox.information(self, "نجح", "تم حذف الشاحنة بنجاح")
            self.load_trucks()
            
        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"خطأ في حذف الشاحنة: {str(e)}")
            
    def get_truck_data(self, truck_id):
        try:
            conn = self.db_manager.get_connection()
            cursor = conn.cursor()
            
            cursor.execute("""
                SELECT plate_number, driver_name, driver_phone, truck_type, capacity,
                       status, insurance_expiry, license_expiry, next_maintenance,
                       fuel_consumption, notes
                FROM trucks WHERE id=?
            """, (truck_id,))
            
            result = cursor.fetchone()
            conn.close()
            
            if result:
                return {
                    'plate_number': result[0],
                    'driver_name': result[1],
                    'driver_phone': result[2],
                    'truck_type': result[3],
                    'capacity': result[4],
                    'status': result[5],
                    'insurance_expiry': result[6],
                    'license_expiry': result[7],
                    'next_maintenance': result[8],
                    'fuel_consumption': result[9],
                    'notes': result[10]
                }
            return {}
            
        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"خطأ في جلب بيانات الشاحنة: {str(e)}")
            return {}
