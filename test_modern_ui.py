#!/usr/bin/env python3
"""
Test Modern UI Components and Styling
اختبار مكونات واجهة المستخدم الحديثة والتنسيق
"""

import sys
from PyQt5.QtWidgets import (QApplication, QMainWindow, QWidget, QVBoxLayout, 
                            QHBoxLayout, QLabel, QPushButton, QFrame, QScrollArea)
from PyQt5.QtCore import Qt
from PyQt5.QtGui import QFont

from ui.styles.modern_theme import ModernTheme
from ui.styles.components import ModernCard, StatsCard, ModernButton, ModernProgressBar

class ModernUITest(QMainWindow):
    """Test window for modern UI components"""
    
    def __init__(self):
        super().__init__()
        self.init_ui()
        
    def init_ui(self):
        """Initialize the test UI"""
        self.setWindowTitle("🎨 اختبار التنسيق الحديث | Modern UI Test")
        self.setGeometry(100, 100, 1200, 800)
        
        # Apply modern theme
        self.setStyleSheet(ModernTheme.get_complete_stylesheet())
        
        # Create central widget with scroll area
        scroll_area = QScrollArea()
        scroll_widget = QWidget()
        scroll_area.setWidget(scroll_widget)
        scroll_area.setWidgetResizable(True)
        self.setCentralWidget(scroll_area)
        
        # Main layout
        main_layout = QVBoxLayout(scroll_widget)
        main_layout.setSpacing(30)
        main_layout.setContentsMargins(30, 30, 30, 30)
        
        # Title
        title = QLabel("🎨 معرض التنسيق الحديث | Modern UI Gallery")
        title.setFont(QFont("Arial", 24, QFont.Bold))
        title.setAlignment(Qt.AlignCenter)
        title.setStyleSheet(f"color: {ModernTheme.COLORS['primary']}; margin-bottom: 20px;")
        main_layout.addWidget(title)
        
        # Statistics Cards Section
        self.create_stats_section(main_layout)
        
        # Modern Cards Section
        self.create_cards_section(main_layout)
        
        # Buttons Section
        self.create_buttons_section(main_layout)
        
        # Progress Bars Section
        self.create_progress_section(main_layout)
        
    def create_stats_section(self, parent_layout):
        """Create statistics cards section"""
        section_title = QLabel("📊 بطاقات الإحصائيات | Statistics Cards")
        section_title.setFont(QFont("Arial", 18, QFont.Bold))
        section_title.setStyleSheet(f"color: {ModernTheme.COLORS['text_primary']}; margin: 20px 0 10px 0;")
        parent_layout.addWidget(section_title)
        
        stats_layout = QHBoxLayout()
        stats_layout.setSpacing(20)
        
        # Create different colored stats cards
        stats_data = [
            ("1,234", "إجمالي العملاء\nTotal Customers", "#3498db"),
            ("567", "الشاحنات النشطة\nActive Trucks", "#2ecc71"),
            ("89", "الطلبات اليوم\nToday's Orders", "#e74c3c"),
            ("12.5M", "المبيعات الشهرية\nMonthly Sales", "#f39c12"),
            ("95%", "معدل الجودة\nQuality Rate", "#9b59b6"),
            ("24/7", "الدعم الفني\nTechnical Support", "#1abc9c")
        ]
        
        for number, label, color in stats_data:
            stats_card = StatsCard(number, label, color)
            stats_layout.addWidget(stats_card)
        
        parent_layout.addLayout(stats_layout)
        
    def create_cards_section(self, parent_layout):
        """Create modern cards section"""
        section_title = QLabel("🃏 البطاقات الحديثة | Modern Cards")
        section_title.setFont(QFont("Arial", 18, QFont.Bold))
        section_title.setStyleSheet(f"color: {ModernTheme.COLORS['text_primary']}; margin: 20px 0 10px 0;")
        parent_layout.addWidget(section_title)
        
        cards_layout = QHBoxLayout()
        cards_layout.setSpacing(20)
        
        # Create different cards
        cards_data = [
            ("إدارة العملاء", "إضافة وتعديل وحذف بيانات العملاء مع إمكانية البحث والفلترة المتقدمة"),
            ("إدارة المخزون", "متابعة مستويات المخزون والتنبيهات التلقائية عند نفاد المواد"),
            ("التقارير المتقدمة", "إنشاء تقارير مفصلة مع الرسوم البيانية والإحصائيات"),
            ("النسخ الاحتياطي", "حماية البيانات مع النسخ الاحتياطي التلقائي والاستعادة السريعة")
        ]
        
        for title, description in cards_data:
            card = ModernCard(title, description)
            card.clicked.connect(lambda t=title: self.show_card_clicked(t))
            cards_layout.addWidget(card)
        
        parent_layout.addLayout(cards_layout)
        
    def create_buttons_section(self, parent_layout):
        """Create buttons section"""
        section_title = QLabel("🔘 الأزرار الحديثة | Modern Buttons")
        section_title.setFont(QFont("Arial", 18, QFont.Bold))
        section_title.setStyleSheet(f"color: {ModernTheme.COLORS['text_primary']}; margin: 20px 0 10px 0;")
        parent_layout.addWidget(section_title)
        
        buttons_layout = QHBoxLayout()
        buttons_layout.setSpacing(15)
        
        # Create different button types
        button_types = [
            ("Primary Button", "primary"),
            ("Success Button", "success"),
            ("Warning Button", "warning"),
            ("Danger Button", "danger"),
            ("Outline Button", "outline")
        ]
        
        for text, button_type in button_types:
            btn = ModernButton(text, button_type)
            btn.clicked.connect(lambda checked, t=text: self.show_button_clicked(t))
            buttons_layout.addWidget(btn)
        
        parent_layout.addLayout(buttons_layout)
        
    def create_progress_section(self, parent_layout):
        """Create progress bars section"""
        section_title = QLabel("📈 أشرطة التقدم | Progress Bars")
        section_title.setFont(QFont("Arial", 18, QFont.Bold))
        section_title.setStyleSheet(f"color: {ModernTheme.COLORS['text_primary']}; margin: 20px 0 10px 0;")
        parent_layout.addWidget(section_title)
        
        progress_frame = QFrame()
        progress_layout = QVBoxLayout(progress_frame)
        progress_layout.setSpacing(15)
        
        # Create different progress bars
        progress_data = [
            ("تقدم المشروع الأول", 75),
            ("معدل الإنجاز اليومي", 60),
            ("استخدام المخزون", 40),
            ("رضا العملاء", 90)
        ]
        
        for label, value in progress_data:
            label_widget = QLabel(f"{label}: {value}%")
            label_widget.setFont(QFont("Arial", 10))
            progress_layout.addWidget(label_widget)
            
            progress_bar = ModernProgressBar()
            progress_bar.setValue(value)
            progress_layout.addWidget(progress_bar)
        
        parent_layout.addWidget(progress_frame)
        
    def show_card_clicked(self, title):
        """Show card clicked message"""
        from PyQt5.QtWidgets import QMessageBox
        msg = QMessageBox()
        msg.setWindowTitle("Card Clicked")
        msg.setText(f"تم النقر على البطاقة: {title}")
        msg.exec_()
        
    def show_button_clicked(self, text):
        """Show button clicked message"""
        from PyQt5.QtWidgets import QMessageBox
        msg = QMessageBox()
        msg.setWindowTitle("Button Clicked")
        msg.setText(f"تم النقر على الزر: {text}")
        msg.exec_()

def main():
    """Main function"""
    app = QApplication(sys.argv)
    
    # Set application properties
    app.setApplicationName("Modern UI Test")
    app.setApplicationVersion("1.0.0")
    
    # Create and show test window
    window = ModernUITest()
    window.show()
    
    print("=== Modern UI Test ===")
    print("🎨 Testing modern UI components and styling")
    print("📱 Check the beautiful cards, buttons, and progress bars")
    print("🖱️ Click on cards and buttons to test interactions")
    
    return app.exec_()

if __name__ == "__main__":
    sys.exit(main())
