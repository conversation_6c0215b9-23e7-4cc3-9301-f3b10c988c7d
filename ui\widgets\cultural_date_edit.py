from PyQt5.QtWidgets import QDateEdit, QCalendarWidget, QVBoxLayout, QWidget
from PyQt5.QtCore import QDate, pyqtSignal
from PyQt5.QtGui import QFont
from utils.cultural_manager import cultural_manager
from datetime import datetime

class CulturalDateEdit(QDateEdit):
    """Date edit widget with cultural formatting support"""
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.setup_cultural_formatting()
        
    def setup_cultural_formatting(self):
        """Setup cultural date formatting"""
        # Set display format based on culture
        preset = cultural_manager.cultural_presets.get(
            cultural_manager.current_culture, 
            cultural_manager.cultural_presets['en-US']
        )
        
        date_format = preset.get('date_format', 'dd/MM/yyyy')
        
        # Convert to Qt format
        qt_format = date_format.replace('yyyy', 'yyyy').replace('MM', 'MM').replace('dd', 'dd')
        self.setDisplayFormat(qt_format)
        
        # Set calendar popup
        self.setCalendarPopup(True)
        
        # Create custom calendar widget
        calendar = CulturalCalendarWidget()
        self.setCalendarWidget(calendar)
        
    def textFromDateTime(self, dateTime):
        """Convert datetime to cultural format"""
        date_obj = dateTime.date().toPython()
        return cultural_manager.format_date(date_obj, 'short')

class CulturalCalendarWidget(QCalendarWidget):
    """Calendar widget with cultural support"""
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.setup_cultural_calendar()
        
    def setup_cultural_calendar(self):
        """Setup cultural calendar"""
        # Set first day of week
        week_start = cultural_manager.get_week_start_day()
        
        day_mapping = {
            'sunday': 7,    # Qt uses 7 for Sunday
            'monday': 1,
            'tuesday': 2,
            'wednesday': 3,
            'thursday': 4,
            'friday': 5,
            'saturday': 6
        }
        
        first_day = day_mapping.get(week_start, 7)
        self.setFirstDayOfWeek(first_day)
        
        # Set weekend days
        weekend_days = cultural_manager.get_weekend_days()
        # Note: Qt weekend formatting would need additional implementation
        
        # Set month/day names if needed
        # This would require more complex Qt calendar customization
