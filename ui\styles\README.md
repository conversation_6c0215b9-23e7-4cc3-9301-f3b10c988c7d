# 🎨 Modern UI Styles System

## Overview
This directory contains the modern styling system for Al-Hassan Stone Factory Management System.

## Files Structure

### `modern_theme.py`
- **Purpose**: Core theme system with color palette and base styles
- **Contains**: 
  - Color definitions
  - Main application stylesheet
  - Table styles
  - Card styles
  - Sidebar styles
  - Dialog styles
  - Menu styles
  - Progress bar styles

### `components.py`
- **Purpose**: Advanced UI components with modern styling
- **Contains**:
  - `ModernCard`: Interactive card component with shadow effects
  - `StatsCard`: Statistics display card with gradient background
  - `ModernButton`: Enhanced button with multiple types
  - `ModernProgressBar`: Custom progress bar with smooth animations

## Usage Examples

### Basic Theme Application
```python
from ui.styles.modern_theme import ModernTheme

# Apply complete theme to window
self.setStyleSheet(ModernTheme.get_complete_stylesheet())

# Apply specific component styles
self.setStyleSheet(ModernTheme.get_table_stylesheet())
```

### Using Modern Components
```python
from ui.styles.components import ModernCard, StatsCard, ModernButton

# Create a modern card
card = ModernCard("Title", "Description")
card.clicked.connect(self.handle_card_click)

# Create a statistics card
stats = StatsCard("150", "Total Customers", "#3498db")

# Create modern buttons
primary_btn = ModernButton("Save", "primary")
success_btn = ModernButton("Submit", "success")
danger_btn = ModernButton("Delete", "danger")
```

## Color Palette

### Primary Colors
- **Primary**: `#0078d4` - Main brand color
- **Primary Dark**: `#106ebe` - Darker variant for hover states
- **Primary Light**: `#40e0d0` - Light variant for selections

### Semantic Colors
- **Success**: `#28a745` - Green for success states
- **Warning**: `#ffc107` - Yellow for warnings
- **Danger**: `#dc3545` - Red for errors/danger
- **Info**: `#17a2b8` - Blue for information

### Background Colors
- **Primary**: `#ffffff` - Main background
- **Secondary**: `#f8f9fa` - Secondary background
- **Dark**: `#343a40` - Dark background
- **Light**: `#e9ecef` - Light background

### Text Colors
- **Primary**: `#212529` - Main text
- **Secondary**: `#6c757d` - Secondary text
- **Muted**: `#868e96` - Muted text
- **White**: `#ffffff` - White text

## Component Features

### ModernCard
- **Shadow Effects**: Automatic drop shadow with hover enhancement
- **Click Interaction**: Emits clicked signal
- **Responsive**: Adapts to content size
- **Customizable**: Title and subtitle support

### StatsCard
- **Gradient Background**: Beautiful gradient based on color
- **Number Display**: Large, prominent number display
- **Label Support**: Descriptive label below number
- **Color Variants**: Multiple predefined color schemes

### ModernButton
- **Multiple Types**: primary, success, warning, danger, outline
- **Hover Effects**: Smooth color transitions
- **Press Effects**: Visual feedback on click
- **Consistent Sizing**: Standardized padding and dimensions

### ModernProgressBar
- **Custom Painting**: Hand-drawn progress visualization
- **Gradient Fill**: Beautiful gradient progress fill
- **Smooth Corners**: Rounded corners for modern look
- **Responsive**: Adapts to container width

## Customization

### Adding New Colors
```python
# In modern_theme.py
COLORS = {
    'your_custom_color': '#hexcode',
    # ... existing colors
}
```

### Creating Custom Components
```python
from ui.styles.modern_theme import ModernTheme

class CustomComponent(QWidget):
    def __init__(self):
        super().__init__()
        self.setStyleSheet(f"""
            QWidget {{
                background-color: {ModernTheme.COLORS['bg_primary']};
                color: {ModernTheme.COLORS['text_primary']};
            }}
        """)
```

### Extending Existing Components
```python
class ExtendedCard(ModernCard):
    def __init__(self, title, subtitle, icon=None):
        super().__init__(title, subtitle)
        if icon:
            self.add_icon(icon)
    
    def add_icon(self, icon):
        # Custom icon implementation
        pass
```

## Best Practices

1. **Consistent Colors**: Always use colors from the ModernTheme.COLORS palette
2. **Component Reuse**: Prefer using existing components over creating new ones
3. **Responsive Design**: Ensure components work on different screen sizes
4. **Accessibility**: Maintain good color contrast for readability
5. **Performance**: Avoid complex stylesheets that might slow down rendering

## Testing

Use the test file to verify styling:
```bash
python test_modern_ui.py
```

This will show all components in action with proper styling applied.

## Integration

The styling system is automatically integrated into:
- Main application window (`main.py`)
- Login window (`ui/login_window.py`)
- Main dashboard (`ui/main_dashboard.py`)
- All module windows (when properly imported)

## Future Enhancements

Planned improvements:
- Dark/Light theme toggle
- Custom theme builder
- Animation system
- More component variants
- Responsive breakpoints
- Theme persistence
