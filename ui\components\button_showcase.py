#!/usr/bin/env python3
"""
Button Showcase - عرض شامل للأزرار
Comprehensive display of all button types and styles
"""

import sys
from PyQt5.QtWidgets import (QApplication, QMainWindow, QWidget, QVBoxLayout, 
                            QHBoxLayout, QGridLayout, QLabel, QPushButton, 
                            QFrame, QScrollArea, QGroupBox, QButtonGroup,
                            QMessageBox, QSizePolicy)
from PyQt5.QtCore import Qt, pyqtSignal
from PyQt5.QtGui import QFont, QIcon

from ui.styles.modern_theme import ModernTheme
from ui.styles.components import ModernButton

class ButtonShowcase(QMainWindow):
    """Comprehensive button showcase window"""
    
    def __init__(self):
        super().__init__()
        self.init_ui()
        
    def init_ui(self):
        """Initialize the button showcase UI"""
        self.setWindowTitle("🔘 عرض الأزرار الشامل | Button Showcase")
        self.setGeometry(100, 100, 1400, 900)
        
        # Apply modern theme
        self.setStyleSheet(ModernTheme.get_complete_stylesheet())
        
        # Create scroll area
        scroll_area = QScrollArea()
        scroll_widget = QWidget()
        scroll_area.setWidget(scroll_widget)
        scroll_area.setWidgetResizable(True)
        self.setCentralWidget(scroll_area)
        
        # Main layout
        main_layout = QVBoxLayout(scroll_widget)
        main_layout.setSpacing(30)
        main_layout.setContentsMargins(30, 30, 30, 30)
        
        # Title
        title = QLabel("🔘 معرض الأزرار الشامل | Complete Button Gallery")
        title.setFont(QFont("Arial", 28, QFont.Bold))
        title.setAlignment(Qt.AlignCenter)
        title.setStyleSheet(f"""
            color: {ModernTheme.COLORS['primary']};
            margin-bottom: 30px;
            padding: 20px;
            background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                stop:0 {ModernTheme.COLORS['bg_primary']},
                stop:1 {ModernTheme.COLORS['bg_light']});
            border-radius: 15px;
            border: 2px solid {ModernTheme.COLORS['border_light']};
        """)
        main_layout.addWidget(title)
        
        # Create different button sections
        self.create_basic_buttons_section(main_layout)
        self.create_colored_buttons_section(main_layout)
        self.create_size_variants_section(main_layout)
        self.create_icon_buttons_section(main_layout)
        self.create_special_buttons_section(main_layout)
        self.create_interactive_buttons_section(main_layout)
        self.create_application_buttons_section(main_layout)
        
    def create_section_title(self, text):
        """Create a section title label"""
        title = QLabel(text)
        title.setFont(QFont("Arial", 18, QFont.Bold))
        title.setStyleSheet(f"""
            color: {ModernTheme.COLORS['text_primary']};
            margin: 20px 0 15px 0;
            padding: 10px;
            background-color: {ModernTheme.COLORS['bg_light']};
            border-radius: 8px;
            border-left: 4px solid {ModernTheme.COLORS['primary']};
        """)
        return title
        
    def create_basic_buttons_section(self, parent_layout):
        """Create basic buttons section"""
        parent_layout.addWidget(self.create_section_title("🔹 الأزرار الأساسية | Basic Buttons"))
        
        frame = QFrame()
        layout = QHBoxLayout(frame)
        layout.setSpacing(15)
        
        # Basic button types
        basic_buttons = [
            ("زر عادي", "Normal Button"),
            ("زر مُفعل", "Enabled Button"),
            ("زر معطل", "Disabled Button"),
            ("زر افتراضي", "Default Button")
        ]
        
        for i, (text_ar, text_en) in enumerate(basic_buttons):
            btn = QPushButton(f"{text_ar}\n{text_en}")
            btn.setMinimumSize(150, 60)
            btn.clicked.connect(lambda checked, t=text_ar: self.show_button_clicked(t))
            
            if i == 2:  # Disable the third button
                btn.setEnabled(False)
            elif i == 3:  # Make the fourth button default
                btn.setDefault(True)
                btn.setStyleSheet(btn.styleSheet() + f"""
                    QPushButton {{
                        border: 2px solid {ModernTheme.COLORS['primary']};
                        font-weight: bold;
                    }}
                """)
            
            layout.addWidget(btn)
        
        parent_layout.addWidget(frame)
        
    def create_colored_buttons_section(self, parent_layout):
        """Create colored buttons section"""
        parent_layout.addWidget(self.create_section_title("🌈 الأزرار الملونة | Colored Buttons"))
        
        frame = QFrame()
        layout = QGridLayout(frame)
        layout.setSpacing(15)
        
        # Colored button types
        colored_buttons = [
            ("أساسي", "Primary", "primary", ModernTheme.COLORS['primary']),
            ("نجاح", "Success", "success", ModernTheme.COLORS['success']),
            ("تحذير", "Warning", "warning", ModernTheme.COLORS['warning']),
            ("خطر", "Danger", "danger", ModernTheme.COLORS['danger']),
            ("معلومات", "Info", "info", ModernTheme.COLORS['info']),
            ("ثانوي", "Secondary", "secondary", ModernTheme.COLORS['secondary']),
            ("فاتح", "Light", "light", ModernTheme.COLORS['bg_light']),
            ("داكن", "Dark", "dark", ModernTheme.COLORS['bg_dark'])
        ]
        
        for i, (text_ar, text_en, btn_type, color) in enumerate(colored_buttons):
            if btn_type in ["primary", "success", "warning", "danger"]:
                btn = ModernButton(f"{text_ar}\n{text_en}", btn_type)
            else:
                btn = QPushButton(f"{text_ar}\n{text_en}")
                btn.setStyleSheet(f"""
                    QPushButton {{
                        background-color: {color};
                        color: {'white' if btn_type != 'light' else 'black'};
                        border: none;
                        border-radius: 8px;
                        padding: 12px 20px;
                        font-weight: 600;
                        min-height: 20px;
                    }}
                    QPushButton:hover {{
                        background-color: {self.darken_color(color)};
                    }}
                """)
            
            btn.setMinimumSize(150, 60)
            btn.clicked.connect(lambda checked, t=text_ar: self.show_button_clicked(t))
            
            row = i // 4
            col = i % 4
            layout.addWidget(btn, row, col)
        
        parent_layout.addWidget(frame)
        
    def create_size_variants_section(self, parent_layout):
        """Create size variants section"""
        parent_layout.addWidget(self.create_section_title("📏 أحجام مختلفة | Size Variants"))
        
        frame = QFrame()
        layout = QHBoxLayout(frame)
        layout.setSpacing(20)
        layout.setAlignment(Qt.AlignCenter)
        
        # Size variants
        sizes = [
            ("صغير", "Small", (100, 30), 8),
            ("متوسط", "Medium", (130, 40), 10),
            ("كبير", "Large", (160, 50), 12),
            ("كبير جداً", "Extra Large", (200, 60), 14)
        ]
        
        for text_ar, text_en, size, font_size in sizes:
            btn = ModernButton(f"{text_ar}\n{text_en}", "primary")
            btn.setFixedSize(*size)
            btn.setStyleSheet(btn.styleSheet() + f"font-size: {font_size}pt;")
            btn.clicked.connect(lambda checked, t=text_ar: self.show_button_clicked(t))
            layout.addWidget(btn)
        
        parent_layout.addWidget(frame)
        
    def create_icon_buttons_section(self, parent_layout):
        """Create icon buttons section"""
        parent_layout.addWidget(self.create_section_title("🎨 أزرار بأيقونات | Icon Buttons"))
        
        frame = QFrame()
        layout = QHBoxLayout(frame)
        layout.setSpacing(15)
        
        # Icon buttons (using text icons for now)
        icon_buttons = [
            ("💾 حفظ", "💾 Save", "success"),
            ("✏️ تعديل", "✏️ Edit", "warning"),
            ("🗑️ حذف", "🗑️ Delete", "danger"),
            ("👁️ عرض", "👁️ View", "info"),
            ("📄 طباعة", "📄 Print", "secondary"),
            ("📤 تصدير", "📤 Export", "primary")
        ]
        
        for text_ar, text_en, btn_type in icon_buttons:
            btn = ModernButton(f"{text_ar}\n{text_en}", btn_type)
            btn.setMinimumSize(120, 60)
            btn.clicked.connect(lambda checked, t=text_ar: self.show_button_clicked(t))
            layout.addWidget(btn)
        
        parent_layout.addWidget(frame)
        
    def create_special_buttons_section(self, parent_layout):
        """Create special buttons section"""
        parent_layout.addWidget(self.create_section_title("⭐ أزرار خاصة | Special Buttons"))
        
        frame = QFrame()
        layout = QHBoxLayout(frame)
        layout.setSpacing(15)
        
        # Special styled buttons
        special_buttons = [
            ("زر متدرج", "Gradient Button", "gradient"),
            ("زر شفاف", "Transparent Button", "transparent"),
            ("زر مدور", "Rounded Button", "rounded"),
            ("زر بحدود", "Outlined Button", "outlined")
        ]
        
        for text_ar, text_en, style_type in special_buttons:
            btn = QPushButton(f"{text_ar}\n{text_en}")
            btn.setMinimumSize(150, 60)
            
            if style_type == "gradient":
                btn.setStyleSheet(f"""
                    QPushButton {{
                        background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                            stop:0 {ModernTheme.COLORS['gradient_start']},
                            stop:1 {ModernTheme.COLORS['gradient_end']});
                        color: white;
                        border: none;
                        border-radius: 8px;
                        padding: 12px 20px;
                        font-weight: 600;
                    }}
                    QPushButton:hover {{
                        background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                            stop:0 {ModernTheme.COLORS['gradient_end']},
                            stop:1 {ModernTheme.COLORS['gradient_start']});
                    }}
                """)
            elif style_type == "transparent":
                btn.setStyleSheet(f"""
                    QPushButton {{
                        background-color: transparent;
                        color: {ModernTheme.COLORS['primary']};
                        border: 2px solid {ModernTheme.COLORS['primary']};
                        border-radius: 8px;
                        padding: 12px 20px;
                        font-weight: 600;
                    }}
                    QPushButton:hover {{
                        background-color: {ModernTheme.COLORS['primary']};
                        color: white;
                    }}
                """)
            elif style_type == "rounded":
                btn.setStyleSheet(f"""
                    QPushButton {{
                        background-color: {ModernTheme.COLORS['primary']};
                        color: white;
                        border: none;
                        border-radius: 30px;
                        padding: 12px 20px;
                        font-weight: 600;
                    }}
                    QPushButton:hover {{
                        background-color: {ModernTheme.COLORS['primary_dark']};
                    }}
                """)
            elif style_type == "outlined":
                btn.setStyleSheet(f"""
                    QPushButton {{
                        background-color: white;
                        color: {ModernTheme.COLORS['primary']};
                        border: 3px solid {ModernTheme.COLORS['primary']};
                        border-radius: 8px;
                        padding: 12px 20px;
                        font-weight: 600;
                    }}
                    QPushButton:hover {{
                        background-color: {ModernTheme.COLORS['bg_light']};
                    }}
                """)
            
            btn.clicked.connect(lambda checked, t=text_ar: self.show_button_clicked(t))
            layout.addWidget(btn)
        
        parent_layout.addWidget(frame)

    def create_interactive_buttons_section(self, parent_layout):
        """Create interactive buttons section"""
        parent_layout.addWidget(self.create_section_title("🎮 أزرار تفاعلية | Interactive Buttons"))

        frame = QFrame()
        layout = QHBoxLayout(frame)
        layout.setSpacing(15)

        # Interactive buttons
        interactive_buttons = [
            ("زر تبديل", "Toggle Button", "toggle"),
            ("زر عداد", "Counter Button", "counter"),
            ("زر تحميل", "Loading Button", "loading"),
            ("زر تأكيد", "Confirm Button", "confirm")
        ]

        for text_ar, text_en, btn_type in interactive_buttons:
            btn = QPushButton(f"{text_ar}\n{text_en}")
            btn.setMinimumSize(150, 60)

            if btn_type == "toggle":
                btn.setCheckable(True)
                btn.setStyleSheet(f"""
                    QPushButton {{
                        background-color: {ModernTheme.COLORS['secondary']};
                        color: white;
                        border: none;
                        border-radius: 8px;
                        padding: 12px 20px;
                        font-weight: 600;
                    }}
                    QPushButton:checked {{
                        background-color: {ModernTheme.COLORS['success']};
                    }}
                    QPushButton:hover {{
                        background-color: {ModernTheme.COLORS['primary']};
                    }}
                """)
                btn.clicked.connect(lambda checked, b=btn: self.handle_toggle_button(b, checked))

            elif btn_type == "counter":
                btn.counter = 0
                btn.clicked.connect(lambda checked, b=btn: self.handle_counter_button(b))
                btn.setStyleSheet(f"""
                    QPushButton {{
                        background-color: {ModernTheme.COLORS['info']};
                        color: white;
                        border: none;
                        border-radius: 8px;
                        padding: 12px 20px;
                        font-weight: 600;
                    }}
                    QPushButton:hover {{
                        background-color: {ModernTheme.COLORS['primary']};
                    }}
                """)

            elif btn_type == "loading":
                btn.setStyleSheet(f"""
                    QPushButton {{
                        background-color: {ModernTheme.COLORS['warning']};
                        color: white;
                        border: none;
                        border-radius: 8px;
                        padding: 12px 20px;
                        font-weight: 600;
                    }}
                    QPushButton:hover {{
                        background-color: {ModernTheme.COLORS['primary']};
                    }}
                """)
                btn.clicked.connect(lambda checked, b=btn: self.handle_loading_button(b))

            elif btn_type == "confirm":
                btn.setStyleSheet(f"""
                    QPushButton {{
                        background-color: {ModernTheme.COLORS['danger']};
                        color: white;
                        border: none;
                        border-radius: 8px;
                        padding: 12px 20px;
                        font-weight: 600;
                    }}
                    QPushButton:hover {{
                        background-color: #c82333;
                    }}
                """)
                btn.clicked.connect(lambda checked, b=btn: self.handle_confirm_button(b))

            layout.addWidget(btn)

        parent_layout.addWidget(frame)

    def create_application_buttons_section(self, parent_layout):
        """Create application-specific buttons section"""
        parent_layout.addWidget(self.create_section_title("🏭 أزرار التطبيق | Application Buttons"))

        frame = QFrame()
        layout = QGridLayout(frame)
        layout.setSpacing(15)

        # Application specific buttons
        app_buttons = [
            ("إضافة عميل", "Add Customer", "success", "👤"),
            ("تسجيل شاحنة", "Register Truck", "warning", "🚛"),
            ("إنشاء فاتورة", "Create Invoice", "primary", "📄"),
            ("عرض المخزون", "View Inventory", "info", "📦"),
            ("تقرير مبيعات", "Sales Report", "secondary", "📊"),
            ("نسخ احتياطي", "Backup Data", "danger", "💾"),
            ("إعدادات النظام", "System Settings", "primary", "⚙️"),
            ("تسجيل خروج", "Logout", "danger", "🚪")
        ]

        for i, (text_ar, text_en, btn_type, icon) in enumerate(app_buttons):
            btn = ModernButton(f"{icon} {text_ar}\n{text_en}", btn_type)
            btn.setMinimumSize(180, 70)
            btn.clicked.connect(lambda checked, t=text_ar: self.show_button_clicked(t))

            row = i // 4
            col = i % 4
            layout.addWidget(btn, row, col)

        parent_layout.addWidget(frame)

        # Add summary section
        self.create_summary_section(parent_layout)

    def create_summary_section(self, parent_layout):
        """Create summary section"""
        parent_layout.addWidget(self.create_section_title("📋 ملخص الأزرار | Button Summary"))

        summary_frame = QFrame()
        summary_frame.setStyleSheet(f"""
            QFrame {{
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 {ModernTheme.COLORS['bg_primary']},
                    stop:1 {ModernTheme.COLORS['bg_light']});
                border: 2px solid {ModernTheme.COLORS['border_light']};
                border-radius: 12px;
                padding: 20px;
                margin: 10px;
            }}
        """)

        summary_layout = QVBoxLayout(summary_frame)

        summary_text = QLabel("""
        📊 إحصائيات الأزرار المعروضة:

        🔹 الأزرار الأساسية: 4 أنواع
        🌈 الأزرار الملونة: 8 ألوان مختلفة
        📏 أحجام مختلفة: 4 أحجام
        🎨 أزرار بأيقونات: 6 أزرار
        ⭐ أزرار خاصة: 4 تصاميم مميزة
        🎮 أزرار تفاعلية: 4 أنواع تفاعلية
        🏭 أزرار التطبيق: 8 أزرار خاصة بالنظام

        📈 المجموع: أكثر من 38 زر مختلف!

        ✨ جميع الأزرار تدعم:
        • تأثيرات التمرير (Hover Effects)
        • تأثيرات النقر (Click Effects)
        • تصميم متجاوب (Responsive Design)
        • ألوان متناسقة (Consistent Colors)
        """)

        summary_text.setFont(QFont("Arial", 11))
        summary_text.setStyleSheet(f"color: {ModernTheme.COLORS['text_primary']}; line-height: 1.6;")
        summary_text.setWordWrap(True)
        summary_layout.addWidget(summary_text)

        parent_layout.addWidget(summary_frame)

    def darken_color(self, color, factor=0.2):
        """Darken a color by a factor"""
        # Simple color darkening - in a real app, you'd use QColor
        color_map = {
            ModernTheme.COLORS['primary']: ModernTheme.COLORS['primary_dark'],
            ModernTheme.COLORS['success']: '#218838',
            ModernTheme.COLORS['warning']: '#e0a800',
            ModernTheme.COLORS['danger']: '#c82333',
            ModernTheme.COLORS['info']: '#138496',
            ModernTheme.COLORS['secondary']: '#545b62',
            ModernTheme.COLORS['bg_light']: '#d1ecf1',
            ModernTheme.COLORS['bg_dark']: '#23272b'
        }
        return color_map.get(color, '#2c3e50')

    def show_button_clicked(self, button_name):
        """Show button clicked message"""
        msg = QMessageBox()
        msg.setWindowTitle("تم النقر على الزر")
        msg.setText(f"تم النقر على: {button_name}")
        msg.setIcon(QMessageBox.Information)
        msg.exec_()

    def handle_toggle_button(self, button, checked):
        """Handle toggle button"""
        if checked:
            button.setText("مُفعل ✓\nActivated ✓")
        else:
            button.setText("زر تبديل\nToggle Button")

    def handle_counter_button(self, button):
        """Handle counter button"""
        button.counter += 1
        button.setText(f"عدد النقرات: {button.counter}\nClicks: {button.counter}")

    def handle_loading_button(self, button):
        """Handle loading button"""
        original_text = button.text()
        button.setText("جاري التحميل...\nLoading...")
        button.setEnabled(False)

        # Re-enable after 2 seconds (simulate loading)
        from PyQt5.QtCore import QTimer
        QTimer.singleShot(2000, lambda: self.reset_loading_button(button, original_text))

    def reset_loading_button(self, button, original_text):
        """Reset loading button"""
        button.setText(original_text)
        button.setEnabled(True)

    def handle_confirm_button(self, button):
        """Handle confirm button"""
        reply = QMessageBox.question(self, 'تأكيد العملية',
                                   'هل أنت متأكد من تنفيذ هذا الإجراء؟',
                                   QMessageBox.Yes | QMessageBox.No)

        if reply == QMessageBox.Yes:
            self.show_button_clicked("تم التأكيد")

def main():
    """Main function"""
    app = QApplication(sys.argv)

    # Set application properties
    app.setApplicationName("Button Showcase")
    app.setApplicationVersion("1.0.0")

    # Create and show showcase window
    window = ButtonShowcase()
    window.show()

    print("=== Button Showcase ===")
    print("🔘 عرض شامل لجميع أنواع الأزرار")
    print("🎨 تصاميم حديثة وتفاعلية")
    print("🖱️ انقر على الأزرار لاختبار التفاعل")

    return app.exec_()

if __name__ == "__main__":
    sys.exit(main())
