from PyQt5.QtWidgets import (QWidget, QVBoxLayout, QHBoxLayout, QTableWidget, 
                            QTableWidgetItem, QPushButton, QLineEdit, QLabel,
                            QDialog, QFormLayout, QMessageBox, QHeaderView,
                            QDateEdit, QDoubleSpinBox, QComboBox, QTextEdit,
                            QSpinBox, QTabWidget, QGroupBox, QGridLayout,
                            QSplitter, QTreeWidget, QTreeWidgetItem)
from PyQt5.QtCore import Qt, QDate, pyqtSignal
from PyQt5.QtGui import QFont, QColor
from datetime import datetime, timedelta
import json

class MaterialProcurementModule(QWidget):
    def __init__(self, db_manager, user_data):
        super().__init__()
        self.db_manager = db_manager
        self.user_data = user_data
        self.init_ui()
        self.load_purchase_orders()
        
    def init_ui(self):
        layout = QVBoxLayout()
        
        # Header
        header_layout = QHBoxLayout()
        
        title = QLabel("إدارة المشتريات / Material Procurement")
        title.setStyleSheet("font-size: 16pt; font-weight: bold; color: #0078d4;")
        header_layout.addWidget(title)
        
        header_layout.addStretch()
        
        # New PO button
        new_po_btn = QPushButton("أمر شراء جديد / New Purchase Order")
        new_po_btn.setStyleSheet("background-color: #28a745;")
        new_po_btn.clicked.connect(self.create_new_po)
        header_layout.addWidget(new_po_btn)
        
        # Materials button
        materials_btn = QPushButton("إدارة المواد / Manage Materials")
        materials_btn.setStyleSheet("background-color: #17a2b8;")
        materials_btn.clicked.connect(self.manage_materials)
        header_layout.addWidget(materials_btn)
        
        layout.addLayout(header_layout)
        
        # Create tabs
        self.tab_widget = QTabWidget()
        
        # Purchase Orders tab
        po_tab = self.create_purchase_orders_tab()
        self.tab_widget.addTab(po_tab, "أوامر الشراء / Purchase Orders")
        
        # Materials tab
        materials_tab = self.create_materials_tab()
        self.tab_widget.addTab(materials_tab, "المواد / Materials")
        
        # Inventory tab
        inventory_tab = self.create_inventory_tab()
        self.tab_widget.addTab(inventory_tab, "المخزون / Inventory")
        
        # Reports tab
        reports_tab = self.create_reports_tab()
        self.tab_widget.addTab(reports_tab, "التقارير / Reports")
        
        layout.addWidget(self.tab_widget)
        
        self.setLayout(layout)
        
    def create_purchase_orders_tab(self):
        """Create purchase orders tab"""
        widget = QWidget()
        layout = QVBoxLayout(widget)
        
        # Filter controls
        filter_layout = QHBoxLayout()
        
        status_label = QLabel("الحالة / Status:")
        self.po_status_filter = QComboBox()
        self.po_status_filter.addItems([
            "الكل / All", "معلق / Pending", "مؤكد / Confirmed", 
            "مستلم جزئياً / Partially Received", "مستلم / Received", "ملغي / Cancelled"
        ])
        self.po_status_filter.currentTextChanged.connect(self.filter_purchase_orders)
        
        filter_layout.addWidget(status_label)
        filter_layout.addWidget(self.po_status_filter)
        filter_layout.addStretch()
        
        layout.addLayout(filter_layout)
        
        # Purchase orders table
        self.po_table = QTableWidget()
        self.po_table.setColumnCount(8)
        self.po_table.setHorizontalHeaderLabels([
            "رقم الأمر / PO No.", "المورد / Supplier", "تاريخ الأمر / Order Date",
            "التسليم المتوقع / Expected Delivery", "المبلغ الكلي / Total Amount",
            "الحالة / Status", "الإجراءات / Actions", "ID"
        ])
        
        self.po_table.hideColumn(7)  # Hide ID column
        
        # Set column widths
        header = self.po_table.horizontalHeader()
        header.setSectionResizeMode(QHeaderView.Stretch)
        
        layout.addWidget(self.po_table)
        
        return widget
        
    def create_materials_tab(self):
        """Create materials management tab"""
        widget = QWidget()
        layout = QVBoxLayout(widget)
        
        # Materials table
        self.materials_table = QTableWidget()
        self.materials_table.setColumnCount(7)
        self.materials_table.setHorizontalHeaderLabels([
            "اسم المادة / Material Name", "الفئة / Category", "الوحدة / Unit",
            "التكلفة / Unit Cost", "المخزون الحالي / Current Stock", 
            "حد إعادة الطلب / Reorder Level", "الإجراءات / Actions"
        ])
        
        # Set column widths
        header = self.materials_table.horizontalHeader()
        header.setSectionResizeMode(QHeaderView.Stretch)
        
        layout.addWidget(self.materials_table)
        
        # Action buttons
        actions_layout = QHBoxLayout()
        
        add_material_btn = QPushButton("إضافة مادة / Add Material")
        add_material_btn.clicked.connect(self.add_material)
        
        add_category_btn = QPushButton("إضافة فئة / Add Category")
        add_category_btn.clicked.connect(self.add_category)
        
        actions_layout.addWidget(add_material_btn)
        actions_layout.addWidget(add_category_btn)
        actions_layout.addStretch()
        
        layout.addLayout(actions_layout)
        
        return widget
        
    def create_inventory_tab(self):
        """Create inventory tracking tab"""
        widget = QWidget()
        layout = QVBoxLayout(widget)
        
        # Summary cards
        summary_layout = QGridLayout()
        
        # Total materials card
        total_card = self.create_summary_card("إجمالي المواد / Total Materials", "0", "#17a2b8")
        summary_layout.addWidget(total_card, 0, 0)
        
        # Low stock card
        low_stock_card = self.create_summary_card("مخزون منخفض / Low Stock", "0", "#ffc107")
        summary_layout.addWidget(low_stock_card, 0, 1)
        
        # Total value card
        value_card = self.create_summary_card("القيمة الكلية / Total Value", "0", "#28a745")
        summary_layout.addWidget(value_card, 0, 2)
        
        layout.addLayout(summary_layout)
        
        # Inventory table
        self.inventory_table = QTableWidget()
        self.inventory_table.setColumnCount(6)
        self.inventory_table.setHorizontalHeaderLabels([
            "المادة / Material", "المخزون الحالي / Current Stock", 
            "حد إعادة الطلب / Reorder Level", "القيمة / Value",
            "آخر حركة / Last Movement", "الحالة / Status"
        ])
        
        # Set column widths
        header = self.inventory_table.horizontalHeader()
        header.setSectionResizeMode(QHeaderView.Stretch)
        
        layout.addWidget(self.inventory_table)
        
        return widget
        
    def create_reports_tab(self):
        """Create procurement reports tab"""
        widget = QWidget()
        layout = QVBoxLayout(widget)
        
        # Report controls
        controls_layout = QHBoxLayout()
        
        report_type_label = QLabel("نوع التقرير / Report Type:")
        self.report_type_combo = QComboBox()
        self.report_type_combo.addItems([
            "تقرير المشتريات / Purchase Report",
            "تقرير المخزون / Inventory Report", 
            "تقرير الموردين / Supplier Report",
            "تحليل التكاليف / Cost Analysis"
        ])
        
        from_date_label = QLabel("من / From:")
        self.from_date_input = QDateEdit()
        self.from_date_input.setDate(QDate.currentDate().addDays(-30))
        self.from_date_input.setCalendarPopup(True)
        
        to_date_label = QLabel("إلى / To:")
        self.to_date_input = QDateEdit()
        self.to_date_input.setDate(QDate.currentDate())
        self.to_date_input.setCalendarPopup(True)
        
        generate_btn = QPushButton("إنشاء التقرير / Generate Report")
        generate_btn.clicked.connect(self.generate_procurement_report)
        
        controls_layout.addWidget(report_type_label)
        controls_layout.addWidget(self.report_type_combo)
        controls_layout.addWidget(from_date_label)
        controls_layout.addWidget(self.from_date_input)
        controls_layout.addWidget(to_date_label)
        controls_layout.addWidget(self.to_date_input)
        controls_layout.addWidget(generate_btn)
        controls_layout.addStretch()
        
        layout.addLayout(controls_layout)
        
        # Report display
        self.report_display = QTextEdit()
        self.report_display.setReadOnly(True)
        layout.addWidget(self.report_display)
        
        return widget
        
    def create_summary_card(self, title, value, color):
        """Create summary card widget"""
        card = QGroupBox()
        card.setStyleSheet(f"""
            QGroupBox {{
                background-color: {color};
                border-radius: 8px;
                font-weight: bold;
                color: white;
                padding: 10px;
            }}
        """)
        
        layout = QVBoxLayout(card)
        
        title_label = QLabel(title)
        title_label.setFont(QFont("Arial", 10))
        title_label.setAlignment(Qt.AlignCenter)
        
        value_label = QLabel(value)
        value_label.setFont(QFont("Arial", 16, QFont.Bold))
        value_label.setAlignment(Qt.AlignCenter)
        
        layout.addWidget(title_label)
        layout.addWidget(value_label)
        
        return card
        
    def load_purchase_orders(self):
        """Load purchase orders from database"""
        conn = self.db_manager.get_connection()
        cursor = conn.cursor()
        
        cursor.execute("""
            SELECT po.id, po.po_number, s.name as supplier_name, po.order_date,
                   po.expected_delivery, po.total_amount, po.status
            FROM purchase_orders po
            JOIN suppliers s ON po.supplier_id = s.id
            ORDER BY po.created_at DESC
        """)
        
        orders = cursor.fetchall()
        
        self.po_table.setRowCount(len(orders))
        
        for row, order in enumerate(orders):
            self.po_table.setItem(row, 0, QTableWidgetItem(order['po_number']))
            self.po_table.setItem(row, 1, QTableWidgetItem(order['supplier_name']))
            self.po_table.setItem(row, 2, QTableWidgetItem(order['order_date']))
            self.po_table.setItem(row, 3, QTableWidgetItem(order['expected_delivery'] or '-'))
            self.po_table.setItem(row, 4, QTableWidgetItem(f"{order['total_amount']:.2f}"))
            
            # Status with color coding
            status_item = QTableWidgetItem(order['status'])
            if order['status'] == 'received':
                status_item.setBackground(QColor('#28a745'))
            elif order['status'] == 'confirmed':
                status_item.setBackground(QColor('#17a2b8'))
            elif order['status'] == 'cancelled':
                status_item.setBackground(QColor('#dc3545'))
            
            self.po_table.setItem(row, 5, status_item)
            
            # Action buttons
            actions_widget = QWidget()
            actions_layout = QHBoxLayout(actions_widget)
            actions_layout.setContentsMargins(5, 5, 5, 5)
            
            view_btn = QPushButton("عرض / View")
            view_btn.setFixedSize(60, 25)
            view_btn.clicked.connect(lambda checked, oid=order['id']: self.view_purchase_order(oid))
            
            receive_btn = QPushButton("استلام / Receive")
            receive_btn.setFixedSize(70, 25)
            receive_btn.setStyleSheet("background-color: #28a745;")
            receive_btn.clicked.connect(lambda checked, oid=order['id']: self.receive_materials(oid))
            
            actions_layout.addWidget(view_btn)
            actions_layout.addWidget(receive_btn)
            
            self.po_table.setCellWidget(row, 6, actions_widget)
            
            # Store ID
            self.po_table.setItem(row, 7, QTableWidgetItem(str(order['id'])))
            
        conn.close()
        
    def create_new_po(self):
        """Create new purchase order"""
        dialog = PurchaseOrderDialog(self.db_manager, self.user_data)
        if dialog.exec_() == QDialog.Accepted:
            self.load_purchase_orders()
            
    def view_purchase_order(self, po_id):
        """View purchase order details"""
        dialog = PurchaseOrderDialog(self.db_manager, self.user_data, po_id)
        dialog.exec_()
        
    def receive_materials(self, po_id):
        """Receive materials from purchase order"""
        dialog = MaterialReceiptDialog(self.db_manager, po_id)
        if dialog.exec_() == QDialog.Accepted:
            self.load_purchase_orders()
            
    def manage_materials(self):
        """Open materials management dialog"""
        dialog = MaterialsManagementDialog(self.db_manager)
        if dialog.exec_() == QDialog.Accepted:
            self.load_materials()
            
    def add_material(self):
        """Add new material"""
        dialog = MaterialDialog(self.db_manager)
        if dialog.exec_() == QDialog.Accepted:
            self.load_materials()
            
    def add_category(self):
        """Add new material category"""
        dialog = CategoryDialog(self.db_manager)
        if dialog.exec_() == QDialog.Accepted:
            self.load_materials()
            
    def load_materials(self):
        """Load materials from database"""
        conn = self.db_manager.get_connection()
        cursor = conn.cursor()
        
        cursor.execute("""
            SELECT m.id, m.name, mc.name as category_name, m.unit,
                   m.unit_cost, m.current_stock, m.reorder_level
            FROM materials m
            LEFT JOIN material_categories mc ON m.category_id = mc.id
            ORDER BY m.name
        """)
        
        materials = cursor.fetchall()
        
        self.materials_table.setRowCount(len(materials))
        
        for row, material in enumerate(materials):
            self.materials_table.setItem(row, 0, QTableWidgetItem(material['name']))
            self.materials_table.setItem(row, 1, QTableWidgetItem(material['category_name'] or '-'))
            self.materials_table.setItem(row, 2, QTableWidgetItem(material['unit']))
            self.materials_table.setItem(row, 3, QTableWidgetItem(f"{material['unit_cost']:.2f}"))
            self.materials_table.setItem(row, 4, QTableWidgetItem(f"{material['current_stock']:.2f}"))
            self.materials_table.setItem(row, 5, QTableWidgetItem(str(material['reorder_level'])))
            
            # Action buttons
            actions_widget = QWidget()
            actions_layout = QHBoxLayout(actions_widget)
            actions_layout.setContentsMargins(5, 5, 5, 5)
            
            edit_btn = QPushButton("تعديل / Edit")
            edit_btn.setFixedSize(70, 25)
            edit_btn.clicked.connect(lambda checked, mid=material['id']: self.edit_material(mid))
            
            actions_layout.addWidget(edit_btn)
            
            self.materials_table.setCellWidget(row, 6, actions_widget)
            
        conn.close()
        
    def edit_material(self, material_id):
        """Edit material"""
        dialog = MaterialDialog(self.db_manager, material_id)
        if dialog.exec_() == QDialog.Accepted:
            self.load_materials()
            
    def filter_purchase_orders(self):
        """Filter purchase orders by status"""
        filter_text = self.po_status_filter.currentText()
        
        for row in range(self.po_table.rowCount()):
            if "الكل" in filter_text or "All" in filter_text:
                self.po_table.setRowHidden(row, False)
            else:
                status_item = self.po_table.item(row, 5)
                if status_item:
                    should_show = any(keyword in filter_text.lower() 
                                    for keyword in status_item.text().lower().split())
                    self.po_table.setRowHidden(row, not should_show)
                    
    def generate_procurement_report(self):
        """Generate procurement report"""
        report_type = self.report_type_combo.currentText()
        from_date = self.from_date_input.date().toString("yyyy-MM-dd")
        to_date = self.to_date_input.date().toString("yyyy-MM-dd")
        
        conn = self.db_manager.get_connection()
        cursor = conn.cursor()
        
        if "Purchase Report" in report_type or "تقرير المشتريات" in report_type:
            cursor.execute("""
                SELECT po.po_number, s.name as supplier_name, po.order_date,
                       po.total_amount, po.status
                FROM purchase_orders po
                JOIN suppliers s ON po.supplier_id = s.id
                WHERE po.order_date BETWEEN ? AND ?
                ORDER BY po.order_date DESC
            """, (from_date, to_date))
            
            orders = cursor.fetchall()
            
            report_text = f"""
تقرير المشتريات من {from_date} إلى {to_date}
Purchase Report from {from_date} to {to_date}

═══════════════════════════════════════════════════════════════

"""
            
            total_amount = 0
            for order in orders:
                report_text += f"رقم الأمر / PO: {order['po_number']}\n"
                report_text += f"المورد / Supplier: {order['supplier_name']}\n"
                report_text += f"التاريخ / Date: {order['order_date']}\n"
                report_text += f"المبلغ / Amount: {order['total_amount']:.2f}\n"
                report_text += f"الحالة / Status: {order['status']}\n"
                report_text += "─" * 50 + "\n"
                total_amount += order['total_amount']
                
            report_text += f"\nإجمالي المشتريات / Total Purchases: {total_amount:.2f}\n"
            report_text += f"عدد الأوامر / Number of Orders: {len(orders)}\n"
            
        elif "Inventory Report" in report_type or "تقرير المخزون" in report_type:
            cursor.execute("""
                SELECT m.name, m.current_stock, m.unit, m.unit_cost,
                       (m.current_stock * m.unit_cost) as total_value
                FROM materials m
                ORDER BY total_value DESC
            """)
            
            materials = cursor.fetchall()
            
            report_text = f"""
تقرير المخزون كما في {to_date}
Inventory Report as of {to_date}

═══════════════════════════════════════════════════════════════

"""
            
            total_value = 0
            for material in materials:
                report_text += f"المادة / Material: {material['name']}\n"
                report_text += f"المخزون / Stock: {material['current_stock']:.2f} {material['unit']}\n"
                report_text += f"التكلفة / Unit Cost: {material['unit_cost']:.2f}\n"
                report_text += f"القيمة الكلية / Total Value: {material['total_value']:.2f}\n"
                report_text += "─" * 50 + "\n"
                total_value += material['total_value']
                
            report_text += f"\nإجمالي قيمة المخزون / Total Inventory Value: {total_value:.2f}\n"
            report_text += f"عدد المواد / Number of Materials: {len(materials)}\n"
            
        else:
            report_text = "تقرير غير متاح / Report not available"
            
        self.report_display.setPlainText(report_text)
        conn.close()

class PurchaseOrderDialog(QDialog):
    def __init__(self, db_manager, user_data, po_id=None):
        super().__init__()
        self.db_manager = db_manager
        self.user_data = user_data
        self.po_id = po_id
        self.po_items = []
        self.init_ui()
        
        if po_id:
            self.load_po_data()
            
    def init_ui(self):
        self.setWindowTitle("أمر شراء / Purchase Order")
        self.setFixedSize(800, 600)
        
        layout = QVBoxLayout()
        
        # PO header
        header_group = QGroupBox("معلومات الأمر / Order Information")
        header_layout = QFormLayout(header_group)
        
        self.po_number_input = QLineEdit()
        self.po_number_input.setText(self.generate_po_number())
        self.po_number_input.setReadOnly(True)
        header_layout.addRow("رقم الأمر / PO Number:", self.po_number_input)
        
        self.supplier_combo = QComboBox()
        self.load_suppliers()
        header_layout.addRow("المورد / Supplier:", self.supplier_combo)
        
        self.order_date_input = QDateEdit()
        self.order_date_input.setDate(QDate.currentDate())
        self.order_date_input.setCalendarPopup(True)
        header_layout.addRow("تاريخ الأمر / Order Date:", self.order_date_input)
        
        self.expected_delivery_input = QDateEdit()
        self.expected_delivery_input.setDate(QDate.currentDate().addDays(7))
        self.expected_delivery_input.setCalendarPopup(True)
        header_layout.addRow("التسليم المتوقع / Expected Delivery:", self.expected_delivery_input)
        
        layout.addWidget(header_group)
        
        # Add item section
        add_item_group = QGroupBox("إضافة مادة / Add Material")
        add_item_layout = QHBoxLayout(add_item_group)
        
        self.material_combo = QComboBox()
        self.load_materials()
        add_item_layout.addWidget(QLabel("المادة / Material:"))
        add_item_layout.addWidget(self.material_combo)
        
        self.quantity_input = QDoubleSpinBox()
        self.quantity_input.setRange(0.1, 10000)
        self.quantity_input.setValue(1)
        add_item_layout.addWidget(QLabel("الكمية / Quantity:"))
        add_item_layout.addWidget(self.quantity_input)
        
        self.unit_price_input = QDoubleSpinBox()
        self.unit_price_input.setRange(0, 100000)
        self.unit_price_input.setDecimals(2)
        add_item_layout.addWidget(QLabel("السعر / Unit Price:"))
        add_item_layout.addWidget(self.unit_price_input)
        
        add_item_btn = QPushButton("إضافة / Add")
        add_item_btn.clicked.connect(self.add_item)
        add_item_layout.addWidget(add_item_btn)
        
        layout.addWidget(add_item_group)
        
        # Items table
        self.items_table = QTableWidget()
        self.items_table.setColumnCount(5)
        self.items_table.setHorizontalHeaderLabels([
            "المادة / Material", "الكمية / Quantity", "السعر / Unit Price",
            "المجموع / Total", "حذف / Remove"
        ])
        
        layout.addWidget(self.items_table)
        
        # Total
        total_layout = QHBoxLayout()
        total_layout.addStretch()
        
        self.total_label = QLabel("المجموع الكلي / Total: 0.00")
        self.total_label.setFont(QFont("Arial", 12, QFont.Bold))
        self.total_label.setStyleSheet("color: #28a745;")
        total_layout.addWidget(self.total_label)
        
        layout.addLayout(total_layout)
        
        # Buttons
        buttons_layout = QHBoxLayout()
        
        save_btn = QPushButton("حفظ / Save")
        save_btn.clicked.connect(self.save_po)
        
        cancel_btn = QPushButton("إلغاء / Cancel")
        cancel_btn.clicked.connect(self.reject)
        
        buttons_layout.addWidget(save_btn)
        buttons_layout.addWidget(cancel_btn)
        
        layout.addLayout(buttons_layout)
        
        self.setLayout(layout)
        
    def generate_po_number(self):
        """Generate unique PO number"""
        timestamp = datetime.now().strftime("%Y%m%d%H%M%S")
        return f"PO-{timestamp}"
        
    def load_suppliers(self):
        """Load suppliers for combo box"""
        conn = self.db_manager.get_connection()
        cursor = conn.cursor()
        
        cursor.execute("SELECT id, name FROM suppliers ORDER BY name")
        suppliers = cursor.fetchall()
        
        self.supplier_combo.addItem("اختر المورد / Select Supplier", None)
        for supplier in suppliers:
            self.supplier_combo.addItem(supplier['name'], supplier['id'])
            
        conn.close()
        
    def load_materials(self):
        """Load materials for combo box"""
        conn = self.db_manager.get_connection()
        cursor = conn.cursor()
        
        cursor.execute("SELECT id, name, unit, unit_cost FROM materials ORDER BY name")
        materials = cursor.fetchall()
        
        self.material_combo.addItem("اختر المادة / Select Material", None)
        for material in materials:
            display_text = f"{material['name']} ({material['unit']})"
            self.material_combo.addItem(display_text, material['id'])
            
        conn.close()
        
    def add_item(self):
        """Add item to purchase order"""
        material_id = self.material_combo.currentData()
        if not material_id:
            QMessageBox.warning(self, "تحذير / Warning", "يرجى اختيار المادة\nPlease select material")
            return
            
        quantity = self.quantity_input.value()
        unit_price = self.unit_price_input.value()
        
        if unit_price == 0:
            QMessageBox.warning(self, "تحذير / Warning", "يرجى إدخال السعر\nPlease enter price")
            return
            
        total_price = quantity * unit_price
        
        # Add to items list
        item = {
            'material_id': material_id,
            'material_name': self.material_combo.currentText(),
            'quantity': quantity,
            'unit_price': unit_price,
            'total_price': total_price
        }
        self.po_items.append(item)
        
        # Update table
        self.update_items_table()
        
        # Clear inputs
        self.material_combo.setCurrentIndex(0)
        self.quantity_input.setValue(1)
        self.unit_price_input.setValue(0)
        
    def update_items_table(self):
        """Update items table display"""
        self.items_table.setRowCount(len(self.po_items))
        
        total_amount = 0
        
        for row, item in enumerate(self.po_items):
            self.items_table.setItem(row, 0, QTableWidgetItem(item['material_name']))
            self.items_table.setItem(row, 1, QTableWidgetItem(f"{item['quantity']:.2f}"))
            self.items_table.setItem(row, 2, QTableWidgetItem(f"{item['unit_price']:.2f}"))
            self.items_table.setItem(row, 3, QTableWidgetItem(f"{item['total_price']:.2f}"))
            
            # Delete button
            delete_btn = QPushButton("حذف / Delete")
            delete_btn.setFixedSize(80, 25)
            delete_btn.setStyleSheet("background-color: #dc3545;")
            delete_btn.clicked.connect(lambda checked, r=row: self.remove_item(r))
            self.items_table.setCellWidget(row, 4, delete_btn)
            
            total_amount += item['total_price']
            
        self.total_label.setText(f"المجموع الكلي / Total: {total_amount:.2f}")
        
    def remove_item(self, row):
        """Remove item from purchase order"""
        if 0 <= row < len(self.po_items):
            self.po_items.pop(row)
            self.update_items_table()
            
    def save_po(self):
        """Save purchase order to database"""
        supplier_id = self.supplier_combo.currentData()
        
        if not supplier_id:
            QMessageBox.warning(self, "تحذير / Warning", "يرجى اختيار المورد\nPlease select supplier")
            return
            
        if not self.po_items:
            QMessageBox.warning(self, "تحذير / Warning", "يرجى إضافة مواد للأمر\nPlease add materials to order")
            return
            
        total_amount = sum(item['total_price'] for item in self.po_items)
        
        conn = self.db_manager.get_connection()
        cursor = conn.cursor()
        
        try:
            # Insert purchase order
            cursor.execute("""
                INSERT INTO purchase_orders 
                (po_number, supplier_id, order_date, expected_delivery, total_amount, status, created_by)
                VALUES (?, ?, ?, ?, ?, 'pending', ?)
            """, (self.po_number_input.text(), supplier_id,
                 self.order_date_input.date().toString("yyyy-MM-dd"),
                 self.expected_delivery_input.date().toString("yyyy-MM-dd"),
                 total_amount, self.user_data['id']))
            
            po_id = cursor.lastrowid
            
            # Insert purchase order items
            for item in self.po_items:
                cursor.execute("""
                    INSERT INTO purchase_order_items 
                    (po_id, material_id, quantity, unit_price, total_price)
                    VALUES (?, ?, ?, ?, ?)
                """, (po_id, item['material_id'], item['quantity'],
                     item['unit_price'], item['total_price']))
            
            conn.commit()
            
            QMessageBox.information(self, "نجح / Success", 
                                  f"تم حفظ أمر الشراء بنجاح\nPurchase order saved successfully\n"
                                  f"رقم الأمر: {self.po_number_input.text()}")
            
            self.accept()
            
        except Exception as e:
            QMessageBox.critical(self, "خطأ / Error", f"Error saving purchase order: {str(e)}")
        finally:
            conn.close()

class MaterialDialog(QDialog):
    def __init__(self, db_manager, material_id=None):
        super().__init__()
        self.db_manager = db_manager
        self.material_id = material_id
        self.init_ui()
        
        if material_id:
            self.load_material_data()
            
    def init_ui(self):
        self.setWindowTitle("مادة / Material")
        self.setFixedSize(400, 350)
        
        layout = QFormLayout()
        
        # Material name
        self.name_input = QLineEdit()
        self.name_input.setPlaceholderText("اسم المادة / Material name")
        layout.addRow("الاسم / Name:", self.name_input)
        
        # Category
        self.category_combo = QComboBox()
        self.load_categories()
        layout.addRow("الفئة / Category:", self.category_combo)
        
        # Description
        self.description_input = QTextEdit()
        self.description_input.setMaximumHeight(60)
        self.description_input.setPlaceholderText("وصف المادة / Material description")
        layout.addRow("الوصف / Description:", self.description_input)
        
        # Unit
        self.unit_input = QLineEdit()
        self.unit_input.setPlaceholderText("الوحدة / Unit (kg, m, pcs, etc.)")
        layout.addRow("الوحدة / Unit:", self.unit_input)
        
        # Unit cost
        self.unit_cost_input = QDoubleSpinBox()
        self.unit_cost_input.setRange(0, 100000)
        self.unit_cost_input.setDecimals(2)
        layout.addRow("تكلفة الوحدة / Unit Cost:", self.unit_cost_input)
        
        # Reorder level
        self.reorder_level_input = QSpinBox()
        self.reorder_level_input.setRange(0, 10000)
        layout.addRow("حد إعادة الطلب / Reorder Level:", self.reorder_level_input)
        
        # Current stock (for editing only)
        if self.material_id:
            self.current_stock_input = QDoubleSpinBox()
            self.current_stock_input.setRange(0, 100000)
            self.current_stock_input.setDecimals(2)
            layout.addRow("المخزون الحالي / Current Stock:", self.current_stock_input)
        
        # Buttons
        buttons_layout = QHBoxLayout()
        
        save_btn = QPushButton("حفظ / Save")
        save_btn.clicked.connect(self.save_material)
        
        cancel_btn = QPushButton("إلغاء / Cancel")
        cancel_btn.clicked.connect(self.reject)
        
        buttons_layout.addWidget(save_btn)
        buttons_layout.addWidget(cancel_btn)
        
        layout.addRow(buttons_layout)
        
        self.setLayout(layout)
        
    def load_categories(self):
        """Load material categories"""
        conn = self.db_manager.get_connection()
        cursor = conn.cursor()
        
        cursor.execute("SELECT id, name FROM material_categories ORDER BY name")
        categories = cursor.fetchall()
        
        self.category_combo.addItem("اختر الفئة / Select Category", None)
        for category in categories:
            self.category_combo.addItem(category['name'], category['id'])
            
        conn.close()
        
    def load_material_data(self):
        """Load existing material data"""
        conn = self.db_manager.get_connection()
        cursor = conn.cursor()
        
        cursor.execute("SELECT * FROM materials WHERE id = ?", (self.material_id,))
        material = cursor.fetchone()
        
        if material:
            self.name_input.setText(material['name'])
            self.description_input.setPlainText(material['description'] or '')
            self.unit_input.setText(material['unit'])
            self.unit_cost_input.setValue(material['unit_cost'])
            self.reorder_level_input.setValue(material['reorder_level'])
            
            if hasattr(self, 'current_stock_input'):
                self.current_stock_input.setValue(material['current_stock'])
            
            # Set category
            for i in range(self.category_combo.count()):
                if self.category_combo.itemData(i) == material['category_id']:
                    self.category_combo.setCurrentIndex(i)
                    break
                    
        conn.close()
        
    def save_material(self):
        """Save material data"""
        name = self.name_input.text().strip()
        unit = self.unit_input.text().strip()
        
        if not name:
            QMessageBox.warning(self, "تحذير / Warning", "يرجى إدخال اسم المادة\nPlease enter material name")
            return
            
        if not unit:
            QMessageBox.warning(self, "تحذير / Warning", "يرجى إدخال الوحدة\nPlease enter unit")
            return
            
        conn = self.db_manager.get_connection()
        cursor = conn.cursor()
        
        try:
            if self.material_id:
                # Update existing material
                cursor.execute("""
                    UPDATE materials 
                    SET name = ?, category_id = ?, description = ?, unit = ?, 
                        unit_cost = ?, reorder_level = ?, current_stock = ?
                    WHERE id = ?
                """, (name, self.category_combo.currentData(), 
                     self.description_input.toPlainText(), unit,
                     self.unit_cost_input.value(), self.reorder_level_input.value(),
                     self.current_stock_input.value() if hasattr(self, 'current_stock_input') else 0,
                     self.material_id))
            else:
                # Add new material
                cursor.execute("""
                    INSERT INTO materials 
                    (name, category_id, description, unit, unit_cost, reorder_level, current_stock)
                    VALUES (?, ?, ?, ?, ?, ?, 0)
                """, (name, self.category_combo.currentData(),
                     self.description_input.toPlainText(), unit,
                     self.unit_cost_input.value(), self.reorder_level_input.value()))
                
            conn.commit()
            self.accept()
            
        except Exception as e:
            QMessageBox.critical(self, "خطأ / Error", f"Error saving material: {str(e)}")
        finally:
            conn.close()

class MaterialReceiptDialog(QDialog):
    def __init__(self, db_manager, po_id):
        super().__init__()
        self.db_manager = db_manager
        self.po_id = po_id
        self.init_ui()
        self.load_po_items()
        
    def init_ui(self):
        self.setWindowTitle("استلام المواد / Material Receipt")
        self.setFixedSize(600, 400)
        
        layout = QVBoxLayout()
        
        # PO info
        self.po_info_label = QLabel()
        self.po_info_label.setStyleSheet("background-color: #404040; padding: 10px; border-radius: 4px;")
        layout.addWidget(self.po_info_label)
        
        # Items table
        self.items_table = QTableWidget()
        self.items_table.setColumnCount(5)
        self.items_table.setHorizontalHeaderLabels([
            "المادة / Material", "الكمية المطلوبة / Ordered Qty", 
            "المستلم سابقاً / Previously Received", "الكمية المستلمة / Received Qty",
            "ملاحظات / Notes"
        ])
        
        layout.addWidget(self.items_table)
        
        # Receipt date
        date_layout = QHBoxLayout()
        date_layout.addWidget(QLabel("تاريخ الاستلام / Receipt Date:"))
        
        self.receipt_date_input = QDateEdit()
        self.receipt_date_input.setDate(QDate.currentDate())
        self.receipt_date_input.setCalendarPopup(True)
        date_layout.addWidget(self.receipt_date_input)
        date_layout.addStretch()
        
        layout.addLayout(date_layout)
        
        # Buttons
        buttons_layout = QHBoxLayout()
        
        receive_btn = QPushButton("تأكيد الاستلام / Confirm Receipt")
        receive_btn.clicked.connect(self.confirm_receipt)
        
        cancel_btn = QPushButton("إلغاء / Cancel")
        cancel_btn.clicked.connect(self.reject)
        
        buttons_layout.addWidget(receive_btn)
        buttons_layout.addWidget(cancel_btn)
        
        layout.addLayout(buttons_layout)
        
        self.setLayout(layout)
        
    def load_po_items(self):
        """Load purchase order items"""
        conn = self.db_manager.get_connection()
        cursor = conn.cursor()
        
        # Get PO info
        cursor.execute("""
            SELECT po.po_number, s.name as supplier_name, po.order_date
            FROM purchase_orders po
            JOIN suppliers s ON po.supplier_id = s.id
            WHERE po.id = ?
        """, (self.po_id,))
        
        po_info = cursor.fetchone()
        
        if po_info:
            info_text = f"أمر الشراء / PO: {po_info['po_number']}\n"
            info_text += f"المورد / Supplier: {po_info['supplier_name']}\n"
            info_text += f"تاريخ الأمر / Order Date: {po_info['order_date']}"
            self.po_info_label.setText(info_text)
        
        # Get PO items
        cursor.execute("""
            SELECT poi.id, m.name as material_name, poi.quantity, poi.received_quantity
            FROM purchase_order_items poi
            JOIN materials m ON poi.material_id = m.id
            WHERE poi.po_id = ?
        """, (self.po_id,))
        
        items = cursor.fetchall()
        
        self.items_table.setRowCount(len(items))
        
        for row, item in enumerate(items):
            self.items_table.setItem(row, 0, QTableWidgetItem(item['material_name']))
            self.items_table.setItem(row, 1, QTableWidgetItem(f"{item['quantity']:.2f}"))
            self.items_table.setItem(row, 2, QTableWidgetItem(f"{item['received_quantity']:.2f}"))
            
            # Received quantity input
            received_input = QDoubleSpinBox()
            received_input.setRange(0, item['quantity'] - item['received_quantity'])
            received_input.setValue(item['quantity'] - item['received_quantity'])
            received_input.setDecimals(2)
            self.items_table.setCellWidget(row, 3, received_input)
            
            # Notes input
            notes_input = QLineEdit()
            notes_input.setPlaceholderText("ملاحظات / Notes")
            self.items_table.setCellWidget(row, 4, notes_input)
            
        conn.close()
        
    def confirm_receipt(self):
        """Confirm material receipt"""
        conn = self.db_manager.get_connection()
        cursor = conn.cursor()
        
        try:
            # Get PO items
            cursor.execute("""
                SELECT poi.id, poi.material_id, poi.quantity, poi.received_quantity
                FROM purchase_order_items poi
                WHERE poi.po_id = ?
            """, (self.po_id,))
            
            po_items = cursor.fetchall()
            
            for row, po_item in enumerate(po_items):
                # Get received quantity from table
                received_widget = self.items_table.cellWidget(row, 3)
                if received_widget:
                    received_qty = received_widget.value()
                    
                    if received_qty > 0:
                        # Update received quantity in PO item
                        new_received_qty = po_item['received_quantity'] + received_qty
                        cursor.execute("""
                            UPDATE purchase_order_items 
                            SET received_quantity = ?
                            WHERE id = ?
                        """, (new_received_qty, po_item['id']))
                        
                        # Update material stock
                        cursor.execute("""
                            UPDATE materials 
                            SET current_stock = current_stock + ?
                            WHERE id = ?
                        """, (received_qty, po_item['material_id']))
            
            # Check if PO is fully received
            cursor.execute("""
                SELECT COUNT(*) as total_items,
                       SUM(CASE WHEN quantity <= received_quantity THEN 1 ELSE 0 END) as received_items
                FROM purchase_order_items
                WHERE po_id = ?
            """, (self.po_id,))
            
            result = cursor.fetchone()
            
            if result['total_items'] == result['received_items']:
                # Mark PO as received
                cursor.execute("""
                    UPDATE purchase_orders 
                    SET status = 'received', actual_delivery = ?
                    WHERE id = ?
                """, (self.receipt_date_input.date().toString("yyyy-MM-dd"), self.po_id))
            else:
                # Mark as partially received
                cursor.execute("""
                    UPDATE purchase_orders 
                    SET status = 'partially_received'
                    WHERE id = ?
                """, (self.po_id,))
            
            conn.commit()
            
            QMessageBox.information(self, "نجح / Success", 
                                  "تم تأكيد استلام المواد بنجاح\nMaterial receipt confirmed successfully")
            
            self.accept()
            
        except Exception as e:
            QMessageBox.critical(self, "خطأ / Error", f"Error confirming receipt: {str(e)}")
        finally:
            conn.close()

class CategoryDialog(QDialog):
    def __init__(self, db_manager, category_id=None):
        super().__init__()
        self.db_manager = db_manager
        self.category_id = category_id
        self.init_ui()
        
        if category_id:
            self.load_category_data()
            
    def init_ui(self):
        self.setWindowTitle("فئة المواد / Material Category")
        self.setFixedSize(350, 200)
        
        layout = QFormLayout()
        
        # Category name
        self.name_input = QLineEdit()
        self.name_input.setPlaceholderText("اسم الفئة / Category name")
        layout.addRow("الاسم / Name:", self.name_input)
        
        # Description
        self.description_input = QTextEdit()
        self.description_input.setMaximumHeight(80)
        self.description_input.setPlaceholderText("وصف الفئة / Category description")
        layout.addRow("الوصف / Description:", self.description_input)
        
        # Buttons
        buttons_layout = QHBoxLayout()
        
        save_btn = QPushButton("حفظ / Save")
        save_btn.clicked.connect(self.save_category)
        
        cancel_btn = QPushButton("إلغاء / Cancel")
        cancel_btn.clicked.connect(self.reject)
        
        buttons_layout.addWidget(save_btn)
        buttons_layout.addWidget(cancel_btn)
        
        layout.addRow(buttons_layout)
        
        self.setLayout(layout)
        
    def save_category(self):
        """Save category data"""
        name = self.name_input.text().strip()
        
        if not name:
            QMessageBox.warning(self, "تحذير / Warning", "يرجى إدخال اسم الفئة\nPlease enter category name")
            return
            
        conn = self.db_manager.get_connection()
        cursor = conn.cursor()
        
        try:
            if self.category_id:
                # Update existing category
                cursor.execute("""
                    UPDATE material_categories 
                    SET name = ?, description = ?
                    WHERE id = ?
                """, (name, self.description_input.toPlainText(), self.category_id))
            else:
                # Add new category
                cursor.execute("""
                    INSERT INTO material_categories (name, description)
                    VALUES (?, ?)
                """, (name, self.description_input.toPlainText()))
                
            conn.commit()
            self.accept()
            
        except Exception as e:
            QMessageBox.critical(self, "خطأ / Error", f"Error saving category: {str(e)}")
        finally:
            conn.close()

class MaterialsManagementDialog(QDialog):
    def __init__(self, db_manager):
        super().__init__()
        self.db_manager = db_manager
        self.init_ui()
        self.load_data()
        
    def init_ui(self):
        self.setWindowTitle("إدارة المواد / Materials Management")
        self.setFixedSize(800, 600)
        
        layout = QVBoxLayout()
        
        # Create tabs
        tab_widget = QTabWidget()
        
        # Materials tab
        materials_tab = QWidget()
        materials_layout = QVBoxLayout(materials_tab)
        
        # Materials table
        self.materials_table = QTableWidget()
        self.materials_table.setColumnCount(7)
        self.materials_table.setHorizontalHeaderLabels([
            "اسم المادة / Material", "الفئة / Category", "الوحدة / Unit",
            "التكلفة / Cost", "المخزون / Stock", "حد الطلب / Reorder", "الإجراءات / Actions"
        ])
        
        materials_layout.addWidget(self.materials_table)
        
        # Materials buttons
        materials_buttons = QHBoxLayout()
        
        add_material_btn = QPushButton("إضافة مادة / Add Material")
        add_material_btn.clicked.connect(self.add_material)
        materials_buttons.addWidget(add_material_btn)
        
        materials_buttons.addStretch()
        materials_layout.addLayout(materials_buttons)
        
        tab_widget.addTab(materials_tab, "المواد / Materials")
        
        # Categories tab
        categories_tab = QWidget()
        categories_layout = QVBoxLayout(categories_tab)
        
        # Categories table
        self.categories_table = QTableWidget()
        self.categories_table.setColumnCount(3)
        self.categories_table.setHorizontalHeaderLabels([
            "اسم الفئة / Category Name", "الوصف / Description", "الإجراءات / Actions"
        ])
        
        categories_layout.addWidget(self.categories_table)
        
        # Categories buttons
        categories_buttons = QHBoxLayout()
        
        add_category_btn = QPushButton("إضافة فئة / Add Category")
        add_category_btn.clicked.connect(self.add_category)
        categories_buttons.addWidget(add_category_btn)
        
        categories_buttons.addStretch()
        categories_layout.addLayout(categories_buttons)
        
        tab_widget.addTab(categories_tab, "الفئات / Categories")
        
        layout.addWidget(tab_widget)
        
        # Close button
        close_btn = QPushButton("إغلاق / Close")
        close_btn.clicked.connect(self.accept)
        layout.addWidget(close_btn)
        
        self.setLayout(layout)
        
    def load_data(self):
        """Load materials and categories data"""
        self.load_materials()
        self.load_categories()
        
    def load_materials(self):
        """Load materials from database"""
        conn = self.db_manager.get_connection()
        cursor = conn.cursor()
        
        cursor.execute("""
            SELECT m.id, m.name, mc.name as category_name, m.unit,
                   m.unit_cost, m.current_stock, m.reorder_level
            FROM materials m
            LEFT JOIN material_categories mc ON m.category_id = mc.id
            ORDER BY m.name
        """)
        
        materials = cursor.fetchall()
        
        self.materials_table.setRowCount(len(materials))
        
        for row, material in enumerate(materials):
            self.materials_table.setItem(row, 0, QTableWidgetItem(material['name']))
            self.materials_table.setItem(row, 1, QTableWidgetItem(material['category_name'] or '-'))
            self.materials_table.setItem(row, 2, QTableWidgetItem(material['unit']))
            self.materials_table.setItem(row, 3, QTableWidgetItem(f"{material['unit_cost']:.2f}"))
            self.materials_table.setItem(row, 4, QTableWidgetItem(f"{material['current_stock']:.2f}"))
            self.materials_table.setItem(row, 5, QTableWidgetItem(str(material['reorder_level'])))
            
            # Action buttons
            actions_widget = QWidget()
            actions_layout = QHBoxLayout(actions_widget)
            actions_layout.setContentsMargins(5, 5, 5, 5)
            
            edit_btn = QPushButton("تعديل / Edit")
            edit_btn.setFixedSize(70, 25)
            edit_btn.clicked.connect(lambda checked, mid=material['id']: self.edit_material(mid))
            
            delete_btn = QPushButton("حذف / Delete")
            delete_btn.setFixedSize(70, 25)
            delete_btn.setStyleSheet("background-color: #dc3545;")
            delete_btn.clicked.connect(lambda checked, mid=material['id']: self.delete_material(mid))
            
            actions_layout.addWidget(edit_btn)
            actions_layout.addWidget(delete_btn)
            
            self.materials_table.setCellWidget(row, 6, actions_widget)
            
        conn.close()
        
    def load_categories(self):
        """Load categories from database"""
        conn = self.db_manager.get_connection()
        cursor = conn.cursor()
        
        cursor.execute("SELECT id, name, description FROM material_categories ORDER BY name")
        categories = cursor.fetchall()
        
        self.categories_table.setRowCount(len(categories))
        
        for row, category in enumerate(categories):
            self.categories_table.setItem(row, 0, QTableWidgetItem(category['name']))
            self.categories_table.setItem(row, 1, QTableWidgetItem(category['description'] or '-'))
            
            # Action buttons
            actions_widget = QWidget()
            actions_layout = QHBoxLayout(actions_widget)
            actions_layout.setContentsMargins(5, 5, 5, 5)
            
            edit_btn = QPushButton("تعديل / Edit")
            edit_btn.setFixedSize(70, 25)
            edit_btn.clicked.connect(lambda checked, cid=category['id']: self.edit_category(cid))
            
            delete_btn = QPushButton("حذف / Delete")
            delete_btn.setFixedSize(70, 25)
            delete_btn.setStyleSheet("background-color: #dc3545;")
            delete_btn.clicked.connect(lambda checked, cid=category['id']: self.delete_category(cid))
            
            actions_layout.addWidget(edit_btn)
            actions_layout.addWidget(delete_btn)
            
            self.categories_table.setCellWidget(row, 2, actions_widget)
            
        conn.close()
        
    def add_material(self):
        """Add new material"""
        dialog = MaterialDialog(self.db_manager)
        if dialog.exec_() == QDialog.Accepted:
            self.load_materials()
            
    def edit_material(self, material_id):
        """Edit material"""
        dialog = MaterialDialog(self.db_manager, material_id)
        if dialog.exec_() == QDialog.Accepted:
            self.load_materials()
            
    def delete_material(self, material_id):
        """Delete material"""
        reply = QMessageBox.question(self, "حذف المادة / Delete Material",
                                   "هل تريد حذف هذه المادة؟\nDo you want to delete this material?",
                                   QMessageBox.Yes | QMessageBox.No)
        
        if reply == QMessageBox.Yes:
            conn = self.db_manager.get_connection()
            cursor = conn.cursor()
            
            try:
                cursor.execute("DELETE FROM materials WHERE id = ?", (material_id,))
                conn.commit()
                
                QMessageBox.information(self, "نجح / Success", 
                                      "تم حذف المادة بنجاح\nMaterial deleted successfully")
                self.load_materials()
                
            except Exception as e:
                QMessageBox.critical(self, "خطأ / Error", f"Error deleting material: {str(e)}")
            finally:
                conn.close()
                
    def add_category(self):
        """Add new category"""
        dialog = CategoryDialog(self.db_manager)
        if dialog.exec_() == QDialog.Accepted:
            self.load_categories()
            
    def edit_category(self, category_id):
        """Edit category"""
        dialog = CategoryDialog(self.db_manager, category_id)
        if dialog.exec_() == QDialog.Accepted:
            self.load_categories()
            
    def delete_category(self, category_id):
        """Delete category"""
        reply = QMessageBox.question(self, "حذف الفئة / Delete Category",
                                   "هل تريد حذف هذه الفئة؟\nDo you want to delete this category?",
                                   QMessageBox.Yes | QMessageBox.No)
        
        if reply == QMessageBox.Yes:
            conn = self.db_manager.get_connection()
            cursor = conn.cursor()
            
            try:
                # Check if category is used by materials
                cursor.execute("SELECT COUNT(*) as count FROM materials WHERE category_id = ?", (category_id,))
                result = cursor.fetchone()
                
                if result['count'] > 0:
                    QMessageBox.warning(self, "تحذير / Warning", 
                                      "لا يمكن حذف الفئة لأنها مستخدمة\nCannot delete category as it is in use")
                    return
                
                cursor.execute("DELETE FROM material_categories WHERE id = ?", (category_id,))
                conn.commit()
                
                QMessageBox.information(self, "نجح / Success", 
                                      "تم حذف الفئة بنجاح\nCategory deleted successfully")
                self.load_categories()
                
            except Exception as e:
                QMessageBox.critical(self, "خطأ / Error", f"Error deleting category: {str(e)}")
            finally:
                conn.close()
