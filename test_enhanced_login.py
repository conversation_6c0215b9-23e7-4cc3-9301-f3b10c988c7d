#!/usr/bin/env python3
"""
Test enhanced login window with better visibility
"""

import sys
from PyQt5.QtWidgets import QApplication
from PyQt5.QtCore import QTimer
from database.db_manager import DatabaseManager
from ui.login_window import <PERSON><PERSON><PERSON>indow
from utils.translator import Translator
from utils.font_manager import FontManager
from utils.locale_manager import LocaleManager
from utils.cultural_manager import CulturalManager

def test_enhanced_login():
    """Test enhanced login window"""
    app = QApplication(sys.argv)
    
    # Initialize managers
    db_manager = DatabaseManager()
    translator = Translator()
    locale_manager = LocaleManager()
    cultural_manager = CulturalManager()
    font_manager = FontManager()
    
    # Create login window
    login_window = LoginWindow(db_manager, translator, font_manager, 
                              locale_manager, cultural_manager)
    
    def on_login_success(user_data):
        """Handle successful login"""
        print(f"✅ Login successful!")
        print(f"User: {user_data['username']}")
        print(f"Role: {user_data['role']}")
        print("🎉 Login window is working correctly!")
        app.quit()
    
    def check_visibility():
        """Check if input fields are visible"""
        print("=== Checking Login Window Visibility ===")
        
        # Check username field
        if login_window.username_input.isVisible():
            print("✅ Username field is visible")
            print(f"   Text: '{login_window.username_input.text()}'")
            print(f"   Placeholder: '{login_window.username_input.placeholderText()}'")
        else:
            print("❌ Username field is NOT visible")
            
        # Check password field
        if login_window.password_input.isVisible():
            print("✅ Password field is visible")
            print(f"   Placeholder: '{login_window.password_input.placeholderText()}'")
        else:
            print("❌ Password field is NOT visible")
            
        # Check login button
        if login_window.login_button.isVisible():
            print("✅ Login button is visible")
            print(f"   Text: '{login_window.login_button.text()}'")
        else:
            print("❌ Login button is NOT visible")
            
        print("\n=== Instructions ===")
        print("1. Check if you can see the username and password fields")
        print("2. The fields should be pre-filled with 'admin' and 'admin123'")
        print("3. Click the 'Login' button to test functionality")
        print("4. If fields are not visible, there's a styling issue")
    
    # Connect login success signal
    login_window.login_successful.connect(on_login_success)
    
    # Check visibility after window is shown
    QTimer.singleShot(1000, check_visibility)
    
    # Show login window
    login_window.show()
    
    return app.exec_()

if __name__ == "__main__":
    print("=== Enhanced Login Window Test ===")
    print("Testing login window with improved visibility...")
    
    sys.exit(test_enhanced_login())
