@echo off
echo ========================================
echo Al-Hassan Stone Factory - Executable Builder
echo ========================================
echo.

echo Installing required packages...
pip install pyinstaller cx_freeze auto-py-to-exe

echo.
echo Building executable with PyInstaller...
echo.

REM Clean previous builds
if exist "dist" rmdir /s /q "dist"
if exist "build" rmdir /s /q "build"

REM Create executable using directory distribution (more reliable)
pyinstaller --name=AlHassanStoneFactory --windowed --onedir --hidden-import=PyQt5.sip --hidden-import=sqlite3 --hidden-import=PyQt5.QtCore --hidden-import=PyQt5.QtGui --hidden-import=PyQt5.QtWidgets main.py

echo.
if exist "dist\AlHassanStoneFactory\AlHassanStoneFactory.exe" (
    echo ========================================
    echo SUCCESS! Executable created successfully!
    echo ========================================
    echo.
    echo Location: dist\AlHassanStoneFactory\
    echo Executable: AlHassanStoneFactory.exe
    echo.
    echo To run the application:
    echo 1. Navigate to: dist\AlHassanStoneFactory\
    echo 2. Double-click: AlHassanStoneFactory.exe
    echo.
    echo Opening the directory...
    explorer "dist\AlHassanStoneFactory"
) else (
    echo ========================================
    echo Build failed or executable not found.
    echo ========================================
    echo.
    echo You can still run the application using Python:
    echo python main.py
)

echo.
pause
