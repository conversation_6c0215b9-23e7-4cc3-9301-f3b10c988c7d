from PyQt5.QtWidgets import QLineEdit, QDoubleSpinBox
from PyQt5.QtCore import pyqtSignal, QValidator
from PyQt5.QtGui import QValidator
from utils.cultural_manager import cultural_manager
import re

class CulturalNumberValidator(QValidator):
    """Validator for cultural number formats"""
    
    def __init__(self, parent=None):
        super().__init__(parent)
        
    def validate(self, input_str, pos):
        """Validate number input according to cultural format"""
        if not input_str:
            return QValidator.Acceptable, input_str, pos
            
        # Get cultural settings
        preset = cultural_manager.cultural_presets.get(
            cultural_manager.current_culture,
            cultural_manager.cultural_presets['en-US']
        )
        
        decimal_sep = preset.get('decimal_separator', '.')
        thousand_sep = preset.get('thousand_separator', ',')
        
        # Allow cultural number digits
        allowed_digits = cultural_manager.number_systems.get(
            cultural_manager.number_format,
            cultural_manager.number_systems['western']
        )
        
        # Create pattern for validation
        digit_pattern = f"[{re.escape(allowed_digits)}{re.escape(decimal_sep)}{re.escape(thousand_sep)}+-]"
        
        if re.match(f"^{digit_pattern}*$", input_str):
            return QValidator.Acceptable, input_str, pos
        else:
            return QValidator.Invalid, input_str, pos

class CulturalNumberEdit(QLineEdit):
    """Number input with cultural formatting"""
    
    value_changed = pyqtSignal(float)
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.setValidator(CulturalNumberValidator())
        self.textChanged.connect(self.on_text_changed)
        
    def on_text_changed(self, text):
        """Handle text change"""
        try:
            value = cultural_manager.parse_number(text)
            self.value_changed.emit(value)
        except:
            pass
            
    def set_value(self, value):
        """Set numeric value"""
        formatted = cultural_manager.format_number(value)
        self.setText(formatted)
        
    def get_value(self):
        """Get numeric value"""
        return cultural_manager.parse_number(self.text())

class CulturalCurrencyEdit(CulturalNumberEdit):
    """Currency input with cultural formatting"""
    
    def __init__(self, parent=None):
        super().__init__(parent)
        
    def set_value(self, value):
        """Set currency value"""
        formatted = cultural_manager.format_currency(value, show_symbol=False)
        self.setText(formatted)
        
    def get_display_text(self):
        """Get formatted currency text with symbol"""
        value = self.get_value()
        return cultural_manager.format_currency(value, show_symbol=True)
