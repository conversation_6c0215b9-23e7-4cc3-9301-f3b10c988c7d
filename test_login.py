#!/usr/bin/env python3
"""
Test script for login functionality
"""

import sys
import os
from database.db_manager import DatabaseManager

def test_login():
    """Test login functionality"""
    print("Testing login functionality...")
    
    # Initialize database manager
    db_manager = DatabaseManager()
    
    # Test with default admin credentials
    username = "admin"
    password = "admin123"
    
    print(f"Attempting to login with username: {username}")
    
    try:
        user_data = db_manager.authenticate_user(username, password)
        
        if user_data:
            print("✅ Login successful!")
            print(f"User ID: {user_data['id']}")
            print(f"Username: {user_data['username']}")
            print(f"Full Name: {user_data['full_name']}")
            print(f"Role: {user_data['role']}")
            return True
        else:
            print("❌ Login failed - Invalid credentials")
            return False
            
    except Exception as e:
        print(f"❌ Login failed with error: {e}")
        return False

def check_database():
    """Check database structure"""
    print("\nChecking database structure...")
    
    db_manager = DatabaseManager()
    conn = db_manager.get_connection()
    cursor = conn.cursor()
    
    try:
        # Check if users table exists
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='users'")
        if cursor.fetchone():
            print("✅ Users table exists")
            
            # Check if admin user exists
            cursor.execute("SELECT * FROM users WHERE username = 'admin'")
            admin_user = cursor.fetchone()
            
            if admin_user:
                print("✅ Admin user exists")
                print(f"Admin user data: {dict(admin_user)}")
            else:
                print("❌ Admin user not found")
                print("Creating admin user...")
                db_manager.create_default_data()
                print("✅ Admin user created")
        else:
            print("❌ Users table not found")
            print("Creating database tables...")
            db_manager.create_tables()
            db_manager.create_default_data()
            print("✅ Database tables created")
            
    except Exception as e:
        print(f"❌ Database check failed: {e}")
    finally:
        conn.close()

if __name__ == "__main__":
    print("=== Login Test Script ===")
    
    # Check database first
    check_database()
    
    # Test login
    success = test_login()
    
    if success:
        print("\n🎉 Login test passed! The application should work correctly.")
    else:
        print("\n⚠️ Login test failed. Please check the database and try again.")
