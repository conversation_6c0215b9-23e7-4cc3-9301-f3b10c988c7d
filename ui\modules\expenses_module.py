from PyQt5.QtWidgets import (QWidget, QVBoxLayout, QHBoxLayout, QPushButton, 
                            QTableWidget, QTableWidgetItem, QLineEdit, QLabel,
                            QFormLayout, QDialog, QMessageBox, QComboBox,
                            QGroupBox, QDateEdit, QTextEdit, QHeaderView,
                            QSpinBox, QDoubleSpinBox, QTabWidget, QFileDialog)
from PyQt5.QtCore import Qt, QDate
from PyQt5.QtGui import QFont, QPixmap
import sqlite3
from datetime import datetime
import os

class ExpenseDialog(QDialog):
    def __init__(self, parent=None, expense_data=None, translator=None):
        super().__init__(parent)
        self.translator = translator
        self.expense_data = expense_data
        self.init_ui()
        
        if expense_data:
            self.populate_fields()
            
    def init_ui(self):
        self.setWindowTitle("إضافة/تعديل مصروف")
        self.setModal(True)
        self.resize(500, 600)
        
        layout = QVBoxLayout()
        
        # Expense form
        form_layout = QFormLayout()
        
        self.expense_date = QDateEdit()
        self.expense_date.setDate(QDate.currentDate())
        self.expense_date.setCalendarPopup(True)
        
        self.category_combo = QComboBox()
        self.category_combo.addItems([
            "وقود", "صيانة", "رواتب", "إيجار", "كهرباء", "ماء", 
            "هاتف", "إنترنت", "تأمين", "ضرائب", "مواد خام", 
            "أدوات", "نقل", "تسويق", "أخرى"
        ])
        self.category_combo.setEditable(True)
        
        self.subcategory_edit = QLineEdit()
        
        self.amount_edit = QDoubleSpinBox()
        self.amount_edit.setMaximum(999999)
        self.amount_edit.setSuffix(" ريال")
        
        self.payment_method_combo = QComboBox()
        self.payment_method_combo.addItems(["نقدي", "شيك", "تحويل بنكي", "بطاقة ائتمان"])
        
        self.vendor_edit = QLineEdit()
        self.invoice_number_edit = QLineEdit()
        
        self.description_edit = QTextEdit()
        self.notes_edit = QTextEdit()
        
        # Receipt attachment
        self.receipt_path = ""
        self.receipt_btn = QPushButton("إرفاق إيصال")
        self.receipt_btn.clicked.connect(self.attach_receipt)
        self.receipt_label = QLabel("لم يتم إرفاق إيصال")
        
        form_layout.addRow("التاريخ:", self.expense_date)
        form_layout.addRow("الفئة:", self.category_combo)
        form_layout.addRow("الفئة الفرعية:", self.subcategory_edit)
        form_layout.addRow("المبلغ:", self.amount_edit)
        form_layout.addRow("طريقة الدفع:", self.payment_method_combo)
        form_layout.addRow("المورد/الجهة:", self.vendor_edit)
        form_layout.addRow("رقم الفاتورة:", self.invoice_number_edit)
        form_layout.addRow("الوصف:", self.description_edit)
        form_layout.addRow("ملاحظات:", self.notes_edit)
        form_layout.addRow("الإيصال:", self.receipt_btn)
        form_layout.addRow("", self.receipt_label)
        
        layout.addLayout(form_layout)
        
        # Buttons
        button_layout = QHBoxLayout()
        self.save_btn = QPushButton("حفظ")
        self.cancel_btn = QPushButton("إلغاء")
        
        self.save_btn.clicked.connect(self.accept)
        self.cancel_btn.clicked.connect(self.reject)
        
        button_layout.addWidget(self.save_btn)
        button_layout.addWidget(self.cancel_btn)
        layout.addLayout(button_layout)
        
        self.setLayout(layout)
        
    def attach_receipt(self):
        file_path, _ = QFileDialog.getOpenFileName(
            self, "اختر إيصال", "", 
            "Image Files (*.png *.jpg *.jpeg *.pdf);;All Files (*)"
        )
        
        if file_path:
            self.receipt_path = file_path
            self.receipt_label.setText(f"تم إرفاق: {os.path.basename(file_path)}")
            
    def populate_fields(self):
        if self.expense_data:
            self.expense_date.setDate(QDate.fromString(self.expense_data.get('expense_date', ''), 'yyyy-MM-dd'))
            self.subcategory_edit.setText(str(self.expense_data.get('subcategory', '')))
            self.amount_edit.setValue(float(self.expense_data.get('amount', 0)))
            self.vendor_edit.setText(str(self.expense_data.get('vendor', '')))
            self.invoice_number_edit.setText(str(self.expense_data.get('invoice_number', '')))
            self.description_edit.setPlainText(str(self.expense_data.get('description', '')))
            self.notes_edit.setPlainText(str(self.expense_data.get('notes', '')))
            
    def get_data(self):
        return {
            'expense_date': self.expense_date.date().toString('yyyy-MM-dd'),
            'category': self.category_combo.currentText(),
            'subcategory': self.subcategory_edit.text(),
            'amount': self.amount_edit.value(),
            'payment_method': self.payment_method_combo.currentText(),
            'vendor': self.vendor_edit.text(),
            'invoice_number': self.invoice_number_edit.text(),
            'description': self.description_edit.toPlainText(),
            'notes': self.notes_edit.toPlainText(),
            'receipt_path': self.receipt_path
        }

class ExpensesModule(QWidget):
    def __init__(self, db_manager, translator, parent=None):
        super().__init__(parent)
        self.db_manager = db_manager
        self.translator = translator
        self.init_ui()
        self.load_expenses()
        
    def init_ui(self):
        layout = QVBoxLayout()
        
        # Header
        header_layout = QHBoxLayout()
        title_label = QLabel("إدارة المصروفات")
        title_label.setFont(QFont("Arial", 16, QFont.Bold))
        header_layout.addWidget(title_label)
        header_layout.addStretch()
        
        # Search and filters
        self.search_edit = QLineEdit()
        self.search_edit.setPlaceholderText("البحث في المصروفات...")
        self.search_edit.textChanged.connect(self.search_expenses)
        
        self.date_from = QDateEdit()
        self.date_from.setDate(QDate.currentDate().addDays(-30))
        self.date_from.setCalendarPopup(True)
        
        self.date_to = QDateEdit()
        self.date_to.setDate(QDate.currentDate())
        self.date_to.setCalendarPopup(True)
        
        self.category_filter = QComboBox()
        self.category_filter.addItems([
            "الكل", "وقود", "صيانة", "رواتب", "إيجار", "كهرباء", 
            "ماء", "هاتف", "إنترنت", "تأمين", "ضرائب", "مواد خام", 
            "أدوات", "نقل", "تسويق", "أخرى"
        ])
        self.category_filter.currentTextChanged.connect(self.filter_expenses)
        
        header_layout.addWidget(QLabel("البحث:"))
        header_layout.addWidget(self.search_edit)
        header_layout.addWidget(QLabel("من:"))
        header_layout.addWidget(self.date_from)
        header_layout.addWidget(QLabel("إلى:"))
        header_layout.addWidget(self.date_to)
        header_layout.addWidget(QLabel("الفئة:"))
        header_layout.addWidget(self.category_filter)
        
        layout.addLayout(header_layout)
        
        # Buttons
        button_layout = QHBoxLayout()
        self.add_btn = QPushButton("إضافة مصروف جديد")
        self.edit_btn = QPushButton("تعديل")
        self.delete_btn = QPushButton("حذف")
        self.refresh_btn = QPushButton("تحديث")
        self.export_btn = QPushButton("تصدير")
        self.report_btn = QPushButton("تقرير المصروفات")
        
        self.add_btn.clicked.connect(self.add_expense)
        self.edit_btn.clicked.connect(self.edit_expense)
        self.delete_btn.clicked.connect(self.delete_expense)
        self.refresh_btn.clicked.connect(self.load_expenses)
        self.export_btn.clicked.connect(self.export_expenses)
        self.report_btn.clicked.connect(self.generate_report)
        
        button_layout.addWidget(self.add_btn)
        button_layout.addWidget(self.edit_btn)
        button_layout.addWidget(self.delete_btn)
        button_layout.addWidget(self.refresh_btn)
        button_layout.addWidget(self.export_btn)
        button_layout.addWidget(self.report_btn)
        button_layout.addStretch()
        
        layout.addLayout(button_layout)
        
        # Create tabs
        tab_widget = QTabWidget()
        
        # Expenses List Tab
        expenses_tab = QWidget()
        expenses_layout = QVBoxLayout()
        
        self.table = QTableWidget()
        self.table.setColumnCount(9)
        self.table.setHorizontalHeaderLabels([
            "الرقم", "التاريخ", "الفئة", "الفئة الفرعية", "المبلغ", 
            "طريقة الدفع", "المورد", "رقم الفاتورة", "الوصف"
        ])
        
        # Set table properties
        self.table.setSelectionBehavior(QTableWidget.SelectRows)
        self.table.setAlternatingRowColors(True)
        self.table.horizontalHeader().setStretchLastSection(True)
        self.table.horizontalHeader().setSectionResizeMode(QHeaderView.Stretch)
        
        expenses_layout.addWidget(self.table)
        expenses_tab.setLayout(expenses_layout)
        tab_widget.addTab(expenses_tab, "قائمة المصروفات")
        
        # Summary Tab
        summary_tab = QWidget()
        summary_layout = QVBoxLayout()
        
        # Monthly summary
        monthly_group = QGroupBox("ملخص شهري")
        monthly_layout = QVBoxLayout()
        
        self.monthly_table = QTableWidget()
        self.monthly_table.setColumnCount(3)
        self.monthly_table.setHorizontalHeaderLabels(["الشهر", "إجمالي المصروفات", "عدد المعاملات"])
        
        monthly_layout.addWidget(self.monthly_table)
        monthly_group.setLayout(monthly_layout)
        
        # Category summary
        category_group = QGroupBox("ملخص حسب الفئة")
        category_layout = QVBoxLayout()
        
        self.category_table = QTableWidget()
        self.category_table.setColumnCount(3)
        self.category_table.setHorizontalHeaderLabels(["الفئة", "إجمالي المصروفات", "النسبة المئوية"])
        
        category_layout.addWidget(self.category_table)
        category_group.setLayout(category_layout)
        
        summary_layout.addWidget(monthly_group)
        summary_layout.addWidget(category_group)
        summary_tab.setLayout(summary_layout)
        tab_widget.addTab(summary_tab, "الملخص")
        
        layout.addWidget(tab_widget)
        
        # Statistics
        stats_layout = QHBoxLayout()
        self.total_expenses_label = QLabel("إجمالي المصروفات: 0 ريال")
        self.monthly_expenses_label = QLabel("مصروفات الشهر: 0 ريال")
        self.daily_expenses_label = QLabel("مصروفات اليوم: 0 ريال")
        self.avg_expense_label = QLabel("متوسط المصروف: 0 ريال")
        
        stats_layout.addWidget(self.total_expenses_label)
        stats_layout.addWidget(self.monthly_expenses_label)
        stats_layout.addWidget(self.daily_expenses_label)
        stats_layout.addWidget(self.avg_expense_label)
        stats_layout.addStretch()
        
        layout.addLayout(stats_layout)
        
        self.setLayout(layout)
        
    def load_expenses(self):
        try:
            conn = self.db_manager.get_connection()
            cursor = conn.cursor()
            
            cursor.execute("""
                SELECT id, expense_date, category, subcategory, amount,
                       payment_method, vendor, invoice_number, description
                FROM expenses 
                ORDER BY expense_date DESC
            """)
            
            expenses = cursor.fetchall()
            
            self.table.setRowCount(len(expenses))
            
            for row, expense in enumerate(expenses):
                for col, value in enumerate(expense):
                    if col == 4:  # amount column
                        value = f"{value:,.2f} ريال"
                    elif col == 1:  # date column
                        value = str(value)[:10]
                    
                    item = QTableWidgetItem(str(value))
                    item.setTextAlignment(Qt.AlignCenter)
                    self.table.setItem(row, col, item)
            
            # Update statistics and summaries
            self.update_statistics(expenses)
            self.load_summaries()
            
            conn.close()
            
        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"خطأ في تحميل المصروفات: {str(e)}")
            
    def load_summaries(self):
        self.load_monthly_summary()
        self.load_category_summary()
        
    def load_monthly_summary(self):
        try:
            conn = self.db_manager.get_connection()
            cursor = conn.cursor()
            
            cursor.execute("""
                SELECT strftime('%Y-%m', expense_date) as month,
                       SUM(amount) as total_amount,
                       COUNT(*) as transaction_count
                FROM expenses
                GROUP BY strftime('%Y-%m', expense_date)
                ORDER BY month DESC
                LIMIT 12
            """)
            
            monthly_data = cursor.fetchall()
            
            self.monthly_table.setRowCount(len(monthly_data))
            
            for row, data in enumerate(monthly_data):
                month_item = QTableWidgetItem(str(data[0]))
                amount_item = QTableWidgetItem(f"{data[1]:,.2f} ريال")
                count_item = QTableWidgetItem(str(data[2]))
                
                month_item.setTextAlignment(Qt.AlignCenter)
                amount_item.setTextAlignment(Qt.AlignCenter)
                count_item.setTextAlignment(Qt.AlignCenter)
                
                self.monthly_table.setItem(row, 0, month_item)
                self.monthly_table.setItem(row, 1, amount_item)
                self.monthly_table.setItem(row, 2, count_item)
            
            conn.close()
            
        except Exception as e:
            print(f"Error loading monthly summary: {e}")
            
    def load_category_summary(self):
        try:
            conn = self.db_manager.get_connection()
            cursor = conn.cursor()
            
            cursor.execute("""
                SELECT category,
                       SUM(amount) as total_amount,
                       (SUM(amount) * 100.0 / (SELECT SUM(amount) FROM expenses)) as percentage
                FROM expenses
                GROUP BY category
                ORDER BY total_amount DESC
            """)
            
            category_data = cursor.fetchall()
            
            self.category_table.setRowCount(len(category_data))
            
            for row, data in enumerate(category_data):
                category_item = QTableWidgetItem(str(data[0]))
                amount_item = QTableWidgetItem(f"{data[1]:,.2f} ريال")
                percentage_item = QTableWidgetItem(f"{data[2]:.1f}%")
                
                category_item.setTextAlignment(Qt.AlignCenter)
                amount_item.setTextAlignment(Qt.AlignCenter)
                percentage_item.setTextAlignment(Qt.AlignCenter)
                
                self.category_table.setItem(row, 0, category_item)
                self.category_table.setItem(row, 1, amount_item)
                self.category_table.setItem(row, 2, percentage_item)
            
            conn.close()
            
        except Exception as e:
            print(f"Error loading category summary: {e}")
            
    def update_statistics(self, expenses):
        if not expenses:
            return
            
        total_amount = sum(expense[4] for expense in expenses)
        
        # Monthly expenses (current month)
        current_month = datetime.now().strftime('%Y-%m')
        monthly_amount = sum(expense[4] for expense in expenses 
                           if str(expense[1])[:7] == current_month)
        
        # Daily expenses (today)
        today = datetime.now().strftime('%Y-%m-%d')
        daily_amount = sum(expense[4] for expense in expenses 
                         if str(expense[1])[:10] == today)
        
        # Average expense
        avg_expense = total_amount / len(expenses) if expenses else 0
        
        self.total_expenses_label.setText(f"إجمالي المصروفات: {total_amount:,.2f} ريال")
        self.monthly_expenses_label.setText(f"مصروفات الشهر: {monthly_amount:,.2f} ريال")
        self.daily_expenses_label.setText(f"مصروفات اليوم: {daily_amount:,.2f} ريال")
        self.avg_expense_label.setText(f"متوسط المصروف: {avg_expense:,.2f} ريال")
        
    def search_expenses(self):
        search_text = self.search_edit.text().lower()
        
        for row in range(self.table.rowCount()):
            show_row = False
            for col in range(self.table.columnCount()):
                item = self.table.item(row, col)
                if item and search_text in item.text().lower():
                    show_row = True
                    break
            self.table.setRowHidden(row, not show_row)
            
    def filter_expenses(self):
        category_filter = self.category_filter.currentText()
        
        for row in range(self.table.rowCount()):
            if category_filter == "الكل":
                self.table.setRowHidden(row, False)
            else:
                category_item = self.table.item(row, 2)  # category column
                if category_item:
                    show_row = category_item.text() == category_filter
                    self.table.setRowHidden(row, not show_row)
                    
    def add_expense(self):
        dialog = ExpenseDialog(self, translator=self.translator)
        if dialog.exec_() == QDialog.Accepted:
            data = dialog.get_data()
            self.save_expense(data)
            
    def edit_expense(self):
        current_row = self.table.currentRow()
        if current_row < 0:
            QMessageBox.warning(self, "تحذير", "يرجى اختيار مصروف للتعديل")
            return
            
        expense_id = self.table.item(current_row, 0).text()
        expense_data = self.get_expense_data(expense_id)
        
        dialog = ExpenseDialog(self, expense_data, self.translator)
        if dialog.exec_() == QDialog.Accepted:
            data = dialog.get_data()
            self.update_expense(expense_id, data)
            
    def delete_expense(self):
        current_row = self.table.currentRow()
        if current_row < 0:
            QMessageBox.warning(self, "تحذير", "يرجى اختيار مصروف للحذف")
            return
            
        expense_description = self.table.item(current_row, 8).text()
        reply = QMessageBox.question(
            self, "تأكيد الحذف", 
            f"هل أنت متأكد من حذف المصروف '{expense_description}'؟",
            QMessageBox.Yes | QMessageBox.No
        )
        
        if reply == QMessageBox.Yes:
            expense_id = self.table.item(current_row, 0).text()
            self.remove_expense(expense_id)
            
    def export_expenses(self):
        QMessageBox.information(self, "تصدير", "سيتم تطوير ميزة التصدير قريباً")
        
    def generate_report(self):
        QMessageBox.information(self, "تقرير", "سيتم تطوير ميزة التقارير قريباً")
        
    def save_expense(self, data):
        try:
            conn = self.db_manager.get_connection()
            cursor = conn.cursor()
            
            cursor.execute("""
                INSERT INTO expenses (expense_date, category, subcategory, amount,
                                    payment_method, vendor, invoice_number, description,
                                    notes, receipt_path)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            """, (
                data['expense_date'], data['category'], data['subcategory'],
                data['amount'], data['payment_method'], data['vendor'],
                data['invoice_number'], data['description'], data['notes'],
                data['receipt_path']
            ))
            
            conn.commit()
            conn.close()
            
            QMessageBox.information(self, "نجح", "تم إضافة المصروف بنجاح")
            self.load_expenses()
            
        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"خطأ في إضافة المصروف: {str(e)}")
            
    def update_expense(self, expense_id, data):
        try:
            conn = self.db_manager.get_connection()
            cursor = conn.cursor()
            
            cursor.execute("""
                UPDATE expenses 
                SET expense_date=?, category=?, subcategory=?, amount=?,
                    payment_method=?, vendor=?, invoice_number=?, description=?,
                    notes=?, receipt_path=?
                WHERE id=?
            """, (
                data['expense_date'], data['category'], data['subcategory'],
                data['amount'], data['payment_method'], data['vendor'],
                data['invoice_number'], data['description'], data['notes'],
                data['receipt_path'], expense_id
            ))
            
            conn.commit()
            conn.close()
            
            QMessageBox.information(self, "نجح", "تم تحديث المصروف بنجاح")
            self.load_expenses()
            
        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"خطأ في تحديث المصروف: {str(e)}")
            
    def remove_expense(self, expense_id):
        try:
            conn = self.db_manager.get_connection()
            cursor = conn.cursor()
            
            cursor.execute("DELETE FROM expenses WHERE id=?", (expense_id,))
            
            conn.commit()
            conn.close()
            
            QMessageBox.information(self, "نجح", "تم حذف المصروف بنجاح")
            self.load_expenses()
            
        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"خطأ في حذف المصروف: {str(e)}")
            
    def get_expense_data(self, expense_id):
        try:
            conn = self.db_manager.get_connection()
            cursor = conn.cursor()
            
            cursor.execute("""
                SELECT expense_date, category, subcategory, amount, payment_method,
                       vendor, invoice_number, description, notes, receipt_path
                FROM expenses WHERE id=?
            """, (expense_id,))
            
            result = cursor.fetchone()
            conn.close()
            
            if result:
                return {
                    'expense_date': result[0],
                    'category': result[1],
                    'subcategory': result[2],
                    'amount': result[3],
                    'payment_method': result[4],
                    'vendor': result[5],
                    'invoice_number': result[6],
                    'description': result[7],
                    'notes': result[8],
                    'receipt_path': result[9]
                }
            return {}
            
        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"خطأ في جلب بيانات المصروف: {str(e)}")
            return {}
