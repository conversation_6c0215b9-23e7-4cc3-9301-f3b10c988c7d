#!/usr/bin/env python3
"""
Simple Button Test - اختبار بسيط للأزرار
Very simple button test without complex imports
"""

import sys
from PyQt5.QtWidgets import (QApplication, QMainWindow, QWidget, QVBoxLayout, 
                            QHBoxLayout, QGridLayout, QLabel, QPushButton, 
                            QFrame, QMessageBox)
from PyQt5.QtCore import Qt
from PyQt5.QtGui import QFont

class SimpleButtonTest(QMainWindow):
    """Simple button test window"""
    
    def __init__(self):
        super().__init__()
        self.init_ui()
        
    def init_ui(self):
        """Initialize the UI"""
        self.setWindowTitle("🔘 اختبار الأزرار البسيط | Simple Button Test")
        self.setGeometry(100, 100, 800, 600)
        
        # Apply basic styling
        self.setStyleSheet("""
            QMainWindow {
                background-color: #f8f9fa;
            }
            QLabel {
                color: #333333;
            }
            QPushButton {
                background-color: #0078d4;
                color: white;
                border: none;
                border-radius: 8px;
                padding: 12px 20px;
                font-weight: bold;
                font-size: 10pt;
                min-height: 20px;
            }
            QPushButton:hover {
                background-color: #106ebe;
            }
            QPushButton:pressed {
                background-color: #005a9e;
            }
            QPushButton:disabled {
                background-color: #6c757d;
            }
        """)
        
        # Create central widget
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        # Main layout
        main_layout = QVBoxLayout(central_widget)
        main_layout.setSpacing(20)
        main_layout.setContentsMargins(30, 30, 30, 30)
        
        # Title
        title = QLabel("🔘 اختبار الأزرار البسيط")
        title.setFont(QFont("Arial", 20, QFont.Bold))
        title.setAlignment(Qt.AlignCenter)
        title.setStyleSheet("color: #0078d4; margin-bottom: 20px;")
        main_layout.addWidget(title)
        
        # Create button sections
        self.create_basic_buttons(main_layout)
        self.create_colored_buttons(main_layout)
        self.create_size_buttons(main_layout)
        self.create_icon_buttons(main_layout)
        
    def create_basic_buttons(self, parent_layout):
        """Create basic buttons"""
        section_title = QLabel("🔹 الأزرار الأساسية")
        section_title.setFont(QFont("Arial", 14, QFont.Bold))
        section_title.setStyleSheet("color: #333; margin: 10px 0;")
        parent_layout.addWidget(section_title)
        
        frame = QFrame()
        layout = QHBoxLayout(frame)
        layout.setSpacing(15)
        
        buttons = [
            ("زر عادي", True),
            ("زر مُفعل", True),
            ("زر معطل", False),
            ("زر افتراضي", True)
        ]
        
        for text, enabled in buttons:
            btn = QPushButton(text)
            btn.setMinimumSize(120, 50)
            btn.setEnabled(enabled)
            btn.clicked.connect(lambda checked, t=text: self.show_clicked(t))
            layout.addWidget(btn)
        
        parent_layout.addWidget(frame)
        
    def create_colored_buttons(self, parent_layout):
        """Create colored buttons"""
        section_title = QLabel("🌈 الأزرار الملونة")
        section_title.setFont(QFont("Arial", 14, QFont.Bold))
        section_title.setStyleSheet("color: #333; margin: 10px 0;")
        parent_layout.addWidget(section_title)
        
        frame = QFrame()
        layout = QHBoxLayout(frame)
        layout.setSpacing(15)
        
        colored_buttons = [
            ("أساسي", "#0078d4", "#106ebe"),
            ("نجاح", "#28a745", "#218838"),
            ("تحذير", "#ffc107", "#e0a800"),
            ("خطر", "#dc3545", "#c82333"),
            ("معلومات", "#17a2b8", "#138496")
        ]
        
        for text, bg_color, hover_color in colored_buttons:
            btn = QPushButton(text)
            btn.setMinimumSize(120, 50)
            btn.setStyleSheet(f"""
                QPushButton {{
                    background-color: {bg_color};
                    color: white;
                    border: none;
                    border-radius: 8px;
                    padding: 12px 20px;
                    font-weight: bold;
                }}
                QPushButton:hover {{
                    background-color: {hover_color};
                }}
            """)
            btn.clicked.connect(lambda checked, t=text: self.show_clicked(t))
            layout.addWidget(btn)
        
        parent_layout.addWidget(frame)
        
    def create_size_buttons(self, parent_layout):
        """Create different sized buttons"""
        section_title = QLabel("📏 أحجام مختلفة")
        section_title.setFont(QFont("Arial", 14, QFont.Bold))
        section_title.setStyleSheet("color: #333; margin: 10px 0;")
        parent_layout.addWidget(section_title)
        
        frame = QFrame()
        layout = QHBoxLayout(frame)
        layout.setSpacing(15)
        layout.setAlignment(Qt.AlignCenter)
        
        sizes = [
            ("صغير", (80, 30)),
            ("متوسط", (120, 40)),
            ("كبير", (160, 50)),
            ("كبير جداً", (200, 60))
        ]
        
        for text, size in sizes:
            btn = QPushButton(text)
            btn.setFixedSize(*size)
            btn.clicked.connect(lambda checked, t=text: self.show_clicked(t))
            layout.addWidget(btn)
        
        parent_layout.addWidget(frame)
        
    def create_icon_buttons(self, parent_layout):
        """Create icon buttons"""
        section_title = QLabel("🎨 أزرار بأيقونات")
        section_title.setFont(QFont("Arial", 14, QFont.Bold))
        section_title.setStyleSheet("color: #333; margin: 10px 0;")
        parent_layout.addWidget(section_title)
        
        frame = QFrame()
        layout = QHBoxLayout(frame)
        layout.setSpacing(15)
        
        icon_buttons = [
            ("💾 حفظ", "#28a745"),
            ("✏️ تعديل", "#ffc107"),
            ("🗑️ حذف", "#dc3545"),
            ("👁️ عرض", "#17a2b8"),
            ("📄 طباعة", "#6c757d"),
            ("📤 تصدير", "#0078d4")
        ]
        
        for text, color in icon_buttons:
            btn = QPushButton(text)
            btn.setMinimumSize(100, 50)
            btn.setStyleSheet(f"""
                QPushButton {{
                    background-color: {color};
                    color: white;
                    border: none;
                    border-radius: 8px;
                    padding: 8px 16px;
                    font-weight: bold;
                }}
                QPushButton:hover {{
                    background-color: {self.darken_color(color)};
                }}
            """)
            btn.clicked.connect(lambda checked, t=text: self.show_clicked(t))
            layout.addWidget(btn)
        
        parent_layout.addWidget(frame)
        
        # Add summary
        self.create_summary(parent_layout)
    
    def create_summary(self, parent_layout):
        """Create summary section"""
        summary_title = QLabel("📋 ملخص")
        summary_title.setFont(QFont("Arial", 14, QFont.Bold))
        summary_title.setStyleSheet("color: #333; margin: 20px 0 10px 0;")
        parent_layout.addWidget(summary_title)
        
        summary_text = QLabel("""
        ✅ تم عرض الأزرار بنجاح!
        
        📊 الإحصائيات:
        • الأزرار الأساسية: 4 أزرار
        • الأزرار الملونة: 5 ألوان
        • أحجام مختلفة: 4 أحجام
        • أزرار بأيقونات: 6 أزرار
        
        🎯 المجموع: 19 زر مختلف
        
        🖱️ انقر على أي زر لاختباره!
        """)
        
        summary_text.setFont(QFont("Arial", 10))
        summary_text.setStyleSheet("""
            color: #666;
            background-color: #e9ecef;
            padding: 15px;
            border-radius: 8px;
            border-left: 4px solid #0078d4;
        """)
        summary_text.setWordWrap(True)
        parent_layout.addWidget(summary_text)
    
    def darken_color(self, color):
        """Simple color darkening"""
        color_map = {
            "#0078d4": "#106ebe",
            "#28a745": "#218838",
            "#ffc107": "#e0a800",
            "#dc3545": "#c82333",
            "#17a2b8": "#138496",
            "#6c757d": "#545b62"
        }
        return color_map.get(color, "#2c3e50")
    
    def show_clicked(self, button_name):
        """Show button clicked message"""
        msg = QMessageBox()
        msg.setWindowTitle("تم النقر على الزر")
        msg.setText(f"تم النقر على: {button_name}")
        msg.setIcon(QMessageBox.Information)
        msg.exec_()

def main():
    """Main function"""
    app = QApplication(sys.argv)
    
    # Create and show window
    window = SimpleButtonTest()
    window.show()
    
    print("=== Simple Button Test ===")
    print("🔘 اختبار الأزرار البسيط")
    print("✅ يعمل بدون مشاكل")
    print("🖱️ انقر على الأزرار لاختبارها")
    
    return app.exec_()

if __name__ == "__main__":
    sys.exit(main())
