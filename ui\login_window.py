from PyQt5.QtWidgets import (QWidget, QVBoxLayout, QHBoxLayout, QLabel, 
                            QLineEdit, QPushButton, QFrame, QMessageBox,
                            QCheckBox, QProgressBar, QComboBox)
from PyQt5.QtCore import Qt, pyqtSignal, QTimer, QThread
from PyQt5.QtGui import QFont, QPixmap, QPainter, QLinearGradient, QColor
import hashlib
from datetime import datetime

class LoginWindow(QWidget):
    login_successful = pyqtSignal(dict)
    
    def __init__(self, db_manager, translator, font_manager, locale_manager, cultural_manager):
        super().__init__()
        self.db_manager = db_manager
        self.translator = translator
        self.font_manager = font_manager
        self.locale_manager = locale_manager
        self.cultural_manager = cultural_manager
        
        self.failed_attempts = 0
        self.max_attempts = 3
        
        self.init_ui()
        
    def init_ui(self):
        """Initialize user interface"""
        self.setWindowTitle("تسجيل الدخول - Al-Hassan Stone Factory Login")
        self.setFixedSize(500, 600)
        self.setWindowFlags(Qt.Window | Qt.WindowCloseButtonHint)
        
        # Center window on screen
        self.center_on_screen()
        
        # Main layout
        main_layout = QVBoxLayout()
        main_layout.setContentsMargins(40, 40, 40, 40)
        main_layout.setSpacing(20)
        
        # Logo and title section
        self.create_header_section(main_layout)
        
        # Login form section
        self.create_login_form(main_layout)
        
        # Footer section
        self.create_footer_section(main_layout)
        
        self.setLayout(main_layout)
        
        # Apply stylesheet
        self.apply_stylesheet()
        
        # Set focus to username field
        self.username_input.setFocus()
        
    def center_on_screen(self):
        """Center window on screen"""
        from PyQt5.QtWidgets import QDesktopWidget
        
        screen = QDesktopWidget().screenGeometry()
        size = self.geometry()
        self.move(
            (screen.width() - size.width()) // 2,
            (screen.height() - size.height()) // 2
        )
        
    def create_header_section(self, parent_layout):
        """Create header with logo and title"""
        header_frame = QFrame()
        header_frame.setObjectName("headerFrame")
        header_layout = QVBoxLayout(header_frame)
        header_layout.setAlignment(Qt.AlignCenter)
        header_layout.setSpacing(15)
        
        # Logo placeholder
        logo_label = QLabel()
        logo_label.setFixedSize(80, 80)
        logo_label.setStyleSheet("""
            QLabel {
                background-color: #0078d4;
                border-radius: 40px;
                border: 3px solid #ffffff;
            }
        """)
        logo_label.setAlignment(Qt.AlignCenter)
        header_layout.addWidget(logo_label, 0, Qt.AlignCenter)
        
        # Company name
        company_name = QLabel("مصنع الحسن ستون")
        company_name.setFont(QFont("Arial", 18, QFont.Bold))
        company_name.setStyleSheet("color: #0078d4; margin: 5px;")
        company_name.setAlignment(Qt.AlignCenter)
        header_layout.addWidget(company_name)
        
        # English name
        company_name_en = QLabel("Al-Hassan Stone Factory")
        company_name_en.setFont(QFont("Arial", 14))
        company_name_en.setStyleSheet("color: #666666; margin-bottom: 10px;")
        company_name_en.setAlignment(Qt.AlignCenter)
        header_layout.addWidget(company_name_en)
        
        # System title
        system_title = QLabel("نظام إدارة المصنع")
        system_title.setFont(QFont("Arial", 12))
        system_title.setStyleSheet("color: #888888;")
        system_title.setAlignment(Qt.AlignCenter)
        header_layout.addWidget(system_title)
        
        parent_layout.addWidget(header_frame)
        
    def create_login_form(self, parent_layout):
        """Create login form"""
        form_frame = QFrame()
        form_frame.setObjectName("formFrame")
        form_layout = QVBoxLayout(form_frame)
        form_layout.setSpacing(15)
        
        # Welcome message
        welcome_label = QLabel("مرحباً بك، يرجى تسجيل الدخول")
        welcome_label.setFont(QFont("Arial", 12))
        welcome_label.setStyleSheet("color: #333333; margin-bottom: 20px;")
        welcome_label.setAlignment(Qt.AlignCenter)
        form_layout.addWidget(welcome_label)
        
        welcome_label_en = QLabel("Welcome, please login to continue")
        welcome_label_en.setFont(QFont("Arial", 10))
        welcome_label_en.setStyleSheet("color: #666666; margin-bottom: 20px;")
        welcome_label_en.setAlignment(Qt.AlignCenter)
        form_layout.addWidget(welcome_label_en)
        
        # Username field
        username_layout = QVBoxLayout()
        username_label = QLabel("اسم المستخدم / Username:")
        username_label.setFont(QFont("Arial", 10, QFont.Bold))
        username_layout.addWidget(username_label)
        
        self.username_input = QLineEdit()
        self.username_input.setPlaceholderText("أدخل اسم المستخدم / Enter username")
        self.username_input.setFont(QFont("Arial", 11))
        self.username_input.returnPressed.connect(self.password_input.setFocus)
        username_layout.addWidget(self.username_input)
        
        form_layout.addLayout(username_layout)
        
        # Password field
        password_layout = QVBoxLayout()
        password_label = QLabel("كلمة المرور / Password:")
        password_label.setFont(QFont("Arial", 10, QFont.Bold))
        password_layout.addWidget(password_label)
        
        self.password_input = QLineEdit()
        self.password_input.setEchoMode(QLineEdit.Password)
        self.password_input.setPlaceholderText("أدخل كلمة المرور / Enter password")
        self.password_input.setFont(QFont("Arial", 11))
        self.password_input.returnPressed.connect(self.login)
        password_layout.addWidget(self.password_input)
        
        form_layout.addLayout(password_layout)
        
        # Remember me checkbox
        self.remember_checkbox = QCheckBox("تذكرني / Remember me")
        self.remember_checkbox.setFont(QFont("Arial", 9))
        form_layout.addWidget(self.remember_checkbox)
        
        # Login button
        self.login_button = QPushButton("تسجيل الدخول / Login")
        self.login_button.setFont(QFont("Arial", 12, QFont.Bold))
        self.login_button.setFixedHeight(45)
        self.login_button.clicked.connect(self.login)
        form_layout.addWidget(self.login_button)
        
        # Progress bar (hidden by default)
        self.progress_bar = QProgressBar()
        self.progress_bar.setVisible(False)
        form_layout.addWidget(self.progress_bar)
        
        # Error message label
        self.error_label = QLabel()
        self.error_label.setStyleSheet("color: #dc3545; font-weight: bold;")
        self.error_label.setAlignment(Qt.AlignCenter)
        self.error_label.setWordWrap(True)
        self.error_label.setVisible(False)
        form_layout.addWidget(self.error_label)
        
        # Language selection
        language_layout = QHBoxLayout()
        language_label = QLabel("اللغة / Language:")
        language_label.setFont(QFont("Arial", 9))
        
        self.language_combo = QComboBox()
        self.language_combo.addItems([
            "العربية / Arabic", "English", "Français", "Español", 
            "Deutsch", "Italiano", "Português", "Русский", "中文", "日本語"
        ])
        self.language_combo.setFont(QFont("Arial", 9))
        
        language_layout.addWidget(language_label)
        language_layout.addWidget(self.language_combo)
        language_layout.addStretch()
        
        form_layout.addLayout(language_layout)
        
        parent_layout.addWidget(form_frame)
        
    def create_footer_section(self, parent_layout):
        """Create footer section"""
        footer_frame = QFrame()
        footer_layout = QVBoxLayout(footer_frame)
        footer_layout.setAlignment(Qt.AlignCenter)
        
        # Default login info
        default_info = QLabel("المستخدم الافتراضي / Default User:")
        default_info.setFont(QFont("Arial", 9))
        default_info.setStyleSheet("color: #666666;")
        default_info.setAlignment(Qt.AlignCenter)
        footer_layout.addWidget(default_info)
        
        credentials_info = QLabel("admin / admin123")
        credentials_info.setFont(QFont("Arial", 9, QFont.Bold))
        credentials_info.setStyleSheet("color: #0078d4;")
        credentials_info.setAlignment(Qt.AlignCenter)
        footer_layout.addWidget(credentials_info)
        
        # Version info
        version_label = QLabel("الإصدار 2.0.0 / Version 2.0.0")
        version_label.setFont(QFont("Arial", 8))
        version_label.setStyleSheet("color: #999999; margin-top: 20px;")
        version_label.setAlignment(Qt.AlignCenter)
        footer_layout.addWidget(version_label)
        
        # Copyright
        copyright_label = QLabel("© 2024 Al-Hassan Stone Factory")
        copyright_label.setFont(QFont("Arial", 8))
        copyright_label.setStyleSheet("color: #999999;")
        copyright_label.setAlignment(Qt.AlignCenter)
        footer_layout.addWidget(copyright_label)
        
        parent_layout.addWidget(footer_frame)
        
    def apply_stylesheet(self):
        """Apply login window stylesheet"""
        style = """
        QWidget {
            background-color: #f8f9fa;
            color: #333333;
            font-family: 'Segoe UI', Arial, sans-serif;
        }
        
        #headerFrame {
            background-color: #ffffff;
            border-radius: 10px;
            border: 1px solid #e9ecef;
            padding: 20px;
        }
        
        #formFrame {
            background-color: #ffffff;
            border-radius: 10px;
            border: 1px solid #e9ecef;
            padding: 30px;
        }
        
        QLineEdit {
            background-color: #ffffff;
            border: 2px solid #e9ecef;
            border-radius: 6px;
            padding: 12px;
            font-size: 11pt;
        }
        
        QLineEdit:focus {
            border-color: #0078d4;
            outline: none;
        }
        
        QPushButton {
            background-color: #0078d4;
            color: white;
            border: none;
            border-radius: 6px;
            padding: 12px;
            font-weight: bold;
        }
        
        QPushButton:hover {
            background-color: #106ebe;
        }
        
        QPushButton:pressed {
            background-color: #005a9e;
        }
        
        QPushButton:disabled {
            background-color: #6c757d;
        }
        
        QCheckBox {
            color: #666666;
        }
        
        QCheckBox::indicator {
            width: 16px;
            height: 16px;
            border: 2px solid #e9ecef;
            border-radius: 3px;
            background-color: #ffffff;
        }
        
        QCheckBox::indicator:checked {
            background-color: #0078d4;
            border-color: #0078d4;
        }
        
        QComboBox {
            background-color: #ffffff;
            border: 1px solid #e9ecef;
            border-radius: 4px;
            padding: 6px;
            min-width: 120px;
        }
        
        QComboBox:focus {
            border-color: #0078d4;
        }
        
        QComboBox::drop-down {
            border: none;
            width: 20px;
        }
        
        QComboBox::down-arrow {
            image: none;
            border-left: 5px solid transparent;
            border-right: 5px solid transparent;
            border-top: 5px solid #666666;
        }
        
        QProgressBar {
            border: 1px solid #e9ecef;
            border-radius: 4px;
            text-align: center;
            background-color: #f8f9fa;
        }
        
        QProgressBar::chunk {
            background-color: #0078d4;
            border-radius: 3px;
        }
        """
        
        self.setStyleSheet(style)
        
    def login(self):
        """Handle login attempt"""
        username = self.username_input.text().strip()
        password = self.password_input.text()
        
        # Validate input
        if not username:
            self.show_error("يرجى إدخال اسم المستخدم\nPlease enter username")
            self.username_input.setFocus()
            return
            
        if not password:
            self.show_error("يرجى إدخال كلمة المرور\nPlease enter password")
            self.password_input.setFocus()
            return
            
        # Disable login button and show progress
        self.login_button.setEnabled(False)
        self.progress_bar.setVisible(True)
        self.progress_bar.setRange(0, 0)  # Indeterminate progress
        self.hide_error()
        
        # Create login thread
        self.login_thread = LoginThread(self.db_manager, username, password)
        self.login_thread.login_result.connect(self.handle_login_result)
        self.login_thread.start()
        
    def handle_login_result(self, success, user_data, error_message):
        """Handle login result from thread"""
        # Re-enable login button and hide progress
        self.login_button.setEnabled(True)
        self.progress_bar.setVisible(False)
        
        if success:
            # Reset failed attempts
            self.failed_attempts = 0
            
            # Log successful login
            self.db_manager.log_activity(
                user_data['id'], 
                f"User logged in: {user_data['username']}"
            )
            
            # Emit success signal
            self.login_successful.emit(user_data)
            
        else:
            # Increment failed attempts
            self.failed_attempts += 1
            
            # Show error message
            if "locked" in error_message.lower():
                self.show_error(f"الحساب مقفل مؤقتاً\nAccount temporarily locked\n{error_message}")
            elif self.failed_attempts >= self.max_attempts:
                self.show_error(f"تم تجاوز الحد الأقصى للمحاولات\nMaximum attempts exceeded\n"
                              f"يرجى المحاولة لاحقاً\nPlease try again later")
                
                # Disable login for a period
                self.login_button.setEnabled(False)
                QTimer.singleShot(60000, lambda: self.login_button.setEnabled(True))  # 1 minute
                
            else:
                remaining = self.max_attempts - self.failed_attempts
                self.show_error(f"اسم المستخدم أو كلمة المرور غير صحيحة\n"
                              f"Invalid username or password\n"
                              f"المحاولات المتبقية / Remaining attempts: {remaining}")
                
            # Clear password field
            self.password_input.clear()
            self.password_input.setFocus()
            
    def show_error(self, message):
        """Show error message"""
        self.error_label.setText(message)
        self.error_label.setVisible(True)
        
        # Auto-hide error after 10 seconds
        QTimer.singleShot(10000, self.hide_error)
        
    def hide_error(self):
        """Hide error message"""
        self.error_label.setVisible(False)
        
    def keyPressEvent(self, event):
        """Handle key press events"""
        if event.key() == Qt.Key_Return or event.key() == Qt.Key_Enter:
            if self.username_input.hasFocus():
                self.password_input.setFocus()
            elif self.password_input.hasFocus():
                self.login()
        
        super().keyPressEvent(event)

class LoginThread(QThread):
    """Thread for handling login authentication"""
    login_result = pyqtSignal(bool, dict, str)
    
    def __init__(self, db_manager, username, password):
        super().__init__()
        self.db_manager = db_manager
        self.username = username
        self.password = password
        
    def run(self):
        """Run login authentication"""
        try:
            # Simulate some processing time
            self.msleep(1000)
            
            # Authenticate user
            user_data = self.db_manager.authenticate_user(self.username, self.password)
            
            if user_data:
                self.login_result.emit(True, user_data, "")
            else:
                self.login_result.emit(False, {}, "Invalid credentials")
                
        except Exception as e:
            self.login_result.emit(False, {}, str(e))
