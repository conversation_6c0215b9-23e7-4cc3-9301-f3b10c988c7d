from PyQt5.QtWidgets import (QWidget, QVBoxLayout, QHBoxLayout, QLabel,
                            QPushButton, QListWidget, QListWidgetItem,
                            QGroupBox, QTextEdit, QComboBox, QCheckBox,
                            QFrame, QScrollArea, QMessageBox, QMenu,
                            QSystemTrayIcon, QApplication)
from PyQt5.QtCore import Qt, QTimer, pyqtSignal, QDateTime, QThread, pyqtSlot
from PyQt5.QtGui import QFont, QIcon, QPixmap, QColor
from datetime import datetime, timedelta
import json
import os

class NotificationItem(QFrame):
    """Individual notification item widget"""
    
    dismissed = pyqtSignal(int)
    
    def __init__(self, notification_id, title, message, notification_type, timestamp, is_read=False):
        super().__init__()
        self.notification_id = notification_id
        self.is_read = is_read
        self.init_ui(title, message, notification_type, timestamp)
        
    def init_ui(self, title, message, notification_type, timestamp):
        """Initialize notification item UI"""
        self.setFrameStyle(QFrame.StyledPanel)
        self.setStyleSheet(f"""
            QFrame {{
                background-color: {'#404040' if self.is_read else '#0078d4'};
                border: 1px solid #555555;
                border-radius: 6px;
                margin: 2px;
                padding: 8px;
            }}
            QFrame:hover {{
                background-color: {'#4a4a4a' if self.is_read else '#106ebe'};
            }}
        """)
        
        layout = QVBoxLayout(self)
        layout.setContentsMargins(10, 8, 10, 8)
        
        # Header with title and timestamp
        header_layout = QHBoxLayout()
        
        # Notification type icon
        type_icons = {
            'info': 'ℹ️',
            'warning': '⚠️',
            'error': '❌',
            'success': '✅',
            'system': '⚙️'
        }
        
        icon_label = QLabel(type_icons.get(notification_type, 'ℹ️'))
        icon_label.setFont(QFont("Arial", 14))
        header_layout.addWidget(icon_label)
        
        # Title
        title_label = QLabel(title)
        title_label.setFont(QFont("Arial", 11, QFont.Bold))
        title_label.setStyleSheet("color: white;")
        header_layout.addWidget(title_label)
        
        header_layout.addStretch()
        
        # Timestamp
        time_label = QLabel(timestamp.strftime('%H:%M'))
        time_label.setFont(QFont("Arial", 9))
        time_label.setStyleSheet("color: #cccccc;")
        header_layout.addWidget(time_label)
        
        # Dismiss button
        dismiss_btn = QPushButton('×')
        dismiss_btn.setFixedSize(20, 20)
        dismiss_btn.setStyleSheet("""
            QPushButton {
                background-color: transparent;
                border: none;
                color: white;
                font-weight: bold;
                font-size: 14px;
            }
            QPushButton:hover {
                background-color: #dc3545;
                border-radius: 10px;
            }
        """)
        dismiss_btn.clicked.connect(lambda: self.dismissed.emit(self.notification_id))
        header_layout.addWidget(dismiss_btn)
        
        layout.addLayout(header_layout)
        
        # Message
        message_label = QLabel(message)
        message_label.setFont(QFont("Arial", 10))
        message_label.setStyleSheet("color: #eeeeee;")
        message_label.setWordWrap(True)
        layout.addWidget(message_label)
        
        # Mark as read when clicked
        self.mousePressEvent = self.mark_as_read
        
    def mark_as_read(self, event):
        """Mark notification as read"""
        if not self.is_read:
            self.is_read = True
            self.setStyleSheet("""
                QFrame {
                    background-color: #404040;
                    border: 1px solid #555555;
                    border-radius: 6px;
                    margin: 2px;
                    padding: 8px;
                }
                QFrame:hover {
                    background-color: #4a4a4a;
                }
            """)

class NotificationCenter(QWidget):
    """Comprehensive notification center"""
    
    notification_created = pyqtSignal(str, str, str)  # title, message, type
    
    def __init__(self, db_manager, user_data):
        super().__init__()
        self.db_manager = db_manager
        self.user_data = user_data
        self.notifications = []
        self.init_ui()
        self.setup_system_tray()
        self.setup_monitoring()
        self.load_notifications()
        
    def init_ui(self):
        """Initialize notification center UI"""
        layout = QVBoxLayout(self)
        
        # Header
        header_layout = QHBoxLayout()
        
        header_label = QLabel("مركز الإشعارات / Notification Center")
        header_label.setFont(QFont("Arial", 16, QFont.Bold))
        header_label.setStyleSheet("color: #0078d4; margin-bottom: 10px;")
        header_layout.addWidget(header_label)
        
        header_layout.addStretch()
        
        # Notification controls
        self.mark_all_read_btn = QPushButton("تحديد الكل كمقروء / Mark All Read")
        self.mark_all_read_btn.clicked.connect(self.mark_all_read)
        header_layout.addWidget(self.mark_all_read_btn)
        
        self.clear_all_btn = QPushButton("مسح الكل / Clear All")
        self.clear_all_btn.clicked.connect(self.clear_all_notifications)
        self.clear_all_btn.setStyleSheet("background-color: #dc3545;")
        header_layout.addWidget(self.clear_all_btn)
        
        layout.addLayout(header_layout)
        
        # Filters
        filters_layout = QHBoxLayout()
        
        filters_layout.addWidget(QLabel("تصفية / Filter:"))
        
        self.filter_combo = QComboBox()
        self.filter_combo.addItems([
            "الكل / All", "غير مقروء / Unread", "معلومات / Info", 
            "تحذيرات / Warnings", "أخطاء / Errors", "نجح / Success", "نظام / System"
        ])
        self.filter_combo.currentTextChanged.connect(self.filter_notifications)
        filters_layout.addWidget(self.filter_combo)
        
        filters_layout.addStretch()
        
        # Settings button
        settings_btn = QPushButton("إعدادات الإشعارات / Notification Settings")
        settings_btn.clicked.connect(self.show_notification_settings)
        filters_layout.addWidget(settings_btn)
        
        layout.addLayout(filters_layout)
        
        # Notifications list
        self.notifications_scroll = QScrollArea()
        self.notifications_scroll.setWidgetResizable(True)
        self.notifications_scroll.setStyleSheet("""
            QScrollArea {
                border: 1px solid #555555;
                border-radius: 4px;
                background-color: #353535;
            }
        """)
        
        self.notifications_widget = QWidget()
        self.notifications_layout = QVBoxLayout(self.notifications_widget)
        self.notifications_layout.addStretch()
        
        self.notifications_scroll.setWidget(self.notifications_widget)
        layout.addWidget(self.notifications_scroll)
        
        # Statistics
        stats_layout = QHBoxLayout()
        
        self.total_notifications_label = QLabel("إجمالي الإشعارات / Total: 0")
        stats_layout.addWidget(self.total_notifications_label)
        
        self.unread_notifications_label = QLabel("غير مقروء / Unread: 0")
        stats_layout.addWidget(self.unread_notifications_label)
        
        stats_layout.addStretch()
        
        layout.addLayout(stats_layout)
        
        # Apply styling
        self.apply_notification_style()
        
    def setup_system_tray(self):
        """Setup system tray notifications"""
        if QSystemTrayIcon.isSystemTrayAvailable():
            self.tray_icon = QSystemTrayIcon(self)
            self.tray_icon.setIcon(QIcon('assets/notification.png'))
            
            # Tray menu
            tray_menu = QMenu()
            
            show_action = tray_menu.addAction("عرض الإشعارات / Show Notifications")
            show_action.triggered.connect(self.show)
            
            tray_menu.addSeparator()
            
            exit_action = tray_menu.addAction("خروج / Exit")
            exit_action.triggered.connect(QApplication.quit)
            
            self.tray_icon.setContextMenu(tray_menu)
            self.tray_icon.show()
            
    def setup_monitoring(self):
        """Setup automatic monitoring for notifications"""
        self.monitor_timer = QTimer()
        self.monitor_timer.timeout.connect(self.check_system_events)
        self.monitor_timer.start(60000)  # Check every minute
        
        # Check for overdue tasks, low inventory, etc.
        self.check_system_events()
        
    def check_system_events(self):
        """Check for system events that require notifications"""
        try:
            conn = self.db_manager.get_connection()
            cursor = conn.cursor()
            
            # Check for overdue sales
            cursor.execute("""
                SELECT COUNT(*)
                FROM sales 
                WHERE status = 'pending' AND due_date < DATE('now')
            """)
            overdue_sales = cursor.fetchone()[0]
            
            if overdue_sales > 0:
                self.create_notification(
                    "مبيعات متأخرة / Overdue Sales",
                    f"يوجد {overdue_sales} فاتورة مبيعات متأخرة\nThere are {overdue_sales} overdue sales invoices",
                    "warning"
                )
            
            # Check for low inventory
            cursor.execute("""
                SELECT COUNT(*)
                FROM slabs 
                WHERE status = 'in_stock'
            """)
            inventory_count = cursor.fetchone()[0]
            
            if inventory_count < 10:  # Low inventory threshold
                self.create_notification(
                    "مخزون منخفض / Low Inventory",
                    f"المخزون منخفض: {inventory_count} قطعة متبقية\nLow inventory: {inventory_count} pieces remaining",
                    "warning"
                )
            
            # Check for maintenance due
            cursor.execute("""
                SELECT COUNT(*)
                FROM maintenance_schedules 
                WHERE next_due <= DATE('now') AND is_active = 1
            """)
            maintenance_due = cursor.fetchone()[0]
            
            if maintenance_due > 0:
                self.create_notification(
                    "صيانة مستحقة / Maintenance Due",
                    f"يوجد {maintenance_due} معدات تحتاج صيانة\nThere are {maintenance_due} equipment items requiring maintenance",
                    "info"
                )
            
            # Check for failed backups
            cursor.execute("""
                SELECT COUNT(*)
                FROM backup_logs 
                WHERE status = 'failed' AND DATE(created_at) = DATE('now')
            """)
            failed_backups = cursor.fetchone()[0]
            
            if failed_backups > 0:
                self.create_notification(
                    "فشل النسخ الاحتياطي / Backup Failed",
                    f"فشل في إنشاء {failed_backups} نسخة احتياطية اليوم\nFailed to create {failed_backups} backup(s) today",
                    "error"
                )
            
            conn.close()
            
        except Exception as e:
            print(f"Error checking system events: {e}")
            
    def create_notification(self, title, message, notification_type="info", persistent=False):
        """Create a new notification"""
        try:
            # Check if similar notification already exists (avoid duplicates)
            for notification in self.notifications:
                if notification['title'] == title and notification['message'] == message:
                    return  # Don't create duplicate
                    
            notification_data = {
                'id': len(self.notifications) + 1,
                'title': title,
                'message': message,
                'type': notification_type,
                'timestamp': datetime.now(),
                'is_read': False,
                'persistent': persistent
            }
            
            self.notifications.insert(0, notification_data)  # Add to beginning
            
            # Save to database
            self.save_notification_to_db(notification_data)
            
            # Add to UI
            self.add_notification_to_ui(notification_data)
            
            # Show system tray notification
            if hasattr(self, 'tray_icon') and self.tray_icon.isVisible():
                self.tray_icon.showMessage(
                    title,
                    message,
                    QSystemTrayIcon.Information,
                    5000  # 5 seconds
                )
            
            # Update statistics
            self.update_statistics()
            
            # Emit signal
            self.notification_created.emit(title, message, notification_type)
            
            # Auto-remove non-persistent notifications after 24 hours
            if not persistent:
                QTimer.singleShot(24 * 60 * 60 * 1000, lambda: self.remove_old_notifications())
                
        except Exception as e:
            print(f"Error creating notification: {e}")
            
    def add_notification_to_ui(self, notification_data):
        """Add notification to UI"""
        notification_item = NotificationItem(
            notification_data['id'],
            notification_data['title'],
            notification_data['message'],
            notification_data['type'],
            notification_data['timestamp'],
            notification_data['is_read']
        )
        
        notification_item.dismissed.connect(self.dismiss_notification)
        
        # Insert at the beginning (before stretch)
        self.notifications_layout.insertWidget(0, notification_item)
        
    def save_notification_to_db(self, notification_data):
        """Save notification to database"""
        try:
            conn = self.db_manager.get_connection()
            cursor = conn.cursor()
            
            cursor.execute("""
                INSERT INTO notifications 
                (title, message, type, timestamp, is_read, user_id, persistent)
                VALUES (?, ?, ?, ?, ?, ?, ?)
            """, (
                notification_data['title'],
                notification_data['message'],
                notification_data['type'],
                notification_data['timestamp'].isoformat(),
                notification_data['is_read'],
                self.user_data['id'],
                notification_data['persistent']
            ))
            
            conn.commit()
            conn.close()
            
        except Exception as e:
            print(f"Error saving notification to database: {e}")
            
    def load_notifications(self):
        """Load notifications from database"""
        try:
            # First, create notifications table if it doesn't exist
            conn = self.db_manager.get_connection()
            cursor = conn.cursor()
            
            cursor.execute("""
                CREATE TABLE IF NOT EXISTS notifications (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    title TEXT NOT NULL,
                    message TEXT NOT NULL,
                    type TEXT DEFAULT 'info',
                    timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    is_read BOOLEAN DEFAULT 0,
                    user_id INTEGER,
                    persistent BOOLEAN DEFAULT 0,
                    FOREIGN KEY (user_id) REFERENCES users (id)
                )
            """)
            
            # Load recent notifications (last 30 days)
            cursor.execute("""
                SELECT id, title, message, type, timestamp, is_read, persistent
                FROM notifications
                WHERE user_id = ? AND DATE(timestamp) >= DATE('now', '-30 days')
                ORDER BY timestamp DESC
            """, (self.user_data['id'],))
            
            notifications = cursor.fetchall()
            
            for notification in notifications:
                notification_data = {
                    'id': notification[0],
                    'title': notification[1],
                    'message': notification[2],
                    'type': notification[3],
                    'timestamp': datetime.fromisoformat(notification[4]),
                    'is_read': bool(notification[5]),
                    'persistent': bool(notification[6])
                }
                
                self.notifications.append(notification_data)
                self.add_notification_to_ui(notification_data)
                
            conn.close()
            
            # Update statistics
            self.update_statistics()
            
        except Exception as e:
            print(f"Error loading notifications: {e}")
            
    def dismiss_notification(self, notification_id):
        """Dismiss a notification"""
        try:
            # Remove from UI
            for i in range(self.notifications_layout.count()):
                item = self.notifications_layout.itemAt(i)
                if item and hasattr(item.widget(), 'notification_id'):
                    if item.widget().notification_id == notification_id:
                        item.widget().setParent(None)
                        break
                        
            # Remove from memory
            self.notifications = [n for n in self.notifications if n['id'] != notification_id]
            
            # Remove from database
            conn = self.db_manager.get_connection()
            cursor = conn.cursor()
            cursor.execute("DELETE FROM notifications WHERE id = ?", (notification_id,))
            conn.commit()
            conn.close()
            
            # Update statistics
            self.update_statistics()
            
        except Exception as e:
            print(f"Error dismissing notification: {e}")
            
    def mark_all_read(self):
        """Mark all notifications as read"""
        try:
            # Update UI
            for i in range(self.notifications_layout.count()):
                item = self.notifications_layout.itemAt(i)
                if item and hasattr(item.widget(), 'mark_as_read'):
                    item.widget().mark_as_read(None)
                    
            # Update memory
            for notification in self.notifications:
                notification['is_read'] = True
                
            # Update database
            conn = self.db_manager.get_connection()
            cursor = conn.cursor()
            cursor.execute("""
                UPDATE notifications 
                SET is_read = 1 
                WHERE user_id = ?
            """, (self.user_data['id'],))
            conn.commit()
            conn.close()
            
            # Update statistics
            self.update_statistics()
            
        except Exception as e:
            print(f"Error marking all as read: {e}")
            
    def clear_all_notifications(self):
        """Clear all notifications"""
        reply = QMessageBox.question(
            self, "مسح الإشعارات / Clear Notifications",
            "هل تريد مسح جميع الإشعارات؟\nDo you want to clear all notifications?",
            QMessageBox.Yes | QMessageBox.No,
            QMessageBox.No
        )
        
        if reply == QMessageBox.Yes:
            try:
                # Clear UI
                for i in reversed(range(self.notifications_layout.count())):
                    item = self.notifications_layout.itemAt(i)
                    if item and item.widget():
                        item.widget().setParent(None)
                        
                # Clear memory
                self.notifications.clear()
                
                # Clear database
                conn = self.db_manager.get_connection()
                cursor = conn.cursor()
                cursor.execute("DELETE FROM notifications WHERE user_id = ?", (self.user_data['id'],))
                conn.commit()
                conn.close()
                
                # Update statistics
                self.update_statistics()
                
            except Exception as e:
                QMessageBox.critical(self, "خطأ / Error", f"Error clearing notifications: {e}")
                
    def filter_notifications(self, filter_text):
        """Filter notifications based on selected criteria"""
        filter_map = {
            "الكل / All": None,
            "غير مقروء / Unread": "unread",
            "معلومات / Info": "info",
            "تحذيرات / Warnings": "warning",
            "أخطاء / Errors": "error",
            "نجح / Success": "success",
            "نظام / System": "system"
        }
        
        filter_type = filter_map.get(filter_text)
        
        # Hide/show notifications based on filter
        for i in range(self.notifications_layout.count()):
            item = self.notifications_layout.itemAt(i)
            if item and hasattr(item.widget(), 'notification_id'):
                widget = item.widget()
                notification = next((n for n in self.notifications if n['id'] == widget.notification_id), None)
                
                if notification:
                    should_show = True
                    
                    if filter_type == "unread":
                        should_show = not notification['is_read']
                    elif filter_type in ["info", "warning", "error", "success", "system"]:
                        should_show = notification['type'] == filter_type
                        
                    widget.setVisible(should_show)
                    
    def update_statistics(self):
        """Update notification statistics"""
        total_count = len(self.notifications)
        unread_count = sum(1 for n in self.notifications if not n['is_read'])
        
        self.total_notifications_label.setText(f"إجمالي الإشعارات / Total: {total_count}")
        self.unread_notifications_label.setText(f"غير مقروء / Unread: {unread_count}")
        
    def remove_old_notifications(self):
        """Remove old non-persistent notifications"""
        try:
            cutoff_date = datetime.now() - timedelta(days=7)
            
            # Remove from memory
            self.notifications = [
                n for n in self.notifications 
                if n['persistent'] or n['timestamp'] > cutoff_date
            ]
            
            # Remove from database
            conn = self.db_manager.get_connection()
            cursor = conn.cursor()
            cursor.execute("""
                DELETE FROM notifications 
                WHERE user_id = ? AND persistent = 0 AND timestamp < ?
            """, (self.user_data['id'], cutoff_date.isoformat()))
            conn.commit()
            conn.close()
            
            # Refresh UI
            self.refresh_notifications_ui()
            
        except Exception as e:
            print(f"Error removing old notifications: {e}")
            
    def refresh_notifications_ui(self):
        """Refresh the notifications UI"""
        # Clear current UI
        for i in reversed(range(self.notifications_layout.count())):
            item = self.notifications_layout.itemAt(i)
            if item and item.widget():
                item.widget().setParent(None)
                
        # Re-add notifications
        for notification in self.notifications:
            self.add_notification_to_ui(notification)
            
        # Update statistics
        self.update_statistics()
        
    def show_notification_settings(self):
        """Show notification settings dialog"""
        from PyQt5.QtWidgets import QDialog, QFormLayout, QSpinBox, QDialogButtonBox
        
        dialog = QDialog(self)
        dialog.setWindowTitle("إعدادات الإشعارات / Notification Settings")
        dialog.setFixedSize(400, 300)
        
        layout = QFormLayout(dialog)
        
        # Notification settings
        enable_notifications = QCheckBox("تمكين الإشعارات / Enable Notifications")
        enable_notifications.setChecked(True)
        layout.addRow(enable_notifications)
        
        enable_sound = QCheckBox("تمكين الصوت / Enable Sound")
        enable_sound.setChecked(True)
        layout.addRow(enable_sound)
        
        notification_duration = QSpinBox()
        notification_duration.setRange(1, 30)
        notification_duration.setValue(5)
        notification_duration.setSuffix(" ثانية / seconds")
        layout.addRow("مدة العرض / Display Duration:", notification_duration)
        
        max_notifications = QSpinBox()
        max_notifications.setRange(10, 1000)
        max_notifications.setValue(100)
        layout.addRow("الحد الأقصى للإشعارات / Max Notifications:", max_notifications)
        
        # Buttons
        buttons = QDialogButtonBox(QDialogButtonBox.Ok | QDialogButtonBox.Cancel)
        buttons.accepted.connect(dialog.accept)
        buttons.rejected.connect(dialog.reject)
        layout.addRow(buttons)
        
        if dialog.exec_() == QDialog.Accepted:
            # Save settings (would integrate with config system)
            pass
            
    def apply_notification_style(self):
        """Apply notification center styling"""
        self.setStyleSheet("""
            QWidget {
                background-color: #2b2b2b;
                color: #ffffff;
                font-family: 'Segoe UI', Arial, sans-serif;
            }
            
            QPushButton {
                background-color: #0078d4;
                color: white;
                border: none;
                padding: 8px 16px;
                border-radius: 4px;
                font-weight: bold;
            }
            
            QPushButton:hover {
                background-color: #106ebe;
            }
            
            QPushButton:pressed {
                background-color: #005a9e;
            }
            
            QComboBox {
                background-color: #404040;
                border: 1px solid #555555;
                border-radius: 4px;
                padding: 6px;
                color: white;
            }
            
            QComboBox:focus {
                border-color: #0078d4;
            }
            
            QComboBox::drop-down {
                border: none;
                background-color: #555555;
                border-top-right-radius: 4px;
                border-bottom-right-radius: 4px;
            }
            
            QComboBox::down-arrow {
                image: none;
                border: 2px solid white;
                border-top: none;
                border-right: none;
                width: 6px;
                height: 6px;
                margin: 4px;
            }
            
            QScrollArea {
                border: 1px solid #555555;
                border-radius: 4px;
                background-color: #353535;
            }
            
            QScrollBar:vertical {
                background-color: #404040;
                width: 12px;
                border-radius: 6px;
            }
            
            QScrollBar::handle:vertical {
                background-color: #0078d4;
                border-radius: 6px;
                min-height: 20px;
            }
            
            QScrollBar::handle:vertical:hover {
                background-color: #106ebe;
            }
        """)

# Add notification center to main dashboard
def add_notification_center_to_dashboard(main_dashboard):
    """Add notification center to main dashboard"""
    notification_center = NotificationCenter(main_dashboard.db_manager, main_dashboard.user_data)
    
    # Add to modules
    main_dashboard.modules['notifications'] = notification_center
    main_dashboard.content_stack.addWidget(notification_center)
    
    # Add to navigation
    if hasattr(main_dashboard, 'nav_buttons'):
        btn = QPushButton("الإشعارات / Notifications")
        btn.setStyleSheet("""
            QPushButton {
                background-color: #17a2b8;
                border: none;
                padding: 12px;
                text-align: left;
                border-radius: 6px;
                font-weight: bold;
                color: white;
            }
            QPushButton:hover {
                background-color: #138496;
            }
        """)
        btn.clicked.connect(lambda: main_dashboard.switch_to_module('notifications'))
        main_dashboard.nav_buttons['notifications'] = btn
        
        # Add to sidebar (would need to modify main dashboard layout)
        
    return notification_center
