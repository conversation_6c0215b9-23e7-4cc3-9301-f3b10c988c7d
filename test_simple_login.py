#!/usr/bin/env python3
"""
Simple login window test to debug visibility issues
"""

import sys
from PyQt5.QtWidgets import (QApplication, QWidget, QVBoxLayout, QHBoxLayout, 
                            QLineEdit, QPushButton, QLabel, QFrame)
from PyQt5.QtCore import Qt
from PyQt5.QtGui import QFont

class SimpleLoginWindow(QWidget):
    def __init__(self):
        super().__init__()
        self.init_ui()
        
    def init_ui(self):
        self.setWindowTitle("Simple Login Test")
        self.setFixedSize(400, 300)
        self.setStyleSheet("background-color: #f0f0f0;")
        
        # Main layout
        main_layout = QVBoxLayout()
        main_layout.setSpacing(20)
        main_layout.setContentsMargins(50, 50, 50, 50)
        
        # Title
        title = QLabel("اختبار تسجيل الدخول")
        title.setFont(QFont("Arial", 16, QFont.Bold))
        title.setAlignment(Qt.AlignCenter)
        title.setStyleSheet("color: #333; margin-bottom: 20px;")
        main_layout.addWidget(title)
        
        # Username
        username_label = QLabel("اسم المستخدم:")
        username_label.setFont(QFont("Arial", 10, QFont.Bold))
        username_label.setStyleSheet("color: #333;")
        main_layout.addWidget(username_label)
        
        self.username_input = QLineEdit()
        self.username_input.setPlaceholderText("أدخل اسم المستخدم")
        self.username_input.setFont(QFont("Arial", 11))
        self.username_input.setFixedHeight(40)
        self.username_input.setStyleSheet("""
            QLineEdit {
                background-color: white;
                border: 2px solid #ccc;
                border-radius: 5px;
                padding: 8px;
                color: #333;
            }
            QLineEdit:focus {
                border-color: #0078d4;
            }
        """)
        main_layout.addWidget(self.username_input)
        
        # Password
        password_label = QLabel("كلمة المرور:")
        password_label.setFont(QFont("Arial", 10, QFont.Bold))
        password_label.setStyleSheet("color: #333;")
        main_layout.addWidget(password_label)
        
        self.password_input = QLineEdit()
        self.password_input.setEchoMode(QLineEdit.Password)
        self.password_input.setPlaceholderText("أدخل كلمة المرور")
        self.password_input.setFont(QFont("Arial", 11))
        self.password_input.setFixedHeight(40)
        self.password_input.setStyleSheet("""
            QLineEdit {
                background-color: white;
                border: 2px solid #ccc;
                border-radius: 5px;
                padding: 8px;
                color: #333;
            }
            QLineEdit:focus {
                border-color: #0078d4;
            }
        """)
        main_layout.addWidget(self.password_input)
        
        # Login button
        self.login_button = QPushButton("تسجيل الدخول")
        self.login_button.setFont(QFont("Arial", 12, QFont.Bold))
        self.login_button.setFixedHeight(45)
        self.login_button.setStyleSheet("""
            QPushButton {
                background-color: #0078d4;
                color: white;
                border: none;
                border-radius: 5px;
                padding: 10px;
            }
            QPushButton:hover {
                background-color: #106ebe;
            }
            QPushButton:pressed {
                background-color: #005a9e;
            }
        """)
        self.login_button.clicked.connect(self.test_login)
        main_layout.addWidget(self.login_button)
        
        # Status label
        self.status_label = QLabel("")
        self.status_label.setAlignment(Qt.AlignCenter)
        self.status_label.setStyleSheet("color: #666; margin-top: 10px;")
        main_layout.addWidget(self.status_label)
        
        self.setLayout(main_layout)
        
        # Auto-fill for testing
        self.username_input.setText("admin")
        self.password_input.setText("admin123")
        self.status_label.setText("تم ملء البيانات تلقائياً للاختبار")
        
    def test_login(self):
        username = self.username_input.text()
        password = self.password_input.text()
        
        if username and password:
            self.status_label.setText(f"تم النقر! المستخدم: {username}")
            self.status_label.setStyleSheet("color: green; font-weight: bold;")
        else:
            self.status_label.setText("يرجى ملء جميع الحقول")
            self.status_label.setStyleSheet("color: red; font-weight: bold;")

def main():
    app = QApplication(sys.argv)
    
    window = SimpleLoginWindow()
    window.show()
    
    print("=== Simple Login Test ===")
    print("If you can see the input fields and they work, the issue is in the main login window.")
    print("If you can't see them, the issue is with PyQt5 or system configuration.")
    
    return app.exec_()

if __name__ == "__main__":
    sys.exit(main())
