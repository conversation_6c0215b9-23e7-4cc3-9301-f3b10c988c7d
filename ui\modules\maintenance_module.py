from PyQt5.QtWidgets import (QWidget, QVBoxLayout, QHBoxLayout, QTableWidget, 
                            QTableWidgetItem, QPushButton, QLineEdit, QLabel,
                            QDialog, QFormLayout, QMessageBox, QHeaderView,
                            QDateEdit, QDoubleSpinBox, QComboBox, QTextEdit,
                            QSpinBox, QTabWidget, QGroupBox, QGridLayout,
                            QCalendarWidget, QListWidget, QListWidgetItem)
from PyQt5.QtCore import Qt, QDate, QTimer, pyqtSignal
from PyQt5.QtGui import QFont, QColor
from datetime import datetime, timedelta
import json

class MaintenanceModule(QWidget):
    def __init__(self, db_manager, user_data):
        super().__init__()
        self.db_manager = db_manager
        self.user_data = user_data
        self.init_ui()
        self.load_equipment()
        self.setup_timer()
        
    def init_ui(self):
        layout = QVBoxLayout()
        
        # Header
        header_layout = QHBoxLayout()
        
        title = QLabel("إدارة الصيانة / Maintenance Management")
        title.setStyleSheet("font-size: 16pt; font-weight: bold; color: #0078d4;")
        header_layout.addWidget(title)
        
        header_layout.addStretch()
        
        # New equipment button
        new_equipment_btn = QPushButton("معدة جديدة / New Equipment")
        new_equipment_btn.setStyleSheet("background-color: #28a745;")
        new_equipment_btn.clicked.connect(self.add_equipment)
        header_layout.addWidget(new_equipment_btn)
        
        # Schedule maintenance button
        schedule_btn = QPushButton("جدولة صيانة / Schedule Maintenance")
        schedule_btn.setStyleSheet("background-color: #17a2b8;")
        schedule_btn.clicked.connect(self.schedule_maintenance)
        header_layout.addWidget(schedule_btn)
        
        layout.addLayout(header_layout)
        
        # Create tabs
        self.tab_widget = QTabWidget()
        
        # Equipment tab
        equipment_tab = self.create_equipment_tab()
        self.tab_widget.addTab(equipment_tab, "المعدات / Equipment")
        
        # Maintenance schedules tab
        schedules_tab = self.create_schedules_tab()
        self.tab_widget.addTab(schedules_tab, "جداول الصيانة / Maintenance Schedules")
        
        # Maintenance records tab
        records_tab = self.create_records_tab()
        self.tab_widget.addTab(records_tab, "سجلات الصيانة / Maintenance Records")
        
        # Calendar tab
        calendar_tab = self.create_calendar_tab()
        self.tab_widget.addTab(calendar_tab, "التقويم / Calendar")
        
        layout.addWidget(self.tab_widget)
        
        self.setLayout(layout)
        
    def create_equipment_tab(self):
        """Create equipment management tab"""
        widget = QWidget()
        layout = QVBoxLayout(widget)
        
        # Equipment status summary
        summary_layout = QGridLayout()
        
        # Operational equipment card
        operational_card = self.create_status_card("معدات تعمل / Operational", "0", "#28a745")
        summary_layout.addWidget(operational_card, 0, 0)
        
        # Under maintenance card
        maintenance_card = self.create_status_card("تحت الصيانة / Under Maintenance", "0", "#ffc107")
        summary_layout.addWidget(maintenance_card, 0, 1)
        
        # Out of service card
        outofservice_card = self.create_status_card("خارج الخدمة / Out of Service", "0", "#dc3545")
        summary_layout.addWidget(outofservice_card, 0, 2)
        
        layout.addLayout(summary_layout)
        
        # Equipment table
        self.equipment_table = QTableWidget()
        self.equipment_table.setColumnCount(8)
        self.equipment_table.setHorizontalHeaderLabels([
            "كود المعدة / Equipment Code", "الاسم / Name", "النوع / Type",
            "الموقع / Location", "الحالة / Status", "آخر صيانة / Last Maintenance",
            "الصيانة القادمة / Next Maintenance", "الإجراءات / Actions"
        ])
        
        # Set column widths
        header = self.equipment_table.horizontalHeader()
        header.setSectionResizeMode(QHeaderView.Stretch)
        
        layout.addWidget(self.equipment_table)
        
        return widget
        
    def create_schedules_tab(self):
        """Create maintenance schedules tab"""
        widget = QWidget()
        layout = QVBoxLayout(widget)
        
        # Schedules table
        self.schedules_table = QTableWidget()
        self.schedules_table.setColumnCount(7)
        self.schedules_table.setHorizontalHeaderLabels([
            "المعدة / Equipment", "نوع الصيانة / Maintenance Type", 
            "التكرار / Frequency", "آخر تنفيذ / Last Performed",
            "الموعد القادم / Next Due", "الحالة / Status", "الإجراءات / Actions"
        ])
        
        # Set column widths
        header = self.schedules_table.horizontalHeader()
        header.setSectionResizeMode(QHeaderView.Stretch)
        
        layout.addWidget(self.schedules_table)
        
        return widget
        
    def create_records_tab(self):
        """Create maintenance records tab"""
        widget = QWidget()
        layout = QVBoxLayout(widget)
        
        # Filter controls
        filter_layout = QHBoxLayout()
        
        equipment_label = QLabel("المعدة / Equipment:")
        self.equipment_filter = QComboBox()
        self.load_equipment_filter()
        self.equipment_filter.currentTextChanged.connect(self.filter_records)
        
        type_label = QLabel("النوع / Type:")
        self.maintenance_type_filter = QComboBox()
        self.maintenance_type_filter.addItems([
            "الكل / All", "صيانة وقائية / Preventive", "صيانة تصحيحية / Corrective",
            "صيانة طارئة / Emergency", "معايرة / Calibration"
        ])
        self.maintenance_type_filter.currentTextChanged.connect(self.filter_records)
        
        filter_layout.addWidget(equipment_label)
        filter_layout.addWidget(self.equipment_filter)
        filter_layout.addWidget(type_label)
        filter_layout.addWidget(self.maintenance_type_filter)
        filter_layout.addStretch()
        
        layout.addLayout(filter_layout)
        
        # Records table
        self.records_table = QTableWidget()
        self.records_table.setColumnCount(8)
        self.records_table.setHorizontalHeaderLabels([
            "رقم الصيانة / Maintenance No.", "المعدة / Equipment", "النوع / Type",
            "التاريخ / Date", "المدة / Duration", "التكلفة / Cost",
            "الحالة / Status", "الإجراءات / Actions"
        ])
        
        # Set column widths
        header = self.records_table.horizontalHeader()
        header.setSectionResizeMode(QHeaderView.Stretch)
        
        layout.addWidget(self.records_table)
        
        # New maintenance record button
        new_record_btn = QPushButton("سجل صيانة جديد / New Maintenance Record")
        new_record_btn.clicked.connect(self.create_maintenance_record)
        layout.addWidget(new_record_btn)
        
        return widget
        
    def create_calendar_tab(self):
        """Create maintenance calendar tab"""
        widget = QWidget()
        layout = QHBoxLayout(widget)
        
        # Calendar widget
        self.calendar = QCalendarWidget()
        self.calendar.clicked.connect(self.on_date_selected)
        layout.addWidget(self.calendar)
        
        # Maintenance list for selected date
        right_panel = QWidget()
        right_layout = QVBoxLayout(right_panel)
        
        self.selected_date_label = QLabel("اختر تاريخاً / Select a date")
        self.selected_date_label.setFont(QFont("Arial", 12, QFont.Bold))
        right_layout.addWidget(self.selected_date_label)
        
        self.maintenance_list = QListWidget()
        right_layout.addWidget(self.maintenance_list)
        
        layout.addWidget(right_panel)
        
        return widget
        
    def create_status_card(self, title, value, color):
        """Create equipment status card"""
        card = QGroupBox()
        card.setStyleSheet(f"""
            QGroupBox {{
                background-color: {color};
                border-radius: 8px;
                font-weight: bold;
                color: white;
                padding: 15px;
            }}
        """)
        
        layout = QVBoxLayout(card)
        
        title_label = QLabel(title)
        title_label.setFont(QFont("Arial", 10))
        title_label.setAlignment(Qt.AlignCenter)
        title_label.setWordWrap(True)
        
        value_label = QLabel(value)
        value_label.setFont(QFont("Arial", 18, QFont.Bold))
        value_label.setAlignment(Qt.AlignCenter)
        
        layout.addWidget(title_label)
        layout.addWidget(value_label)
        
        return card
        
    def setup_timer(self):
        """Setup timer for checking maintenance schedules"""
        self.timer = QTimer()
        self.timer.timeout.connect(self.check_maintenance_due)
        self.timer.start(60000)  # Check every minute
        
    def load_equipment(self):
        """Load equipment from database"""
        conn = self.db_manager.get_connection()
        cursor = conn.cursor()
        
        cursor.execute("""
            SELECT id, equipment_code, name, type, location, status,
                   last_maintenance, next_maintenance
            FROM equipment
            ORDER BY equipment_code
        """)
        
        equipment_list = cursor.fetchall()
        
        self.equipment_table.setRowCount(len(equipment_list))
        
        operational_count = 0
        maintenance_count = 0
        outofservice_count = 0
        
        for row, equipment in enumerate(equipment_list):
            self.equipment_table.setItem(row, 0, QTableWidgetItem(equipment['equipment_code']))
            self.equipment_table.setItem(row, 1, QTableWidgetItem(equipment['name']))
            self.equipment_table.setItem(row, 2, QTableWidgetItem(equipment['type']))
            self.equipment_table.setItem(row, 3, QTableWidgetItem(equipment['location'] or '-'))
            
            # Status with color coding
            status_item = QTableWidgetItem(equipment['status'])
            if equipment['status'] == 'operational':
                status_item.setBackground(QColor('#28a745'))
                operational_count += 1
            elif equipment['status'] == 'under_maintenance':
                status_item.setBackground(QColor('#ffc107'))
                maintenance_count += 1
            else:
                status_item.setBackground(QColor('#dc3545'))
                outofservice_count += 1
                
            self.equipment_table.setItem(row, 4, status_item)
            self.equipment_table.setItem(row, 5, QTableWidgetItem(equipment['last_maintenance'] or '-'))
            self.equipment_table.setItem(row, 6, QTableWidgetItem(equipment['next_maintenance'] or '-'))
            
            # Action buttons
            actions_widget = QWidget()
            actions_layout = QHBoxLayout(actions_widget)
            actions_layout.setContentsMargins(5, 5, 5, 5)
            
            view_btn = QPushButton("عرض / View")
            view_btn.setFixedSize(60, 25)
            view_btn.clicked.connect(lambda checked, eid=equipment['id']: self.view_equipment(eid))
            
            edit_btn = QPushButton("تعديل / Edit")
            edit_btn.setFixedSize(60, 25)
            edit_btn.clicked.connect(lambda checked, eid=equipment['id']: self.edit_equipment(eid))
            
            maintain_btn = QPushButton("صيانة / Maintain")
            maintain_btn.setFixedSize(70, 25)
            maintain_btn.setStyleSheet("background-color: #ffc107;")
            maintain_btn.clicked.connect(lambda checked, eid=equipment['id']: self.perform_maintenance(eid))
            
            actions_layout.addWidget(view_btn)
            actions_layout.addWidget(edit_btn)
            actions_layout.addWidget(maintain_btn)
            
            self.equipment_table.setCellWidget(row, 7, actions_widget)
            
        # Update status cards
        self.update_status_cards(operational_count, maintenance_count, outofservice_count)
        
        conn.close()
        
    def update_status_cards(self, operational, maintenance, outofservice):
        """Update equipment status cards"""
        # This would update the status cards with actual counts
        # Implementation depends on how cards are stored/accessed
        pass
        
    def load_equipment_filter(self):
        """Load equipment for filter dropdown"""
        conn = self.db_manager.get_connection()
        cursor = conn.cursor()
        
        cursor.execute("SELECT id, equipment_code, name FROM equipment ORDER BY equipment_code")
        equipment_list = cursor.fetchall()
        
        self.equipment_filter.addItem("الكل / All", None)
        for equipment in equipment_list:
            display_text = f"{equipment['equipment_code']} - {equipment['name']}"
            self.equipment_filter.addItem(display_text, equipment['id'])
            
        conn.close()
        
    def add_equipment(self):
        """Add new equipment"""
        dialog = EquipmentDialog(self.db_manager)
        if dialog.exec_() == QDialog.Accepted:
            self.load_equipment()
            
    def view_equipment(self, equipment_id):
        """View equipment details"""
        dialog = EquipmentDialog(self.db_manager, equipment_id, view_only=True)
        dialog.exec_()
        
    def edit_equipment(self, equipment_id):
        """Edit equipment"""
        dialog = EquipmentDialog(self.db_manager, equipment_id)
        if dialog.exec_() == QDialog.Accepted:
            self.load_equipment()
            
    def perform_maintenance(self, equipment_id):
        """Perform maintenance on equipment"""
        dialog = MaintenanceRecordDialog(self.db_manager, self.user_data, equipment_id=equipment_id)
        if dialog.exec_() == QDialog.Accepted:
            self.load_equipment()
            
    def schedule_maintenance(self):
        """Schedule maintenance"""
        dialog = MaintenanceScheduleDialog(self.db_manager)
        if dialog.exec_() == QDialog.Accepted:
            self.load_schedules()
            
    def create_maintenance_record(self):
        """Create new maintenance record"""
        dialog = MaintenanceRecordDialog(self.db_manager, self.user_data)
        if dialog.exec_() == QDialog.Accepted:
            self.load_records()
            
    def load_schedules(self):
        """Load maintenance schedules"""
        conn = self.db_manager.get_connection()
        cursor = conn.cursor()
        
        cursor.execute("""
            SELECT ms.id, e.equipment_code, e.name as equipment_name,
                   ms.maintenance_type, ms.frequency_days, ms.last_performed,
                   ms.next_due, ms.is_active
            FROM maintenance_schedules ms
            JOIN equipment e ON ms.equipment_id = e.id
            WHERE ms.is_active = 1
            ORDER BY ms.next_due
        """)
        
        schedules = cursor.fetchall()
        
        self.schedules_table.setRowCount(len(schedules))
        
        for row, schedule in enumerate(schedules):
            equipment_display = f"{schedule['equipment_code']} - {schedule['equipment_name']}"
            self.schedules_table.setItem(row, 0, QTableWidgetItem(equipment_display))
            self.schedules_table.setItem(row, 1, QTableWidgetItem(schedule['maintenance_type']))
            self.schedules_table.setItem(row, 2, QTableWidgetItem(f"{schedule['frequency_days']} أيام / days"))
            self.schedules_table.setItem(row, 3, QTableWidgetItem(schedule['last_performed'] or '-'))
            self.schedules_table.setItem(row, 4, QTableWidgetItem(schedule['next_due']))
            
            # Status based on due date
            next_due = datetime.strptime(schedule['next_due'], '%Y-%m-%d').date()
            today = datetime.now().date()
            
            if next_due < today:
                status = "متأخر / Overdue"
                status_color = QColor('#dc3545')
            elif next_due <= today + timedelta(days=7):
                status = "مستحق قريباً / Due Soon"
                status_color = QColor('#ffc107')
            else:
                status = "مجدول / Scheduled"
                status_color = QColor('#28a745')
                
            status_item = QTableWidgetItem(status)
            status_item.setBackground(status_color)
            self.schedules_table.setItem(row, 5, status_item)
            
            # Action buttons
            actions_widget = QWidget()
            actions_layout = QHBoxLayout(actions_widget)
            actions_layout.setContentsMargins(5, 5, 5, 5)
            
            edit_btn = QPushButton("تعديل / Edit")
            edit_btn.setFixedSize(70, 25)
            edit_btn.clicked.connect(lambda checked, sid=schedule['id']: self.edit_schedule(sid))
            
            perform_btn = QPushButton("تنفيذ / Perform")
            perform_btn.setFixedSize(70, 25)
            perform_btn.setStyleSheet("background-color: #28a745;")
            perform_btn.clicked.connect(lambda checked, sid=schedule['id']: self.perform_scheduled_maintenance(sid))
            
            actions_layout.addWidget(edit_btn)
            actions_layout.addWidget(perform_btn)
            
            self.schedules_table.setCellWidget(row, 6, actions_widget)
            
        conn.close()
        
    def load_records(self):
        """Load maintenance records"""
        conn = self.db_manager.get_connection()
        cursor = conn.cursor()
        
        cursor.execute("""
            SELECT mr.id, mr.maintenance_number, e.equipment_code, e.name as equipment_name,
                   mr.maintenance_type, mr.actual_date, mr.duration_hours, mr.cost, mr.status
            FROM maintenance_records mr
            JOIN equipment e ON mr.equipment_id = e.id
            ORDER BY mr.actual_date DESC
        """)
        
        records = cursor.fetchall()
        
        self.records_table.setRowCount(len(records))
        
        for row, record in enumerate(records):
            equipment_display = f"{record['equipment_code']} - {record['equipment_name']}"
            
            self.records_table.setItem(row, 0, QTableWidgetItem(record['maintenance_number']))
            self.records_table.setItem(row, 1, QTableWidgetItem(equipment_display))
            self.records_table.setItem(row, 2, QTableWidgetItem(record['maintenance_type']))
            self.records_table.setItem(row, 3, QTableWidgetItem(record['actual_date']))
            self.records_table.setItem(row, 4, QTableWidgetItem(f"{record['duration_hours'] or 0:.1f} ساعة / hours"))
            self.records_table.setItem(row, 5, QTableWidgetItem(f"{record['cost'] or 0:.2f}"))
            
            # Status with color coding
            status_item = QTableWidgetItem(record['status'])
            if record['status'] == 'completed':
                status_item.setBackground(QColor('#28a745'))
            elif record['status'] == 'in_progress':
                status_item.setBackground(QColor('#ffc107'))
            else:
                status_item.setBackground(QColor('#dc3545'))
                
            self.records_table.setItem(row, 6, status_item)
            
            # Action buttons
            actions_widget = QWidget()
            actions_layout = QHBoxLayout(actions_widget)
            actions_layout.setContentsMargins(5, 5, 5, 5)
            
            view_btn = QPushButton("عرض / View")
            view_btn.setFixedSize(60, 25)
            view_btn.clicked.connect(lambda checked, rid=record['id']: self.view_record(rid))
            
            edit_btn = QPushButton("تعديل / Edit")
            edit_btn.setFixedSize(60, 25)
            edit_btn.clicked.connect(lambda checked, rid=record['id']: self.edit_record(rid))
            
            actions_layout.addWidget(view_btn)
            actions_layout.addWidget(edit_btn)
            
            self.records_table.setCellWidget(row, 7, actions_widget)
            
        conn.close()
        
    def filter_records(self):
        """Filter maintenance records"""
        equipment_id = self.equipment_filter.currentData()
        maintenance_type = self.maintenance_type_filter.currentText()
        
        for row in range(self.records_table.rowCount()):
            show_row = True
            
            # Filter by equipment
            if equipment_id:
                # This would need more complex logic to match equipment
                pass
                
            # Filter by maintenance type
            if not ("الكل" in maintenance_type or "All" in maintenance_type):
                type_item = self.records_table.item(row, 2)
                if type_item:
                    if not any(keyword in maintenance_type.lower() 
                             for keyword in type_item.text().lower().split()):
                        show_row = False
                        
            self.records_table.setRowHidden(row, not show_row)
            
    def on_date_selected(self, date):
        """Handle calendar date selection"""
        selected_date = date.toString("yyyy-MM-dd")
        self.selected_date_label.setText(f"الصيانة المجدولة في / Scheduled Maintenance on: {selected_date}")
        
        # Load maintenance scheduled for selected date
        conn = self.db_manager.get_connection()
        cursor = conn.cursor()
        
        cursor.execute("""
            SELECT e.equipment_code, e.name, ms.maintenance_type
            FROM maintenance_schedules ms
            JOIN equipment e ON ms.equipment_id = e.id
            WHERE ms.next_due = ? AND ms.is_active = 1
        """, (selected_date,))
        
        scheduled_maintenance = cursor.fetchall()
        
        self.maintenance_list.clear()
        
        if scheduled_maintenance:
            for maintenance in scheduled_maintenance:
                item_text = f"{maintenance['equipment_code']} - {maintenance['name']}: {maintenance['maintenance_type']}"
                item = QListWidgetItem(item_text)
                self.maintenance_list.addItem(item)
        else:
            item = QListWidgetItem("لا توجد صيانة مجدولة / No scheduled maintenance")
            self.maintenance_list.addItem(item)
            
        conn.close()
        
    def check_maintenance_due(self):
        """Check for maintenance due and show notifications"""
        conn = self.db_manager.get_connection()
        cursor = conn.cursor()
        
        # Check for overdue maintenance
        cursor.execute("""
            SELECT COUNT(*) as overdue_count
            FROM maintenance_schedules ms
            WHERE ms.next_due < date('now') AND ms.is_active = 1
        """)
        
        result = cursor.fetchone()
        overdue_count = result['overdue_count'] if result else 0
        
        if overdue_count > 0:
            # Show notification (this could be implemented as a system tray notification)
            pass
            
        conn.close()
        
    def edit_schedule(self, schedule_id):
        """Edit maintenance schedule"""
        dialog = MaintenanceScheduleDialog(self.db_manager, schedule_id)
        if dialog.exec_() == QDialog.Accepted:
            self.load_schedules()
            
    def perform_scheduled_maintenance(self, schedule_id):
        """Perform scheduled maintenance"""
        # Get schedule details and create maintenance record
        conn = self.db_manager.get_connection()
        cursor = conn.cursor()
        
        cursor.execute("""
            SELECT equipment_id, maintenance_type
            FROM maintenance_schedules
            WHERE id = ?
        """, (schedule_id,))
        
        schedule = cursor.fetchone()
        
        if schedule:
            dialog = MaintenanceRecordDialog(
                self.db_manager, self.user_data, 
                equipment_id=schedule['equipment_id'],
                maintenance_type=schedule['maintenance_type'],
                schedule_id=schedule_id
            )
            if dialog.exec_() == QDialog.Accepted:
                self.load_schedules()
                self.load_records()
                
        conn.close()
        
    def view_record(self, record_id):
        """View maintenance record"""
        dialog = MaintenanceRecordDialog(self.db_manager, self.user_data, record_id=record_id, view_only=True)
        dialog.exec_()
        
    def edit_record(self, record_id):
        """Edit maintenance record"""
        dialog = MaintenanceRecordDialog(self.db_manager, self.user_data, record_id=record_id)
        if dialog.exec_() == QDialog.Accepted:
            self.load_records()

class EquipmentDialog(QDialog):
    def __init__(self, db_manager, equipment_id=None, view_only=False):
        super().__init__()
        self.db_manager = db_manager
        self.equipment_id = equipment_id
        self.view_only = view_only
        self.init_ui()
        
        if equipment_id:
            self.load_equipment_data()
            
    def init_ui(self):
        self.setWindowTitle("معدة / Equipment")
        self.setFixedSize(500, 600)
        
        layout = QFormLayout()
        
        # Equipment code
        self.equipment_code_input = QLineEdit()
        self.equipment_code_input.setPlaceholderText("كود المعدة / Equipment code")
        layout.addRow("كود المعدة / Equipment Code:", self.equipment_code_input)
        
        # Name
        self.name_input = QLineEdit()
        self.name_input.setPlaceholderText("اسم المعدة / Equipment name")
        layout.addRow("الاسم / Name:", self.name_input)
        
        # Type
        self.type_combo = QComboBox()
        self.type_combo.addItems([
            "آلة قطع / Cutting Machine", "آلة تلميع / Polishing Machine",
            "رافعة / Crane", "مولد كهرباء / Generator", "ضاغط هواء / Air Compressor",
            "أخرى / Other"
        ])
        layout.addRow("النوع / Type:", self.type_combo)
        
        # Manufacturer
        self.manufacturer_input = QLineEdit()
        self.manufacturer_input.setPlaceholderText("الشركة المصنعة / Manufacturer")
        layout.addRow("الشركة المصنعة / Manufacturer:", self.manufacturer_input)
        
        # Model
        self.model_input = QLineEdit()
        self.model_input.setPlaceholderText("الموديل / Model")
        layout.addRow("الموديل / Model:", self.model_input)
        
        # Serial number
        self.serial_number_input = QLineEdit()
        self.serial_number_input.setPlaceholderText("الرقم التسلسلي / Serial number")
        layout.addRow("الرقم التسلسلي / Serial Number:", self.serial_number_input)
        
        # Purchase date
        self.purchase_date_input = QDateEdit()
        self.purchase_date_input.setCalendarPopup(True)
        layout.addRow("تاريخ الشراء / Purchase Date:", self.purchase_date_input)
        
        # Purchase cost
        self.purchase_cost_input = QDoubleSpinBox()
        self.purchase_cost_input.setRange(0, 1000000)
        self.purchase_cost_input.setDecimals(2)
        layout.addRow("تكلفة الشراء / Purchase Cost:", self.purchase_cost_input)
        
        # Location
        self.location_input = QLineEdit()
        self.location_input.setPlaceholderText("موقع المعدة / Equipment location")
        layout.addRow("الموقع / Location:", self.location_input)
        
        # Status
        self.status_combo = QComboBox()
        self.status_combo.addItems([
            "تعمل / Operational", "تحت الصيانة / Under Maintenance", 
            "خارج الخدمة / Out of Service"
        ])
        layout.addRow("الحالة / Status:", self.status_combo)
        
        # Last maintenance
        self.last_maintenance_input = QDateEdit()
        self.last_maintenance_input.setCalendarPopup(True)
        self.last_maintenance_input.setSpecialValueText("لم يتم / Not performed")
        layout.addRow("آخر صيانة / Last Maintenance:", self.last_maintenance_input)
        
        # Next maintenance
        self.next_maintenance_input = QDateEdit()
        self.next_maintenance_input.setCalendarPopup(True)
        self.next_maintenance_input.setSpecialValueText("غير محدد / Not scheduled")
        layout.addRow("الصيانة القادمة / Next Maintenance:", self.next_maintenance_input)
        
        # Buttons
        if not self.view_only:
            buttons_layout = QHBoxLayout()
            
            save_btn = QPushButton("حفظ / Save")
            save_btn.clicked.connect(self.save_equipment)
            
            cancel_btn = QPushButton("إلغاء / Cancel")
            cancel_btn.clicked.connect(self.reject)
            
            buttons_layout.addWidget(save_btn)
            buttons_layout.addWidget(cancel_btn)
            
            layout.addRow(buttons_layout)
        else:
            # View only mode
            close_btn = QPushButton("إغلاق / Close")
            close_btn.clicked.connect(self.accept)
            layout.addRow(close_btn)
            
            # Disable all inputs
            self.setWindowTitle("عرض المعدة / View Equipment")
            for widget in self.findChildren((QLineEdit, QComboBox, QDateEdit, QDoubleSpinBox)):
                widget.setEnabled(False)
        
        self.setLayout(layout)
        
    def load_equipment_data(self):
        """Load existing equipment data"""
        conn = self.db_manager.get_connection()
        cursor = conn.cursor()
        
        cursor.execute("SELECT * FROM equipment WHERE id = ?", (self.equipment_id,))
        equipment = cursor.fetchone()
        
        if equipment:
            self.equipment_code_input.setText(equipment['equipment_code'])
            self.name_input.setText(equipment['name'])
            self.type_combo.setCurrentText(equipment['type'])
            self.manufacturer_input.setText(equipment['manufacturer'] or '')
            self.model_input.setText(equipment['model'] or '')
            self.serial_number_input.setText(equipment['serial_number'] or '')
            
            if equipment['purchase_date']:
                self.purchase_date_input.setDate(QDate.fromString(equipment['purchase_date'], "yyyy-MM-dd"))
                
            self.purchase_cost_input.setValue(equipment['purchase_cost'] or 0)
            self.location_input.setText(equipment['location'] or '')
            
            # Set status
            status_map = {
                'operational': 0,
                'under_maintenance': 1,
                'out_of_service': 2
            }
            self.status_combo.setCurrentIndex(status_map.get(equipment['status'], 0))
            
            if equipment['last_maintenance']:
                self.last_maintenance_input.setDate(QDate.fromString(equipment['last_maintenance'], "yyyy-MM-dd"))
                
            if equipment['next_maintenance']:
                self.next_maintenance_input.setDate(QDate.fromString(equipment['next_maintenance'], "yyyy-MM-dd"))
                
        conn.close()
        
    def save_equipment(self):
        """Save equipment data"""
        equipment_code = self.equipment_code_input.text().strip()
        name = self.name_input.text().strip()
        
        if not equipment_code:
            QMessageBox.warning(self, "تحذير / Warning", "يرجى إدخال كود المعدة\nPlease enter equipment code")
            return
            
        if not name:
            QMessageBox.warning(self, "تحذير / Warning", "يرجى إدخال اسم المعدة\nPlease enter equipment name")
            return
            
        conn = self.db_manager.get_connection()
        cursor = conn.cursor()
        
        try:
            status_map = ['operational', 'under_maintenance', 'out_of_service']
            status = status_map[self.status_combo.currentIndex()]
            
            if self.equipment_id:
                # Update existing equipment
                cursor.execute("""
                    UPDATE equipment 
                    SET equipment_code = ?, name = ?, type = ?, manufacturer = ?, model = ?,
                        serial_number = ?, purchase_date = ?, purchase_cost = ?, location = ?,
                        status = ?, last_maintenance = ?, next_maintenance = ?
                    WHERE id = ?
                """, (equipment_code, name, self.type_combo.currentText(),
                     self.manufacturer_input.text(), self.model_input.text(),
                     self.serial_number_input.text(),
                     self.purchase_date_input.date().toString("yyyy-MM-dd"),
                     self.purchase_cost_input.value(), self.location_input.text(),
                     status,
                     self.last_maintenance_input.date().toString("yyyy-MM-dd") if self.last_maintenance_input.date().isValid() else None,
                     self.next_maintenance_input.date().toString("yyyy-MM-dd") if self.next_maintenance_input.date().isValid() else None,
                     self.equipment_id))
            else:
                # Add new equipment
                cursor.execute("""
                    INSERT INTO equipment 
                    (equipment_code, name, type, manufacturer, model, serial_number,
                     purchase_date, purchase_cost, location, status, last_maintenance, next_maintenance)
                    VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                """, (equipment_code, name, self.type_combo.currentText(),
                     self.manufacturer_input.text(), self.model_input.text(),
                     self.serial_number_input.text(),
                     self.purchase_date_input.date().toString("yyyy-MM-dd"),
                     self.purchase_cost_input.value(), self.location_input.text(),
                     status,
                     self.last_maintenance_input.date().toString("yyyy-MM-dd") if self.last_maintenance_input.date().isValid() else None,
                     self.next_maintenance_input.date().toString("yyyy-MM-dd") if self.next_maintenance_input.date().isValid() else None))
                
            conn.commit()
            self.accept()
            
        except Exception as e:
            QMessageBox.critical(self, "خطأ / Error", f"Error saving equipment: {str(e)}")
        finally:
            conn.close()

class MaintenanceScheduleDialog(QDialog):
    def __init__(self, db_manager, schedule_id=None):
        super().__init__()
        self.db_manager = db_manager
        self.schedule_id = schedule_id
        self.init_ui()
        
        if schedule_id:
            self.load_schedule_data()
            
    def init_ui(self):
        self.setWindowTitle("جدولة الصيانة / Maintenance Schedule")
        self.setFixedSize(400, 300)
        
        layout = QFormLayout()
        
        # Equipment selection
        self.equipment_combo = QComboBox()
        self.load_equipment()
        layout.addRow("المعدة / Equipment:", self.equipment_combo)
        
        # Maintenance type
        self.maintenance_type_combo = QComboBox()
        self.maintenance_type_combo.addItems([
            "صيانة وقائية / Preventive", "صيانة تصحيحية / Corrective",
            "معايرة / Calibration", "فحص / Inspection"
        ])
        layout.addRow("نوع الصيانة / Maintenance Type:", self.maintenance_type_combo)
        
        # Frequency
        self.frequency_input = QSpinBox()
        self.frequency_input.setRange(1, 365)
        self.frequency_input.setValue(30)
        self.frequency_input.setSuffix(" يوم / days")
        layout.addRow("التكرار / Frequency:", self.frequency_input)
        
        # Last performed
        self.last_performed_input = QDateEdit()
        self.last_performed_input.setCalendarPopup(True)
        self.last_performed_input.setSpecialValueText("لم يتم / Not performed")
        layout.addRow("آخر تنفيذ / Last Performed:", self.last_performed_input)
        
        # Next due (calculated automatically)
        self.next_due_input = QDateEdit()
        self.next_due_input.setCalendarPopup(True)
        self.next_due_input.setReadOnly(True)
        layout.addRow("الموعد القادم / Next Due:", self.next_due_input)
        
        # Connect signals to calculate next due date
        self.last_performed_input.dateChanged.connect(self.calculate_next_due)
        self.frequency_input.valueChanged.connect(self.calculate_next_due)
        
        # Active status
        self.is_active_combo = QComboBox()
        self.is_active_combo.addItems(["نشط / Active", "غير نشط / Inactive"])
        layout.addRow("الحالة / Status:", self.is_active_combo)
        
        # Buttons
        buttons_layout = QHBoxLayout()
        
        save_btn = QPushButton("حفظ / Save")
        save_btn.clicked.connect(self.save_schedule)
        
        cancel_btn = QPushButton("إلغاء / Cancel")
        cancel_btn.clicked.connect(self.reject)
        
        buttons_layout.addWidget(save_btn)
        buttons_layout.addWidget(cancel_btn)
        
        layout.addRow(buttons_layout)
        
        self.setLayout(layout)
        
        # Calculate initial next due date
        self.calculate_next_due()
        
    def load_equipment(self):
        """Load equipment for selection"""
        conn = self.db_manager.get_connection()
        cursor = conn.cursor()
        
        cursor.execute("SELECT id, equipment_code, name FROM equipment ORDER BY equipment_code")
        equipment_list = cursor.fetchall()
        
        for equipment in equipment_list:
            display_text = f"{equipment['equipment_code']} - {equipment['name']}"
            self.equipment_combo.addItem(display_text, equipment['id'])
            
        conn.close()
        
    def calculate_next_due(self):
        """Calculate next due date based on last performed and frequency"""
        if self.last_performed_input.date().isValid():
            last_performed = self.last_performed_input.date()
            frequency = self.frequency_input.value()
            next_due = last_performed.addDays(frequency)
            self.next_due_input.setDate(next_due)
        else:
            # If no last performed date, use today + frequency
            today = QDate.currentDate()
            frequency = self.frequency_input.value()
            next_due = today.addDays(frequency)
            self.next_due_input.setDate(next_due)
            
    def load_schedule_data(self):
        """Load existing schedule data"""
        conn = self.db_manager.get_connection()
        cursor = conn.cursor()
        
        cursor.execute("SELECT * FROM maintenance_schedules WHERE id = ?", (self.schedule_id,))
        schedule = cursor.fetchone()
        
        if schedule:
            # Set equipment
            for i in range(self.equipment_combo.count()):
                if self.equipment_combo.itemData(i) == schedule['equipment_id']:
                    self.equipment_combo.setCurrentIndex(i)
                    break
                    
            self.maintenance_type_combo.setCurrentText(schedule['maintenance_type'])
            self.frequency_input.setValue(schedule['frequency_days'])
            
            if schedule['last_performed']:
                self.last_performed_input.setDate(QDate.fromString(schedule['last_performed'], "yyyy-MM-dd"))
                
            if schedule['next_due']:
                self.next_due_input.setDate(QDate.fromString(schedule['next_due'], "yyyy-MM-dd"))
                
            self.is_active_combo.setCurrentIndex(0 if schedule['is_active'] else 1)
            
        conn.close()
        
    def save_schedule(self):
        """Save maintenance schedule"""
        equipment_id = self.equipment_combo.currentData()
        
        if not equipment_id:
            QMessageBox.warning(self, "تحذير / Warning", "يرجى اختيار المعدة\nPlease select equipment")
            return
            
        conn = self.db_manager.get_connection()
        cursor = conn.cursor()
        
        try:
            is_active = self.is_active_combo.currentIndex() == 0
            
            if self.schedule_id:
                # Update existing schedule
                cursor.execute("""
                    UPDATE maintenance_schedules 
                    SET equipment_id = ?, maintenance_type = ?, frequency_days = ?,
                        last_performed = ?, next_due = ?, is_active = ?
                    WHERE id = ?
                """, (equipment_id, self.maintenance_type_combo.currentText(),
                     self.frequency_input.value(),
                     self.last_performed_input.date().toString("yyyy-MM-dd") if self.last_performed_input.date().isValid() else None,
                     self.next_due_input.date().toString("yyyy-MM-dd"),
                     is_active, self.schedule_id))
            else:
                # Add new schedule
                cursor.execute("""
                    INSERT INTO maintenance_schedules 
                    (equipment_id, maintenance_type, frequency_days, last_performed, next_due, is_active)
                    VALUES (?, ?, ?, ?, ?, ?)
                """, (equipment_id, self.maintenance_type_combo.currentText(),
                     self.frequency_input.value(),
                     self.last_performed_input.date().toString("yyyy-MM-dd") if self.last_performed_input.date().isValid() else None,
                     self.next_due_input.date().toString("yyyy-MM-dd"),
                     is_active))
                
            conn.commit()
            self.accept()
            
        except Exception as e:
            QMessageBox.critical(self, "خطأ / Error", f"Error saving schedule: {str(e)}")
        finally:
            conn.close()

class MaintenanceRecordDialog(QDialog):
    def __init__(self, db_manager, user_data, record_id=None, equipment_id=None, 
                 maintenance_type=None, schedule_id=None, view_only=False):
        super().__init__()
        self.db_manager = db_manager
        self.user_data = user_data
        self.record_id = record_id
        self.schedule_id = schedule_id
        self.view_only = view_only
        self.init_ui()
        
        if equipment_id:
            self.set_equipment(equipment_id)
        if maintenance_type:
            self.maintenance_type_combo.setCurrentText(maintenance_type)
        if record_id:
            self.load_record_data()
            
    def init_ui(self):
        self.setWindowTitle("سجل الصيانة / Maintenance Record")
        self.setFixedSize(500, 600)
        
        layout = QFormLayout()
        
        # Maintenance number
        self.maintenance_number_input = QLineEdit()
        self.maintenance_number_input.setText(self.generate_maintenance_number())
        self.maintenance_number_input.setReadOnly(True)
        layout.addRow("رقم الصيانة / Maintenance No.:", self.maintenance_number_input)
        
        # Equipment selection
        self.equipment_combo = QComboBox()
        self.load_equipment()
        layout.addRow("المعدة / Equipment:", self.equipment_combo)
        
        # Maintenance type
        self.maintenance_type_combo = QComboBox()
        self.maintenance_type_combo.addItems([
            "صيانة وقائية / Preventive", "صيانة تصحيحية / Corrective",
            "صيانة طارئة / Emergency", "معايرة / Calibration", "فحص / Inspection"
        ])
        layout.addRow("نوع الصيانة / Maintenance Type:", self.maintenance_type_combo)
        
        # Scheduled date
        self.scheduled_date_input = QDateEdit()
        self.scheduled_date_input.setCalendarPopup(True)
        self.scheduled_date_input.setSpecialValueText("غير مجدول / Not scheduled")
        layout.addRow("التاريخ المجدول / Scheduled Date:", self.scheduled_date_input)
        
        # Actual date
        self.actual_date_input = QDateEdit()
        self.actual_date_input.setDate(QDate.currentDate())
        self.actual_date_input.setCalendarPopup(True)
        layout.addRow("التاريخ الفعلي / Actual Date:", self.actual_date_input)
        
        # Technician
        self.technician_combo = QComboBox()
        self.load_technicians()
        layout.addRow("الفني / Technician:", self.technician_combo)
        
        # Duration
        self.duration_input = QDoubleSpinBox()
        self.duration_input.setRange(0, 24)
        self.duration_input.setDecimals(1)
        self.duration_input.setSuffix(" ساعة / hours")
        layout.addRow("المدة / Duration:", self.duration_input)
        
        # Cost
        self.cost_input = QDoubleSpinBox()
        self.cost_input.setRange(0, 100000)
        self.cost_input.setDecimals(2)
        layout.addRow("التكلفة / Cost:", self.cost_input)
        
        # Parts used
        self.parts_used_input = QTextEdit()
        self.parts_used_input.setMaximumHeight(60)
        self.parts_used_input.setPlaceholderText("القطع المستخدمة / Parts used")
        layout.addRow("القطع المستخدمة / Parts Used:", self.parts_used_input)
        
        # Work performed
        self.work_performed_input = QTextEdit()
        self.work_performed_input.setMaximumHeight(80)
        self.work_performed_input.setPlaceholderText("العمل المنجز / Work performed")
        layout.addRow("العمل المنجز / Work Performed:", self.work_performed_input)
        
        # Status
        self.status_combo = QComboBox()
        self.status_combo.addItems([
            "مكتمل / Completed", "قيد التنفيذ / In Progress", "مؤجل / Postponed"
        ])
        layout.addRow("الحالة / Status:", self.status_combo)
        
        # Buttons
        if not self.view_only:
            buttons_layout = QHBoxLayout()
            
            save_btn = QPushButton("حفظ / Save")
            save_btn.clicked.connect(self.save_record)
            
            cancel_btn = QPushButton("إلغاء / Cancel")
            cancel_btn.clicked.connect(self.reject)
            
            buttons_layout.addWidget(save_btn)
            buttons_layout.addWidget(cancel_btn)
            
            layout.addRow(buttons_layout)
        else:
            # View only mode
            close_btn = QPushButton("إغلاق / Close")
            close_btn.clicked.connect(self.accept)
            layout.addRow(close_btn)
            
            # Disable all inputs
            self.setWindowTitle("عرض سجل الصيانة / View Maintenance Record")
            for widget in self.findChildren((QLineEdit, QTextEdit, QComboBox, QDateEdit, QDoubleSpinBox)):
                widget.setEnabled(False)
        
        self.setLayout(layout)
        
    def generate_maintenance_number(self):
        """Generate unique maintenance number"""
        timestamp = datetime.now().strftime("%Y%m%d%H%M%S")
        return f"MR-{timestamp}"
        
    def load_equipment(self):
        """Load equipment for selection"""
        conn = self.db_manager.get_connection()
        cursor = conn.cursor()
        
        cursor.execute("SELECT id, equipment_code, name FROM equipment ORDER BY equipment_code")
        equipment_list = cursor.fetchall()
        
        self.equipment_combo.addItem("اختر المعدة / Select Equipment", None)
        for equipment in equipment_list:
            display_text = f"{equipment['equipment_code']} - {equipment['name']}"
            self.equipment_combo.addItem(display_text, equipment['id'])
            
        conn.close()
        
    def load_technicians(self):
        """Load technicians (users with maintenance role)"""
        conn = self.db_manager.get_connection()
        cursor = conn.cursor()
        
        cursor.execute("""
            SELECT id, full_name FROM users 
            WHERE role IN ('admin', 'technician', 'maintenance') AND is_active = 1
            ORDER BY full_name
        """)
        
        technicians = cursor.fetchall()
        
        self.technician_combo.addItem("اختر الفني / Select Technician", None)
        for technician in technicians:
            self.technician_combo.addItem(technician['full_name'], technician['id'])
            
        # Set current user as default
        for i in range(self.technician_combo.count()):
            if self.technician_combo.itemData(i) == self.user_data['id']:
                self.technician_combo.setCurrentIndex(i)
                break
                
        conn.close()
        
    def set_equipment(self, equipment_id):
        """Set equipment selection"""
        for i in range(self.equipment_combo.count()):
            if self.equipment_combo.itemData(i) == equipment_id:
                self.equipment_combo.setCurrentIndex(i)
                break
                
    def load_record_data(self):
        """Load existing record data"""
        conn = self.db_manager.get_connection()
        cursor = conn.cursor()
        
        cursor.execute("SELECT * FROM maintenance_records WHERE id = ?", (self.record_id,))
        record = cursor.fetchone()
        
        if record:
            self.maintenance_number_input.setText(record['maintenance_number'])
            
            # Set equipment
            for i in range(self.equipment_combo.count()):
                if self.equipment_combo.itemData(i) == record['equipment_id']:
                    self.equipment_combo.setCurrentIndex(i)
                    break
                    
            self.maintenance_type_combo.setCurrentText(record['maintenance_type'])
            
            if record['scheduled_date']:
                self.scheduled_date_input.setDate(QDate.fromString(record['scheduled_date'], "yyyy-MM-dd"))
                
            self.actual_date_input.setDate(QDate.fromString(record['actual_date'], "yyyy-MM-dd"))
            
            # Set technician
            for i in range(self.technician_combo.count()):
                if self.technician_combo.itemData(i) == record['technician_id']:
                    self.technician_combo.setCurrentIndex(i)
                    break
                    
            self.duration_input.setValue(record['duration_hours'] or 0)
            self.cost_input.setValue(record['cost'] or 0)
            self.parts_used_input.setPlainText(record['parts_used'] or '')
            self.work_performed_input.setPlainText(record['work_performed'] or '')
            
            # Set status
            status_map = {
                'completed': 0,
                'in_progress': 1,
                'postponed': 2
            }
            self.status_combo.setCurrentIndex(status_map.get(record['status'], 0))
            
        conn.close()
        
    def save_record(self):
        """Save maintenance record"""
        equipment_id = self.equipment_combo.currentData()
        technician_id = self.technician_combo.currentData()
        
        if not equipment_id:
            QMessageBox.warning(self, "تحذير / Warning", "يرجى اختيار المعدة\nPlease select equipment")
            return
            
        if not technician_id:
            QMessageBox.warning(self, "تحذير / Warning", "يرجى اختيار الفني\nPlease select technician")
            return
            
        conn = self.db_manager.get_connection()
        cursor = conn.cursor()
        
        try:
            status_map = ['completed', 'in_progress', 'postponed']
            status = status_map[self.status_combo.currentIndex()]
            
            if self.record_id:
                # Update existing record
                cursor.execute("""
                    UPDATE maintenance_records 
                    SET equipment_id = ?, maintenance_type = ?, scheduled_date = ?,
                        actual_date = ?, technician_id = ?, duration_hours = ?, cost = ?,
                        parts_used = ?, work_performed = ?, status = ?
                    WHERE id = ?
                """, (equipment_id, self.maintenance_type_combo.currentText(),
                     self.scheduled_date_input.date().toString("yyyy-MM-dd") if self.scheduled_date_input.date().isValid() else None,
                     self.actual_date_input.date().toString("yyyy-MM-dd"),
                     technician_id, self.duration_input.value(), self.cost_input.value(),
                     self.parts_used_input.toPlainText(), self.work_performed_input.toPlainText(),
                     status, self.record_id))
            else:
                # Add new record
                cursor.execute("""
                    INSERT INTO maintenance_records 
                    (maintenance_number, equipment_id, maintenance_type, scheduled_date,
                     actual_date, technician_id, duration_hours, cost, parts_used,
                     work_performed, status)
                    VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                """, (self.maintenance_number_input.text(), equipment_id,
                     self.maintenance_type_combo.currentText(),
                     self.scheduled_date_input.date().toString("yyyy-MM-dd") if self.scheduled_date_input.date().isValid() else None,
                     self.actual_date_input.date().toString("yyyy-MM-dd"),
                     technician_id, self.duration_input.value(), self.cost_input.value(),
                     self.parts_used_input.toPlainText(), self.work_performed_input.toPlainText(),
                     status))
            
            # Update equipment last maintenance date if completed
            if status == 'completed':
                cursor.execute("""
                    UPDATE equipment 
                    SET last_maintenance = ?
                    WHERE id = ?
                """, (self.actual_date_input.date().toString("yyyy-MM-dd"), equipment_id))
                
                # Update schedule if this was scheduled maintenance
                if self.schedule_id:
                    cursor.execute("""
                        UPDATE maintenance_schedules 
                        SET last_performed = ?, next_due = date(?, '+' || frequency_days || ' days')
                        WHERE id = ?
                    """, (self.actual_date_input.date().toString("yyyy-MM-dd"),
                         self.actual_date_input.date().toString("yyyy-MM-dd"),
                         self.schedule_id))
            
            conn.commit()
            
            QMessageBox.information(self, "نجح / Success", 
                                  "تم حفظ سجل الصيانة بنجاح\nMaintenance record saved successfully")
            
            self.accept()
            
        except Exception as e:
            QMessageBox.critical(self, "خطأ / Error", f"Error saving record: {str(e)}")
        finally:
            conn.close()
