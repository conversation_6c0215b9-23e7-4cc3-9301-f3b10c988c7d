from PyQt5.QtWidgets import (QWidget, QVBoxLayout, QHBoxLayout, QTableWidget, 
                            QTableWidgetItem, QPushButton, QLineEdit, QLabel,
                            QDialog, QFormLayout, QMessageBox, QHeaderView,
                            QDateEdit, QDoubleSpinBox, QComboBox, QTextEdit,
                            QSpinBox, QTabWidget, QGroupBox, QGridLayout)
from PyQt5.QtCore import Qt, QDate, pyqtSignal
from PyQt5.QtGui import QFont, QColor
from datetime import datetime, timedelta
import json

class SalesQuotationModule(QWidget):
    def __init__(self, db_manager, user_data):
        super().__init__()
        self.db_manager = db_manager
        self.user_data = user_data
        self.init_ui()
        self.load_quotations()
        
    def init_ui(self):
        layout = QVBoxLayout()
        
        # Header
        header_layout = QHBoxLayout()
        
        title = QLabel("عروض الأسعار / Sales Quotations")
        title.setStyleSheet("font-size: 16pt; font-weight: bold; color: #0078d4;")
        header_layout.addWidget(title)
        
        header_layout.addStretch()
        
        # New quotation button
        new_quotation_btn = QPushButton("عرض سعر جديد / New Quotation")
        new_quotation_btn.setStyleSheet("background-color: #28a745;")
        new_quotation_btn.clicked.connect(self.create_new_quotation)
        header_layout.addWidget(new_quotation_btn)
        
        layout.addLayout(header_layout)
        
        # Filter controls
        filter_layout = QHBoxLayout()
        
        status_label = QLabel("الحالة / Status:")
        self.status_filter = QComboBox()
        self.status_filter.addItems([
            "الكل / All", "مسودة / Draft", "مرسل / Sent", 
            "مقبول / Accepted", "مرفوض / Rejected", "منتهي الصلاحية / Expired"
        ])
        self.status_filter.currentTextChanged.connect(self.filter_quotations)
        
        customer_label = QLabel("العميل / Customer:")
        self.customer_filter = QComboBox()
        self.load_customers_filter()
        self.customer_filter.currentTextChanged.connect(self.filter_quotations)
        
        filter_layout.addWidget(status_label)
        filter_layout.addWidget(self.status_filter)
        filter_layout.addWidget(customer_label)
        filter_layout.addWidget(self.customer_filter)
        filter_layout.addStretch()
        
        layout.addLayout(filter_layout)
        
        # Quotations table
        self.quotations_table = QTableWidget()
        self.quotations_table.setColumnCount(8)
        self.quotations_table.setHorizontalHeaderLabels([
            "رقم العرض / Quotation No.", "العميل / Customer", "تاريخ العرض / Date",
            "صالح حتى / Valid Until", "المبلغ النهائي / Final Amount", 
            "الحالة / Status", "الإجراءات / Actions", "ID"
        ])
        
        self.quotations_table.hideColumn(7)  # Hide ID column
        
        # Set column widths
        header = self.quotations_table.horizontalHeader()
        header.setSectionResizeMode(QHeaderView.Stretch)
        
        layout.addWidget(self.quotations_table)
        
        self.setLayout(layout)
        
    def load_customers_filter(self):
        """Load customers for filter dropdown"""
        conn = self.db_manager.get_connection()
        cursor = conn.cursor()
        
        cursor.execute("SELECT id, name FROM customers ORDER BY name")
        customers = cursor.fetchall()
        
        self.customer_filter.addItem("الكل / All", None)
        for customer in customers:
            self.customer_filter.addItem(customer['name'], customer['id'])
            
        conn.close()
        
    def load_quotations(self):
        """Load quotations from database"""
        conn = self.db_manager.get_connection()
        cursor = conn.cursor()
        
        cursor.execute("""
            SELECT sq.id, sq.quotation_number, c.name as customer_name, 
                   sq.quotation_date, sq.valid_until, sq.final_amount, sq.status
            FROM sales_quotations sq
            JOIN customers c ON sq.customer_id = c.id
            ORDER BY sq.created_at DESC
        """)
        
        quotations = cursor.fetchall()
        
        self.quotations_table.setRowCount(len(quotations))
        
        for row, quotation in enumerate(quotations):
            self.quotations_table.setItem(row, 0, QTableWidgetItem(quotation['quotation_number']))
            self.quotations_table.setItem(row, 1, QTableWidgetItem(quotation['customer_name']))
            self.quotations_table.setItem(row, 2, QTableWidgetItem(quotation['quotation_date']))
            self.quotations_table.setItem(row, 3, QTableWidgetItem(quotation['valid_until']))
            self.quotations_table.setItem(row, 4, QTableWidgetItem(f"{quotation['final_amount']:.2f}"))
            
            # Status with color coding
            status_item = QTableWidgetItem(quotation['status'])
            if quotation['status'] == 'accepted':
                status_item.setBackground(QColor('#28a745'))
            elif quotation['status'] == 'rejected':
                status_item.setBackground(QColor('#dc3545'))
            elif quotation['status'] == 'expired':
                status_item.setBackground(QColor('#6c757d'))
            elif quotation['status'] == 'sent':
                status_item.setBackground(QColor('#17a2b8'))
            
            self.quotations_table.setItem(row, 5, status_item)
            
            # Action buttons
            actions_widget = QWidget()
            actions_layout = QHBoxLayout(actions_widget)
            actions_layout.setContentsMargins(5, 5, 5, 5)
            
            view_btn = QPushButton("عرض / View")
            view_btn.setFixedSize(60, 25)
            view_btn.clicked.connect(lambda checked, qid=quotation['id']: self.view_quotation(qid))
            
            edit_btn = QPushButton("تعديل / Edit")
            edit_btn.setFixedSize(60, 25)
            edit_btn.clicked.connect(lambda checked, qid=quotation['id']: self.edit_quotation(qid))
            
            convert_btn = QPushButton("تحويل / Convert")
            convert_btn.setFixedSize(70, 25)
            convert_btn.setStyleSheet("background-color: #28a745;")
            convert_btn.clicked.connect(lambda checked, qid=quotation['id']: self.convert_to_invoice(qid))
            
            actions_layout.addWidget(view_btn)
            actions_layout.addWidget(edit_btn)
            actions_layout.addWidget(convert_btn)
            
            self.quotations_table.setCellWidget(row, 6, actions_widget)
            
            # Store ID
            self.quotations_table.setItem(row, 7, QTableWidgetItem(str(quotation['id'])))
            
        conn.close()
        
    def create_new_quotation(self):
        """Create new quotation"""
        dialog = QuotationDialog(self.db_manager, self.user_data)
        if dialog.exec_() == QDialog.Accepted:
            self.load_quotations()
            
    def view_quotation(self, quotation_id):
        """View quotation details"""
        dialog = QuotationDialog(self.db_manager, self.user_data, quotation_id, view_only=True)
        dialog.exec_()
        
    def edit_quotation(self, quotation_id):
        """Edit quotation"""
        dialog = QuotationDialog(self.db_manager, self.user_data, quotation_id)
        if dialog.exec_() == QDialog.Accepted:
            self.load_quotations()
            
    def convert_to_invoice(self, quotation_id):
        """Convert quotation to sales invoice"""
        reply = QMessageBox.question(self, "تحويل إلى فاتورة / Convert to Invoice",
                                   "هل تريد تحويل عرض السعر إلى فاتورة مبيعات؟\n"
                                   "Do you want to convert this quotation to a sales invoice?",
                                   QMessageBox.Yes | QMessageBox.No)
        
        if reply == QMessageBox.Yes:
            conn = self.db_manager.get_connection()
            cursor = conn.cursor()
            
            try:
                # Get quotation details
                cursor.execute("SELECT * FROM sales_quotations WHERE id = ?", (quotation_id,))
                quotation = cursor.fetchone()
                
                if quotation:
                    # Generate invoice number
                    timestamp = datetime.now().strftime("%Y%m%d%H%M%S")
                    invoice_number = f"INV-{timestamp}"
                    
                    # Create sales invoice
                    cursor.execute("""
                        INSERT INTO sales 
                        (invoice_number, quotation_id, customer_id, sale_date, total_amount,
                         discount_percent, discount_amount, tax_percent, tax_amount, final_amount,
                         status, payment_terms, due_date, notes, created_by)
                        VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, 'pending', ?, ?, ?, ?)
                    """, (invoice_number, quotation_id, quotation['customer_id'],
                         datetime.now().date().isoformat(), quotation['total_amount'],
                         quotation['discount_percent'], quotation['discount_amount'],
                         quotation['tax_percent'], quotation['tax_amount'], quotation['final_amount'],
                         30, (datetime.now() + timedelta(days=30)).date().isoformat(),
                         quotation['notes'], self.user_data['id']))
                    
                    sale_id = cursor.lastrowid
                    
                    # Copy quotation items to sale items
                    cursor.execute("""
                        SELECT * FROM sales_quotation_items WHERE quotation_id = ?
                    """, (quotation_id,))
                    
                    quotation_items = cursor.fetchall()
                    
                    for item in quotation_items:
                        cursor.execute("""
                            INSERT INTO sale_items 
                            (sale_id, slab_id, quantity, unit_price, total_price)
                            VALUES (?, ?, ?, ?, ?)
                        """, (sale_id, item['slab_id'], item['quantity'],
                             item['unit_price'], item['total_price']))
                    
                    # Update quotation status
                    cursor.execute("""
                        UPDATE sales_quotations 
                        SET status = 'accepted'
                        WHERE id = ?
                    """, (quotation_id,))
                    
                    conn.commit()
                    
                    QMessageBox.information(self, "نجح / Success", 
                                          f"تم تحويل عرض السعر إلى فاتورة بنجاح\n"
                                          f"Quotation converted to invoice successfully\n"
                                          f"رقم الفاتورة / Invoice No.: {invoice_number}")
                    
                    self.load_quotations()
                    
            except Exception as e:
                QMessageBox.critical(self, "خطأ / Error", f"Error converting quotation: {str(e)}")
            finally:
                conn.close()
                
    def filter_quotations(self):
        """Filter quotations by status and customer"""
        status_filter = self.status_filter.currentText()
        customer_id = self.customer_filter.currentData()
        
        for row in range(self.quotations_table.rowCount()):
            show_row = True
            
            # Filter by status
            if not ("الكل" in status_filter or "All" in status_filter):
                status_item = self.quotations_table.item(row, 5)
                if status_item:
                    if not any(keyword in status_filter.lower() 
                             for keyword in status_item.text().lower().split()):
                        show_row = False
                        
            # Filter by customer
            if show_row and customer_id:
                # This would need more complex logic to match customer
                pass
                
            self.quotations_table.setRowHidden(row, not show_row)

class QuotationDialog(QDialog):
    def __init__(self, db_manager, user_data, quotation_id=None, view_only=False):
        super().__init__()
        self.db_manager = db_manager
        self.user_data = user_data
        self.quotation_id = quotation_id
        self.view_only = view_only
        self.quotation_items = []
        self.init_ui()
        
        if quotation_id:
            self.load_quotation_data()
            
    def init_ui(self):
        self.setWindowTitle("عرض سعر / Sales Quotation")
        self.setFixedSize(900, 700)
        
        layout = QVBoxLayout()
        
        # Quotation header
        header_group = QGroupBox("معلومات العرض / Quotation Information")
        header_layout = QFormLayout(header_group)
        
        self.quotation_number_input = QLineEdit()
        self.quotation_number_input.setText(self.generate_quotation_number())
        self.quotation_number_input.setReadOnly(True)
        header_layout.addRow("رقم العرض / Quotation No.:", self.quotation_number_input)
        
        # Customer selection
        self.customer_combo = QComboBox()
        self.load_customers()
        header_layout.addRow("العميل / Customer:", self.customer_combo)
        
        # Quotation date
        self.quotation_date_input = QDateEdit()
        self.quotation_date_input.setDate(QDate.currentDate())
        self.quotation_date_input.setCalendarPopup(True)
        header_layout.addRow("تاريخ العرض / Quotation Date:", self.quotation_date_input)
        
        # Valid until
        self.valid_until_input = QDateEdit()
        self.valid_until_input.setDate(QDate.currentDate().addDays(30))
        self.valid_until_input.setCalendarPopup(True)
        header_layout.addRow("صالح حتى / Valid Until:", self.valid_until_input)
        
        # Status
        self.status_combo = QComboBox()
        self.status_combo.addItems([
            "مسودة / Draft", "مرسل / Sent", "مقبول / Accepted", 
            "مرفوض / Rejected", "منتهي الصلاحية / Expired"
        ])
        header_layout.addRow("الحالة / Status:", self.status_combo)
        
        layout.addWidget(header_group)
        
        # Add item section
        if not self.view_only:
            add_item_group = QGroupBox("إضافة عنصر / Add Item")
            add_item_layout = QHBoxLayout(add_item_group)
            
            self.slab_combo = QComboBox()
            self.load_slabs()
            add_item_layout.addWidget(QLabel("البلاطة / Slab:"))
            add_item_layout.addWidget(self.slab_combo)
            
            self.quantity_input = QDoubleSpinBox()
            self.quantity_input.setRange(0.1, 1000)
            self.quantity_input.setValue(1)
            self.quantity_input.setDecimals(2)
            add_item_layout.addWidget(QLabel("الكمية / Quantity:"))
            add_item_layout.addWidget(self.quantity_input)
            
            self.unit_price_input = QDoubleSpinBox()
            self.unit_price_input.setRange(0, 100000)
            self.unit_price_input.setDecimals(2)
            add_item_layout.addWidget(QLabel("السعر / Unit Price:"))
            add_item_layout.addWidget(self.unit_price_input)
            
            add_item_btn = QPushButton("إضافة / Add")
            add_item_btn.clicked.connect(self.add_item)
            add_item_layout.addWidget(add_item_btn)
            
            layout.addWidget(add_item_group)
        
        # Items table
        items_group = QGroupBox("عناصر العرض / Quotation Items")
        items_layout = QVBoxLayout(items_group)
        
        self.items_table = QTableWidget()
        self.items_table.setColumnCount(6 if not self.view_only else 5)
        headers = ["البلاطة / Slab", "الكمية / Quantity", "السعر / Unit Price", 
                  "المجموع / Total", "الوصف / Description"]
        if not self.view_only:
            headers.append("حذف / Remove")
        self.items_table.setHorizontalHeaderLabels(headers)
        
        items_layout.addWidget(self.items_table)
        
        layout.addWidget(items_group)
        
        # Totals section
        totals_group = QGroupBox("المجاميع / Totals")
        totals_layout = QFormLayout(totals_group)
        
        self.subtotal_input = QDoubleSpinBox()
        self.subtotal_input.setRange(0, 1000000)
        self.subtotal_input.setDecimals(2)
        self.subtotal_input.setReadOnly(True)
        totals_layout.addRow("المجموع الفرعي / Subtotal:", self.subtotal_input)
        
        self.discount_percent_input = QDoubleSpinBox()
        self.discount_percent_input.setRange(0, 100)
        self.discount_percent_input.setDecimals(2)
        self.discount_percent_input.setSuffix(" %")
        self.discount_percent_input.valueChanged.connect(self.calculate_totals)
        totals_layout.addRow("نسبة الخصم / Discount %:", self.discount_percent_input)
        
        self.discount_amount_input = QDoubleSpinBox()
        self.discount_amount_input.setRange(0, 1000000)
        self.discount_amount_input.setDecimals(2)
        self.discount_amount_input.setReadOnly(True)
        totals_layout.addRow("مبلغ الخصم / Discount Amount:", self.discount_amount_input)
        
        self.tax_percent_input = QDoubleSpinBox()
        self.tax_percent_input.setRange(0, 100)
        self.tax_percent_input.setDecimals(2)
        self.tax_percent_input.setSuffix(" %")
        self.tax_percent_input.setValue(15)  # Default VAT
        self.tax_percent_input.valueChanged.connect(self.calculate_totals)
        totals_layout.addRow("نسبة الضريبة / Tax %:", self.tax_percent_input)
        
        self.tax_amount_input = QDoubleSpinBox()
        self.tax_amount_input.setRange(0, 1000000)
        self.tax_amount_input.setDecimals(2)
        self.tax_amount_input.setReadOnly(True)
        totals_layout.addRow("مبلغ الضريبة / Tax Amount:", self.tax_amount_input)
        
        self.final_amount_input = QDoubleSpinBox()
        self.final_amount_input.setRange(0, 1000000)
        self.final_amount_input.setDecimals(2)
        self.final_amount_input.setReadOnly(True)
        self.final_amount_input.setStyleSheet("font-weight: bold; font-size: 14px;")
        totals_layout.addRow("المبلغ النهائي / Final Amount:", self.final_amount_input)
        
        layout.addWidget(totals_group)
        
        # Notes
        notes_group = QGroupBox("ملاحظات / Notes")
        notes_layout = QVBoxLayout(notes_group)
        
        self.notes_input = QTextEdit()
        self.notes_input.setMaximumHeight(80)
        self.notes_input.setPlaceholderText("ملاحظات إضافية / Additional notes")
        notes_layout.addWidget(self.notes_input)
        
        layout.addWidget(notes_group)
        
        # Buttons
        if not self.view_only:
            buttons_layout = QHBoxLayout()
            
            save_btn = QPushButton("حفظ / Save")
            save_btn.clicked.connect(self.save_quotation)
            
            cancel_btn = QPushButton("إلغاء / Cancel")
            cancel_btn.clicked.connect(self.reject)
            
            buttons_layout.addWidget(save_btn)
            buttons_layout.addWidget(cancel_btn)
            
            layout.addLayout(buttons_layout)
        else:
            # View only mode
            close_btn = QPushButton("إغلاق / Close")
            close_btn.clicked.connect(self.accept)
            layout.addWidget(close_btn)
            
            # Disable all inputs
            self.setWindowTitle("عرض عرض السعر / View Quotation")
            for widget in self.findChildren((QLineEdit, QTextEdit, QComboBox, QDateEdit, QDoubleSpinBox)):
                widget.setEnabled(False)
        
        self.setLayout(layout)
        
    def generate_quotation_number(self):
        """Generate unique quotation number"""
        timestamp = datetime.now().strftime("%Y%m%d%H%M%S")
        return f"QUO-{timestamp}"
        
    def load_customers(self):
        """Load customers for selection"""
        conn = self.db_manager.get_connection()
        cursor = conn.cursor()
        
        cursor.execute("SELECT id, name FROM customers ORDER BY name")
        customers = cursor.fetchall()
        
        self.customer_combo.addItem("اختر العميل / Select Customer", None)
        for customer in customers:
            self.customer_combo.addItem(customer['name'], customer['id'])
            
        conn.close()
        
    def load_slabs(self):
        """Load available slabs"""
        conn = self.db_manager.get_connection()
        cursor = conn.cursor()
        
        cursor.execute("""
            SELECT id, slab_number, granite_type, length, width, thickness, area
            FROM slabs
            WHERE status IN ('in_stock', 'quality_approved')
            ORDER BY slab_number
        """)
        
        slabs = cursor.fetchall()
        
        self.slab_combo.addItem("اختر البلاطة / Select Slab", None)
        for slab in slabs:
            display_text = f"{slab['slab_number']} - {slab['granite_type']} ({slab['area']:.2f} m²)"
            self.slab_combo.addItem(display_text, slab['id'])
            
        conn.close()
        
    def add_item(self):
        """Add item to quotation"""
        slab_id = self.slab_combo.currentData()
        if not slab_id:
            QMessageBox.warning(self, "تحذير / Warning", "يرجى اختيار البلاطة\nPlease select slab")
            return
            
        quantity = self.quantity_input.value()
        unit_price = self.unit_price_input.value()
        
        if unit_price == 0:
            QMessageBox.warning(self, "تحذير / Warning", "يرجى إدخال السعر\nPlease enter price")
            return
            
        total_price = quantity * unit_price
        
        # Get slab info
        conn = self.db_manager.get_connection()
        cursor = conn.cursor()
        cursor.execute("""
            SELECT slab_number, granite_type, length, width, thickness
            FROM slabs WHERE id = ?
        """, (slab_id,))
        slab = cursor.fetchone()
        conn.close()
        
        if slab:
            description = f"{slab['granite_type']} - {slab['length']}×{slab['width']}×{slab['thickness']}m"
            
            # Add to items list
            item = {
                'slab_id': slab_id,
                'slab_number': slab['slab_number'],
                'quantity': quantity,
                'unit_price': unit_price,
                'total_price': total_price,
                'description': description
            }
            self.quotation_items.append(item)
            
            # Update table
            self.update_items_table()
            
            # Clear inputs
            self.slab_combo.setCurrentIndex(0)
            self.quantity_input.setValue(1)
            self.unit_price_input.setValue(0)
            
    def update_items_table(self):
        """Update items table display"""
        self.items_table.setRowCount(len(self.quotation_items))
        
        subtotal = 0
        
        for row, item in enumerate(self.quotation_items):
            self.items_table.setItem(row, 0, QTableWidgetItem(item['slab_number']))
            self.items_table.setItem(row, 1, QTableWidgetItem(f"{item['quantity']:.2f}"))
            self.items_table.setItem(row, 2, QTableWidgetItem(f"{item['unit_price']:.2f}"))
            self.items_table.setItem(row, 3, QTableWidgetItem(f"{item['total_price']:.2f}"))
            self.items_table.setItem(row, 4, QTableWidgetItem(item['description']))
            
            if not self.view_only:
                # Delete button
                delete_btn = QPushButton("حذف / Delete")
                delete_btn.setFixedSize(80, 25)
                delete_btn.setStyleSheet("background-color: #dc3545;")
                delete_btn.clicked.connect(lambda checked, r=row: self.remove_item(r))
                self.items_table.setCellWidget(row, 5, delete_btn)
            
            subtotal += item['total_price']
            
        self.subtotal_input.setValue(subtotal)
        self.calculate_totals()
        
    def remove_item(self, row):
        """Remove item from quotation"""
        if 0 <= row < len(self.quotation_items):
            self.quotation_items.pop(row)
            self.update_items_table()
            
    def calculate_totals(self):
        """Calculate quotation totals"""
        subtotal = self.subtotal_input.value()
        discount_percent = self.discount_percent_input.value()
        tax_percent = self.tax_percent_input.value()
        
        discount_amount = subtotal * (discount_percent / 100)
        amount_after_discount = subtotal - discount_amount
        tax_amount = amount_after_discount * (tax_percent / 100)
        final_amount = amount_after_discount + tax_amount
        
        self.discount_amount_input.setValue(discount_amount)
        self.tax_amount_input.setValue(tax_amount)
        self.final_amount_input.setValue(final_amount)
        
    def load_quotation_data(self):
        """Load existing quotation data"""
        conn = self.db_manager.get_connection()
        cursor = conn.cursor()
        
        # Get quotation info
        cursor.execute("SELECT * FROM sales_quotations WHERE id = ?", (self.quotation_id,))
        quotation = cursor.fetchone()
        
        if quotation:
            self.quotation_number_input.setText(quotation['quotation_number'])
            
            # Set customer
            for i in range(self.customer_combo.count()):
                if self.customer_combo.itemData(i) == quotation['customer_id']:
                    self.customer_combo.setCurrentIndex(i)
                    break
                    
            self.quotation_date_input.setDate(QDate.fromString(quotation['quotation_date'], "yyyy-MM-dd"))
            self.valid_until_input.setDate(QDate.fromString(quotation['valid_until'], "yyyy-MM-dd"))
            
            # Set status
            status_map = {
                'draft': 0,
                'sent': 1,
                'accepted': 2,
                'rejected': 3,
                'expired': 4
            }
            self.status_combo.setCurrentIndex(status_map.get(quotation['status'], 0))
            
            self.discount_percent_input.setValue(quotation['discount_percent'])
            self.tax_percent_input.setValue(quotation['tax_percent'])
            self.notes_input.setPlainText(quotation['notes'] or '')
            
            # Get quotation items
            cursor.execute("""
                SELECT sqi.*, s.slab_number, s.granite_type, s.length, s.width, s.thickness
                FROM sales_quotation_items sqi
                JOIN slabs s ON sqi.slab_id = s.id
                WHERE sqi.quotation_id = ?
            """, (self.quotation_id,))
            
            items = cursor.fetchall()
            
            self.quotation_items = []
            for item in items:
                description = f"{item['granite_type']} - {item['length']}×{item['width']}×{item['thickness']}m"
                
                self.quotation_items.append({
                    'slab_id': item['slab_id'],
                    'slab_number': item['slab_number'],
                    'quantity': item['quantity'],
                    'unit_price': item['unit_price'],
                    'total_price': item['total_price'],
                    'description': description
                })
                
            self.update_items_table()
            
        conn.close()
        
    def save_quotation(self):
        """Save quotation to database"""
        customer_id = self.customer_combo.currentData()
        
        if not customer_id:
            QMessageBox.warning(self, "تحذير / Warning", "يرجى اختيار العميل\nPlease select customer")
            return
            
        if not self.quotation_items:
            QMessageBox.warning(self, "تحذير / Warning", "يرجى إضافة عناصر للعرض\nPlease add items to quotation")
            return
            
        conn = self.db_manager.get_connection()
        cursor = conn.cursor()
        
        try:
            status_map = ['draft', 'sent', 'accepted', 'rejected', 'expired']
            status = status_map[self.status_combo.currentIndex()]
            
            if self.quotation_id:
                # Update existing quotation
                cursor.execute("""
                    UPDATE sales_quotations 
                    SET customer_id = ?, quotation_date = ?, valid_until = ?, 
                        total_amount = ?, discount_percent = ?, discount_amount = ?,
                        tax_percent = ?, tax_amount = ?, final_amount = ?, 
                        status = ?, notes = ?
                    WHERE id = ?
                """, (customer_id, self.quotation_date_input.date().toString("yyyy-MM-dd"),
                     self.valid_until_input.date().toString("yyyy-MM-dd"),
                     self.subtotal_input.value(), self.discount_percent_input.value(),
                     self.discount_amount_input.value(), self.tax_percent_input.value(),
                     self.tax_amount_input.value(), self.final_amount_input.value(),
                     status, self.notes_input.toPlainText(), self.quotation_id))
                
                # Delete existing items
                cursor.execute("DELETE FROM sales_quotation_items WHERE quotation_id = ?", (self.quotation_id,))
                
                quotation_id = self.quotation_id
            else:
                # Add new quotation
                cursor.execute("""
                    INSERT INTO sales_quotations 
                    (quotation_number, customer_id, quotation_date, valid_until, 
                     total_amount, discount_percent, discount_amount, tax_percent, 
                     tax_amount, final_amount, status, notes, created_by)
                    VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                """, (self.quotation_number_input.text(), customer_id,
                     self.quotation_date_input.date().toString("yyyy-MM-dd"),
                     self.valid_until_input.date().toString("yyyy-MM-dd"),
                     self.subtotal_input.value(), self.discount_percent_input.value(),
                     self.discount_amount_input.value(), self.tax_percent_input.value(),
                     self.tax_amount_input.value(), self.final_amount_input.value(),
                     status, self.notes_input.toPlainText(), self.user_data['id']))
                
                quotation_id = cursor.lastrowid
            
            # Insert quotation items
            for item in self.quotation_items:
                cursor.execute("""
                    INSERT INTO sales_quotation_items 
                    (quotation_id, slab_id, description, quantity, unit_price, total_price)
                    VALUES (?, ?, ?, ?, ?, ?)
                """, (quotation_id, item['slab_id'], item['description'],
                     item['quantity'], item['unit_price'], item['total_price']))
            
            conn.commit()
            
            QMessageBox.information(self, "نجح / Success", 
                                  "تم حفظ عرض السعر بنجاح\nQuotation saved successfully")
            
            self.accept()
            
        except Exception as e:
            QMessageBox.critical(self, "خطأ / Error", f"Error saving quotation: {str(e)}")
        finally:
            conn.close()
