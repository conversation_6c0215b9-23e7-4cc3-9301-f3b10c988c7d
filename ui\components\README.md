# 🔘 UI Components - Button System

## Overview
This directory contains advanced UI components for Al-Hassan Stone Factory Management System, with a focus on comprehensive button implementations.

## Files

### `button_showcase.py`
**Purpose**: Comprehensive showcase of all button types and styles
**Features**:
- 38+ different button variations
- Interactive demonstrations
- Real-time testing capabilities
- Organized by categories

**Sections**:
1. **Basic Buttons** - Standard button types
2. **Colored Buttons** - 8 different color schemes
3. **Size Variants** - 4 different sizes
4. **Icon Buttons** - Buttons with emoji icons
5. **Special Buttons** - Gradient, transparent, rounded, outlined
6. **Interactive Buttons** - Toggle, counter, loading, confirm
7. **Application Buttons** - System-specific buttons

### `enhanced_buttons.py`
**Purpose**: Advanced button components with enhanced functionality
**Components**:
- `ActionButton` - Enhanced button with icon and description
- `IconButton` - Icon-only button with tooltip
- `ToggleButton` - Toggle button with on/off states
- `ButtonGroup` - Group of related buttons
- `FloatingActionButton` - Material Design FAB

## Usage Examples

### Basic Button Showcase
```python
from ui.components.button_showcase import ButtonShowcase

# Create and show the showcase
showcase = ButtonShowcase()
showcase.show()
```

### Enhanced Buttons
```python
from ui.components.enhanced_buttons import ActionButton, IconButton, ButtonGroup

# Action button with icon and description
action_btn = ActionButton(
    title="إضافة عميل",
    description="Add New Customer",
    icon="👤",
    button_type="primary"
)

# Icon-only button
icon_btn = IconButton("💾", "Save File", size=40)

# Button group
group = ButtonGroup("Customer Actions", "horizontal")
group.add_action_button("Add", "", "➕", "success")
group.add_action_button("Edit", "", "✏️", "warning")
group.add_action_button("Delete", "", "🗑️", "danger")
```

## Button Types

### Color Types
- **primary** - Main brand color (#0078d4)
- **success** - Green for success actions (#28a745)
- **warning** - Yellow for warnings (#ffc107)
- **danger** - Red for dangerous actions (#dc3545)
- **info** - Blue for information (#17a2b8)
- **secondary** - Gray for secondary actions (#6c757d)

### Size Variants
- **Small** - 100×30 pixels
- **Medium** - 130×40 pixels
- **Large** - 160×50 pixels
- **Extra Large** - 200×60 pixels

### Special Styles
- **Gradient** - Beautiful gradient backgrounds
- **Transparent** - Transparent with colored borders
- **Rounded** - Fully rounded corners
- **Outlined** - White background with colored borders

## Interactive Features

### Toggle Button
```python
toggle = ToggleButton("ON", "OFF")
toggle.toggled_custom.connect(handle_toggle)
```

### Counter Button
- Automatically counts clicks
- Displays count in button text
- Useful for statistics

### Loading Button
- Shows loading state
- Automatically disables during loading
- Returns to normal state when complete

### Confirm Button
- Requests confirmation before action
- Shows confirmation dialog
- Safe for sensitive operations

## Common Button Configurations

The system includes predefined configurations for common actions:

```python
COMMON_BUTTONS = {
    "save": {"title": "حفظ", "icon": "💾", "type": "success"},
    "edit": {"title": "تعديل", "icon": "✏️", "type": "warning"},
    "delete": {"title": "حذف", "icon": "🗑️", "type": "danger"},
    "view": {"title": "عرض", "icon": "👁️", "type": "info"},
    "print": {"title": "طباعة", "icon": "🖨️", "type": "secondary"},
    "export": {"title": "تصدير", "icon": "📤", "type": "primary"},
    "add": {"title": "إضافة", "icon": "➕", "type": "success"},
    "search": {"title": "بحث", "icon": "🔍", "type": "info"}
}
```

### Using Common Buttons
```python
from ui.components.enhanced_buttons import create_common_button

# Create a save button
save_btn = create_common_button("save", callback=save_data)

# Create a delete button
delete_btn = create_common_button("delete", callback=delete_item)
```

## Testing

### Run Button Showcase
```bash
python test_buttons.py
```

### Test Individual Components
```python
from ui.components.enhanced_buttons import ActionButton
from PyQt5.QtWidgets import QApplication
import sys

app = QApplication(sys.argv)
btn = ActionButton("Test", "Test Button", "🧪", "primary")
btn.show()
app.exec_()
```

## Styling Integration

All buttons automatically integrate with the modern theme system:
- Colors from `ModernTheme.COLORS`
- Consistent styling across components
- Hover and press effects
- Responsive design

## Best Practices

1. **Consistent Colors**: Use predefined color types
2. **Appropriate Icons**: Choose meaningful emoji icons
3. **Clear Labels**: Use descriptive button text
4. **Logical Grouping**: Group related buttons together
5. **Responsive Sizing**: Choose appropriate sizes for context

## Future Enhancements

Planned improvements:
- Animated button transitions
- More icon options
- Custom button shapes
- Advanced interaction patterns
- Accessibility improvements

## Performance

The button system is optimized for:
- Fast rendering
- Smooth animations
- Low memory usage
- Responsive interactions
- Cross-platform compatibility
