from PyQt5.QtWidgets import (QWidget, QVBoxLayout, QHBoxLayout, QTableWidget, 
                            QTableWidgetItem, QPushButton, QLineEdit, QLabel,
                            QDialog, QFormLayout, QMessageBox, QHeaderView,
                            QDateEdit, QComboBox, QTextEdit, QTabWidget,
                            QGroupBox, QGridLayout, QProgressBar, QCheckBox,
                            QSpinBox, QDoubleSpinBox)
from PyQt5.QtCore import Qt, QDate, QThread, pyqtSignal
from PyQt5.QtGui import QFont, QColor, QPixmap, QPainter
from datetime import datetime, timedelta
import json
import csv
import os

class AdvancedReportsModule(QWidget):
    def __init__(self, db_manager, user_data):
        super().__init__()
        self.db_manager = db_manager
        self.user_data = user_data
        self.init_ui()
        
    def init_ui(self):
        layout = QVBoxLayout()
        
        # Header
        header_layout = QHBoxLayout()
        
        title = QLabel("التقارير المتقدمة والتحليلات / Advanced Reports & Analytics")
        title.setStyleSheet("font-size: 16pt; font-weight: bold; color: #0078d4;")
        header_layout.addWidget(title)
        
        header_layout.addStretch()
        
        # Export all button
        export_all_btn = QPushButton("تصدير جميع التقارير / Export All Reports")
        export_all_btn.setStyleSheet("background-color: #28a745;")
        export_all_btn.clicked.connect(self.export_all_reports)
        header_layout.addWidget(export_all_btn)
        
        layout.addLayout(header_layout)
        
        # Create tabs
        self.tab_widget = QTabWidget()
        
        # Financial reports tab
        financial_tab = self.create_financial_reports_tab()
        self.tab_widget.addTab(financial_tab, "التقارير المالية / Financial Reports")
        
        # Production reports tab
        production_tab = self.create_production_reports_tab()
        self.tab_widget.addTab(production_tab, "تقارير الإنتاج / Production Reports")
        
        # Inventory reports tab
        inventory_tab = self.create_inventory_reports_tab()
        self.tab_widget.addTab(inventory_tab, "تقارير المخزون / Inventory Reports")
        
        # Quality reports tab
        quality_tab = self.create_quality_reports_tab()
        self.tab_widget.addTab(quality_tab, "تقارير الجودة / Quality Reports")
        
        # KPI dashboard tab
        kpi_tab = self.create_kpi_dashboard_tab()
        self.tab_widget.addTab(kpi_tab, "لوحة المؤشرات / KPI Dashboard")
        
        # Custom reports tab
        custom_tab = self.create_custom_reports_tab()
        self.tab_widget.addTab(custom_tab, "تقارير مخصصة / Custom Reports")
        
        layout.addWidget(self.tab_widget)
        
        self.setLayout(layout)
        
    def create_financial_reports_tab(self):
        """Create financial reports tab"""
        widget = QWidget()
        layout = QVBoxLayout(widget)
        
        # Report selection
        reports_group = QGroupBox("التقارير المالية المتاحة / Available Financial Reports")
        reports_layout = QGridLayout(reports_group)
        
        # Sales summary report
        sales_summary_btn = QPushButton("ملخص المبيعات / Sales Summary")
        sales_summary_btn.clicked.connect(lambda: self.generate_financial_report('sales_summary'))
        reports_layout.addWidget(sales_summary_btn, 0, 0)
        
        # Profit & Loss report
        profit_loss_btn = QPushButton("الأرباح والخسائر / Profit & Loss")
        profit_loss_btn.clicked.connect(lambda: self.generate_financial_report('profit_loss'))
        reports_layout.addWidget(profit_loss_btn, 0, 1)
        
        # Cash flow report
        cash_flow_btn = QPushButton("التدفق النقدي / Cash Flow")
        cash_flow_btn.clicked.connect(lambda: self.generate_financial_report('cash_flow'))
        reports_layout.addWidget(cash_flow_btn, 0, 2)
        
        # Customer aging report
        customer_aging_btn = QPushButton("أعمار الذمم / Customer Aging")
        customer_aging_btn.clicked.connect(lambda: self.generate_financial_report('customer_aging'))
        reports_layout.addWidget(customer_aging_btn, 1, 0)
        
        # Expense analysis report
        expense_analysis_btn = QPushButton("تحليل المصروفات / Expense Analysis")
        expense_analysis_btn.clicked.connect(lambda: self.generate_financial_report('expense_analysis'))
        reports_layout.addWidget(expense_analysis_btn, 1, 1)
        
        # Tax report
        tax_report_btn = QPushButton("التقرير الضريبي / Tax Report")
        tax_report_btn.clicked.connect(lambda: self.generate_financial_report('tax_report'))
        reports_layout.addWidget(tax_report_btn, 1, 2)
        
        layout.addWidget(reports_group)
        
        # Date range selection
        date_group = QGroupBox("نطاق التاريخ / Date Range")
        date_layout = QFormLayout(date_group)
        
        self.financial_from_date = QDateEdit()
        self.financial_from_date.setDate(QDate.currentDate().addDays(-30))
        self.financial_from_date.setCalendarPopup(True)
        date_layout.addRow("من / From:", self.financial_from_date)
        
        self.financial_to_date = QDateEdit()
        self.financial_to_date.setDate(QDate.currentDate())
        self.financial_to_date.setCalendarPopup(True)
        date_layout.addRow("إلى / To:", self.financial_to_date)
        
        layout.addWidget(date_group)
        
        # Report display
        self.financial_report_display = QTextEdit()
        self.financial_report_display.setReadOnly(True)
        self.financial_report_display.setFont(QFont("Courier", 10))
        layout.addWidget(self.financial_report_display)
        
        # Export buttons
        export_layout = QHBoxLayout()
        
        export_pdf_btn = QPushButton("تصدير PDF / Export PDF")
        export_pdf_btn.clicked.connect(lambda: self.export_report('pdf', self.financial_report_display.toPlainText()))
        
        export_excel_btn = QPushButton("تصدير Excel / Export Excel")
        export_excel_btn.clicked.connect(lambda: self.export_report('excel', self.financial_report_display.toPlainText()))
        
        export_layout.addWidget(export_pdf_btn)
        export_layout.addWidget(export_excel_btn)
        export_layout.addStretch()
        
        layout.addLayout(export_layout)
        
        return widget
        
    def create_production_reports_tab(self):
        """Create production reports tab"""
        widget = QWidget()
        layout = QVBoxLayout(widget)
        
        # Report selection
        reports_group = QGroupBox("تقارير الإنتاج المتاحة / Available Production Reports")
        reports_layout = QGridLayout(reports_group)
        
        # Production efficiency report
        efficiency_btn = QPushButton("كفاءة الإنتاج / Production Efficiency")
        efficiency_btn.clicked.connect(lambda: self.generate_production_report('efficiency'))
        reports_layout.addWidget(efficiency_btn, 0, 0)
        
        # Waste analysis report
        waste_analysis_btn = QPushButton("تحليل الفاقد / Waste Analysis")
        waste_analysis_btn.clicked.connect(lambda: self.generate_production_report('waste_analysis'))
        reports_layout.addWidget(waste_analysis_btn, 0, 1)
        
        # Equipment utilization report
        equipment_util_btn = QPushButton("استخدام المعدات / Equipment Utilization")
        equipment_util_btn.clicked.connect(lambda: self.generate_production_report('equipment_utilization'))
        reports_layout.addWidget(equipment_util_btn, 0, 2)
        
        # Production capacity report
        capacity_btn = QPushButton("الطاقة الإنتاجية / Production Capacity")
        capacity_btn.clicked.connect(lambda: self.generate_production_report('capacity'))
        reports_layout.addWidget(capacity_btn, 1, 0)
        
        # Block to slab conversion report
        conversion_btn = QPushButton("تحويل الكتل للبلاطات / Block to Slab Conversion")
        conversion_btn.clicked.connect(lambda: self.generate_production_report('conversion'))
        reports_layout.addWidget(conversion_btn, 1, 1)
        
        layout.addWidget(reports_group)
        
        # Report display
        self.production_report_display = QTextEdit()
        self.production_report_display.setReadOnly(True)
        self.production_report_display.setFont(QFont("Courier", 10))
        layout.addWidget(self.production_report_display)
        
        return widget
        
    def create_inventory_reports_tab(self):
        """Create inventory reports tab"""
        widget = QWidget()
        layout = QVBoxLayout(widget)
        
        # Report selection
        reports_group = QGroupBox("تقارير المخزون المتاحة / Available Inventory Reports")
        reports_layout = QGridLayout(reports_group)
        
        # Stock levels report
        stock_levels_btn = QPushButton("مستويات المخزون / Stock Levels")
        stock_levels_btn.clicked.connect(lambda: self.generate_inventory_report('stock_levels'))
        reports_layout.addWidget(stock_levels_btn, 0, 0)
        
        # Inventory valuation report
        valuation_btn = QPushButton("تقييم المخزون / Inventory Valuation")
        valuation_btn.clicked.connect(lambda: self.generate_inventory_report('valuation'))
        reports_layout.addWidget(valuation_btn, 0, 1)
        
        # Inventory movement report
        movement_btn = QPushButton("حركة المخزون / Inventory Movement")
        movement_btn.clicked.connect(lambda: self.generate_inventory_report('movement'))
        reports_layout.addWidget(movement_btn, 0, 2)
        
        # Slow moving items report
        slow_moving_btn = QPushButton("البضائع بطيئة الحركة / Slow Moving Items")
        slow_moving_btn.clicked.connect(lambda: self.generate_inventory_report('slow_moving'))
        reports_layout.addWidget(slow_moving_btn, 1, 0)
        
        # Reorder report
        reorder_btn = QPushButton("تقرير إعادة الطلب / Reorder Report")
        reorder_btn.clicked.connect(lambda: self.generate_inventory_report('reorder'))
        reports_layout.addWidget(reorder_btn, 1, 1)
        
        layout.addWidget(reports_group)
        
        # Report display
        self.inventory_report_display = QTextEdit()
        self.inventory_report_display.setReadOnly(True)
        self.inventory_report_display.setFont(QFont("Courier", 10))
        layout.addWidget(self.inventory_report_display)
        
        return widget
        
    def create_quality_reports_tab(self):
        """Create quality reports tab"""
        widget = QWidget()
        layout = QVBoxLayout(widget)
        
        # Report selection
        reports_group = QGroupBox("تقارير الجودة المتاحة / Available Quality Reports")
        reports_layout = QGridLayout(reports_group)
        
        # Quality metrics report
        metrics_btn = QPushButton("مؤشرات الجودة / Quality Metrics")
        metrics_btn.clicked.connect(lambda: self.generate_quality_report('metrics'))
        reports_layout.addWidget(metrics_btn, 0, 0)
        
        # Defects analysis report
        defects_btn = QPushButton("تحليل العيوب / Defects Analysis")
        defects_btn.clicked.connect(lambda: self.generate_quality_report('defects'))
        reports_layout.addWidget(defects_btn, 0, 1)
        
        # Inspector performance report
        inspector_btn = QPushButton("أداء المفتشين / Inspector Performance")
        inspector_btn.clicked.connect(lambda: self.generate_quality_report('inspector_performance'))
        reports_layout.addWidget(inspector_btn, 0, 2)
        
        # Quality trends report
        trends_btn = QPushButton("اتجاهات الجودة / Quality Trends")
        trends_btn.clicked.connect(lambda: self.generate_quality_report('trends'))
        reports_layout.addWidget(trends_btn, 1, 0)
        
        layout.addWidget(reports_group)
        
        # Report display
        self.quality_report_display = QTextEdit()
        self.quality_report_display.setReadOnly(True)
        self.quality_report_display.setFont(QFont("Courier", 10))
        layout.addWidget(self.quality_report_display)
        
        return widget
        
    def create_kpi_dashboard_tab(self):
        """Create KPI dashboard tab"""
        widget = QWidget()
        layout = QVBoxLayout(widget)
        
        # KPI cards
        kpi_layout = QGridLayout()
        
        # Revenue KPI
        revenue_card = self.create_kpi_card("الإيرادات / Revenue", "0", "#28a745", "SAR")
        kpi_layout.addWidget(revenue_card, 0, 0)
        
        # Production KPI
        production_card = self.create_kpi_card("الإنتاج / Production", "0", "#17a2b8", "m²")
        kpi_layout.addWidget(production_card, 0, 1)
        
        # Quality KPI
        quality_card = self.create_kpi_card("الجودة / Quality", "0", "#ffc107", "%")
        kpi_layout.addWidget(quality_card, 0, 2)
        
        # Efficiency KPI
        efficiency_card = self.create_kpi_card("الكفاءة / Efficiency", "0", "#6f42c1", "%")
        kpi_layout.addWidget(efficiency_card, 1, 0)
        
        # Customer satisfaction KPI
        satisfaction_card = self.create_kpi_card("رضا العملاء / Customer Satisfaction", "0", "#e83e8c", "★")
        kpi_layout.addWidget(satisfaction_card, 1, 1)
        
        # Inventory turnover KPI
        turnover_card = self.create_kpi_card("دوران المخزون / Inventory Turnover", "0", "#fd7e14", "x")
        kpi_layout.addWidget(turnover_card, 1, 2)
        
        layout.addLayout(kpi_layout)
        
        # KPI controls
        controls_group = QGroupBox("إعدادات المؤشرات / KPI Settings")
        controls_layout = QFormLayout(controls_group)
        
        self.kpi_period = QComboBox()
        self.kpi_period.addItems([
            "هذا الشهر / This Month", "الشهر الماضي / Last Month",
            "هذا الربع / This Quarter", "هذا العام / This Year"
        ])
        controls_layout.addRow("الفترة / Period:", self.kpi_period)
        
        refresh_kpi_btn = QPushButton("تحديث المؤشرات / Refresh KPIs")
        refresh_kpi_btn.clicked.connect(self.refresh_kpis)
        controls_layout.addRow(refresh_kpi_btn)
        
        layout.addWidget(controls_group)
        
        # KPI trends chart placeholder
        trends_group = QGroupBox("اتجاهات المؤشرات / KPI Trends")
        trends_layout = QVBoxLayout(trends_group)
        
        self.kpi_trends_label = QLabel("سيتم إضافة مخططات الاتجاهات هنا\nKPI trend charts will be added here")
        self.kpi_trends_label.setAlignment(Qt.AlignCenter)
        self.kpi_trends_label.setStyleSheet("background-color: #404040; padding: 50px; border-radius: 8px;")
        trends_layout.addWidget(self.kpi_trends_label)
        
        layout.addWidget(trends_group)
        
        # Load initial KPIs
        self.refresh_kpis()
        
        return widget
        
    def create_custom_reports_tab(self):
        """Create custom reports tab"""
        widget = QWidget()
        layout = QVBoxLayout(widget)
        
        # Report builder
        builder_group = QGroupBox("منشئ التقارير المخصصة / Custom Report Builder")
        builder_layout = QFormLayout(builder_group)
        
        # Report name
        self.custom_report_name = QLineEdit()
        self.custom_report_name.setPlaceholderText("اسم التقرير / Report name")
        builder_layout.addRow("اسم التقرير / Report Name:", self.custom_report_name)
        
        # Data source
        self.custom_data_source = QComboBox()
        self.custom_data_source.addItems([
            "المبيعات / Sales", "المشتريات / Purchases", "المخزون / Inventory",
            "الإنتاج / Production", "الجودة / Quality", "الصيانة / Maintenance",
            "العملاء / Customers", "الموردين / Suppliers"
        ])
        builder_layout.addRow("مصدر البيانات / Data Source:", self.custom_data_source)
        
        # Columns selection
        self.custom_columns = QTextEdit()
        self.custom_columns.setMaximumHeight(60)
        self.custom_columns.setPlaceholderText("الأعمدة المطلوبة (مفصولة بفواصل) / Required columns (comma separated)")
        builder_layout.addRow("الأعمدة / Columns:", self.custom_columns)
        
        # Filters
        self.custom_filters = QTextEdit()
        self.custom_filters.setMaximumHeight(60)
        self.custom_filters.setPlaceholderText("المرشحات (WHERE conditions) / Filters (WHERE conditions)")
        builder_layout.addRow("المرشحات / Filters:", self.custom_filters)
        
        # Group by
        self.custom_group_by = QLineEdit()
        self.custom_group_by.setPlaceholderText("تجميع حسب / Group by")
        builder_layout.addRow("تجميع حسب / Group By:", self.custom_group_by)
        
        # Order by
        self.custom_order_by = QLineEdit()
        self.custom_order_by.setPlaceholderText("ترتيب حسب / Order by")
        builder_layout.addRow("ترتيب حسب / Order By:", self.custom_order_by)
        
        # Generate button
        generate_custom_btn = QPushButton("إنشاء التقرير / Generate Report")
        generate_custom_btn.clicked.connect(self.generate_custom_report)
        builder_layout.addRow(generate_custom_btn)
        
        layout.addWidget(builder_group)
        
        # Custom report display
        self.custom_report_display = QTextEdit()
        self.custom_report_display.setReadOnly(True)
        self.custom_report_display.setFont(QFont("Courier", 10))
        layout.addWidget(self.custom_report_display)
        
        # Save custom report
        save_layout = QHBoxLayout()
        
        save_custom_btn = QPushButton("حفظ التقرير / Save Report")
        save_custom_btn.clicked.connect(self.save_custom_report)
        
        load_custom_btn = QPushButton("تحميل تقرير محفوظ / Load Saved Report")
        load_custom_btn.clicked.connect(self.load_custom_report)
        
        save_layout.addWidget(save_custom_btn)
        save_layout.addWidget(load_custom_btn)
        save_layout.addStretch()
        
        layout.addLayout(save_layout)
        
        return widget
        
    def create_kpi_card(self, title, value, color, unit=""):
        """Create KPI card widget"""
        card = QGroupBox()
        card.setStyleSheet(f"""
            QGroupBox {{
                background-color: {color};
                border-radius: 8px;
                font-weight: bold;
                color: white;
                padding: 15px;
                min-height: 100px;
            }}
        """)
        
        layout = QVBoxLayout(card)
        
        title_label = QLabel(title)
        title_label.setFont(QFont("Arial", 10))
        title_label.setAlignment(Qt.AlignCenter)
        title_label.setWordWrap(True)
        
        value_label = QLabel(f"{value} {unit}")
        value_label.setFont(QFont("Arial", 18, QFont.Bold))
        value_label.setAlignment(Qt.AlignCenter)
        
        layout.addWidget(title_label)
        layout.addWidget(value_label)
        
        return card
        
    def generate_financial_report(self, report_type):
        """Generate financial report"""
        from_date = self.financial_from_date.date().toString("yyyy-MM-dd")
        to_date = self.financial_to_date.date().toString("yyyy-MM-dd")
        
        conn = self.db_manager.get_connection()
        cursor = conn.cursor()
        
        if report_type == 'sales_summary':
            cursor.execute("""
                SELECT 
                    DATE(sale_date) as date,
                    COUNT(*) as transactions,
                    SUM(final_amount) as total_sales,
                    AVG(final_amount) as avg_transaction
                FROM sales
                WHERE sale_date BETWEEN ? AND ?
                GROUP BY DATE(sale_date)
                ORDER BY date DESC
            """, (from_date, to_date))
            
            results = cursor.fetchall()
            
            report_text = f"""
تقرير ملخص المبيعات من {from_date} إلى {to_date}
Sales Summary Report from {from_date} to {to_date}

═══════════════════════════════════════════════════════════════

التاريخ          المعاملات    إجمالي المبيعات    متوسط المعاملة
Date            Transactions  Total Sales      Avg Transaction
─────────────────────────────────────────────────────────────

"""
            
            total_sales = 0
            total_transactions = 0
            
            for result in results:
                report_text += f"{result['date']:<15} {result['transactions']:<12} {result['total_sales']:<15.2f} {result['avg_transaction']:<15.2f}\n"
                total_sales += result['total_sales']
                total_transactions += result['transactions']
                
            report_text += f"""
─────────────────────────────────────────────────────────────
الإجمالي / Total: {total_transactions} معاملة / transactions
إجمالي المبيعات / Total Sales: {total_sales:.2f}
متوسط المعاملة / Average Transaction: {total_sales/max(total_transactions, 1):.2f}
            """
            
        elif report_type == 'profit_loss':
            # Get sales revenue
            cursor.execute("""
                SELECT SUM(final_amount) as revenue
                FROM sales
                WHERE sale_date BETWEEN ? AND ?
            """, (from_date, to_date))
            
            revenue_result = cursor.fetchone()
            revenue = revenue_result['revenue'] or 0
            
            # Get expenses
            cursor.execute("""
                SELECT SUM(amount) as expenses
                FROM expenses
                WHERE expense_date BETWEEN ? AND ?
            """, (from_date, to_date))
            
            expense_result = cursor.fetchone()
            expenses = expense_result['expenses'] or 0
            
            # Get cost of goods sold (simplified)
            cursor.execute("""
                SELECT SUM(total_amount) as cogs
                FROM purchase_orders
                WHERE order_date BETWEEN ? AND ? AND status = 'received'
            """, (from_date, to_date))
            
            cogs_result = cursor.fetchone()
            cogs = cogs_result['cogs'] or 0
            
            gross_profit = revenue - cogs
            net_profit = gross_profit - expenses
            
            report_text = f"""
تقرير الأرباح والخسائر من {from_date} إلى {to_date}
Profit & Loss Report from {from_date} to {to_date}

═══════════════════════════════════════════════════════════════

الإيرادات / Revenue:                    {revenue:>15.2f}
تكلفة البضاعة المباعة / COGS:           {cogs:>15.2f}
                                        ─────────────────
الربح الإجمالي / Gross Profit:          {gross_profit:>15.2f}

المصروفات التشغيلية / Operating Expenses: {expenses:>15.2f}
                                        ─────────────────
صافي الربح / Net Profit:               {net_profit:>15.2f}

هامش الربح الإجمالي / Gross Margin:     {(gross_profit/max(revenue, 1)*100):>14.1f}%
هامش الربح الصافي / Net Margin:        {(net_profit/max(revenue, 1)*100):>14.1f}%
            """
            
        else:
            report_text = f"تقرير {report_type} غير متاح حالياً\nReport {report_type} not available yet"
            
        self.financial_report_display.setPlainText(report_text)
        conn.close()
        
    def generate_production_report(self, report_type):
        """Generate production report"""
        conn = self.db_manager.get_connection()
        cursor = conn.cursor()
        
        if report_type == 'efficiency':
            cursor.execute("""
                SELECT 
                    pp.plan_number,
                    pp.plan_date,
                    COUNT(ppi.id) as planned_items,
                    SUM(ppi.target_slabs) as target_slabs,
                    SUM(ppi.actual_slabs) as actual_slabs,
                    AVG(ppi.estimated_waste) as avg_estimated_waste,
                    AVG(ppi.actual_waste) as avg_actual_waste
                FROM production_plans pp
                LEFT JOIN production_plan_items ppi ON pp.id = ppi.plan_id
                WHERE pp.status = 'completed'
                GROUP BY pp.id
                ORDER BY pp.plan_date DESC
                LIMIT 20
            """)
            
            results = cursor.fetchall()
            
            report_text = """
تقرير كفاءة الإنتاج
Production Efficiency Report

═══════════════════════════════════════════════════════════════

رقم الخطة    التاريخ      المستهدف   الفعلي    الكفاءة%   الفاقد%
Plan No.     Date        Target     Actual    Efficiency  Waste
─────────────────────────────────────────────────────────────

"""
            
            total_target = 0
            total_actual = 0
            
            for result in results:
                target = result['target_slabs'] or 0
                actual = result['actual_slabs'] or 0
                efficiency = (actual / max(target, 1)) * 100 if target > 0 else 0
                waste = result['avg_actual_waste'] or 0
                
                report_text += f"{result['plan_number']:<12} {result['plan_date']:<10} {target:<10} {actual:<9} {efficiency:<10.1f} {waste:<8.1f}\n"
                
                total_target += target
                total_actual += actual
                
            overall_efficiency = (total_actual / max(total_target, 1)) * 100
            
            report_text += f"""
─────────────────────────────────────────────────────────────
الكفاءة الإجمالية / Overall Efficiency: {overall_efficiency:.1f}%
إجمالي المستهدف / Total Target: {total_target}
إجمالي الفعلي / Total Actual: {total_actual}
            """
            
        else:
            report_text = f"تقرير {report_type} غير متاح حالياً\nReport {report_type} not available yet"
            
        self.production_report_display.setPlainText(report_text)
        conn.close()
        
    def generate_inventory_report(self, report_type):
        """Generate inventory report"""
        conn = self.db_manager.get_connection()
        cursor = conn.cursor()
        
        if report_type == 'stock_levels':
            cursor.execute("""
                SELECT 
                    m.name,
                    mc.name as category,
                    m.current_stock,
                    m.reorder_level,
                    m.unit,
                    m.unit_cost,
                    (m.current_stock * m.unit_cost) as total_value
                FROM materials m
                LEFT JOIN material_categories mc ON m.category_id = mc.id
                ORDER BY total_value DESC
            """)
            
            results = cursor.fetchall()
            
            report_text = """
تقرير مستويات المخزون
Stock Levels Report

═══════════════════════════════════════════════════════════════

المادة                الفئة        المخزون    حد الطلب   القيمة
Material             Category     Stock      Reorder    Value
─────────────────────────────────────────────────────────────

"""
            
            total_value = 0
            low_stock_items = 0
            
            for result in results:
                stock = result['current_stock']
                reorder = result['reorder_level']
                value = result['total_value']
                
                status = "منخفض / Low" if stock <= reorder else "طبيعي / Normal"
                if stock <= reorder:
                    low_stock_items += 1
                    
                report_text += f"{result['name']:<20} {(result['category'] or '-'):<12} {stock:<10.1f} {reorder:<10} {value:<10.2f}\n"
                total_value += value
                
            report_text += f"""
─────────────────────────────────────────────────────────────
إجمالي قيمة المخزون / Total Inventory Value: {total_value:.2f}
المواد منخفضة المخزون / Low Stock Items: {low_stock_items}
إجمالي المواد / Total Materials: {len(results)}
            """
            
        else:
            report_text = f"تقرير {report_type} غير متاح حالياً\nReport {report_type} not available yet"
            
        self.inventory_report_display.setPlainText(report_text)
        conn.close()
        
    def generate_quality_report(self, report_type):
        """Generate quality report"""
        conn = self.db_manager.get_connection()
        cursor = conn.cursor()
        
        if report_type == 'metrics':
            cursor.execute("""
                SELECT 
                    COUNT(*) as total_inspections,
                    SUM(CASE WHEN status = 'passed' THEN 1 ELSE 0 END) as passed,
                    SUM(CASE WHEN status = 'failed' THEN 1 ELSE 0 END) as failed,
                    AVG(surface_quality) as avg_surface,
                    AVG(dimensional_accuracy) as avg_dimensional,
                    AVG(color_consistency) as avg_color
                FROM quality_inspections
                WHERE inspection_date >= date('now', '-30 days')
            """)
            
            result = cursor.fetchone()
            
            total = result['total_inspections'] or 0
            passed = result['passed'] or 0
            failed = result['failed'] or 0
            pass_rate = (passed / max(total, 1)) * 100
            
            report_text = f"""
تقرير مؤشرات الجودة (آخر 30 يوم)
Quality Metrics Report (Last 30 Days)

═══════════════════════════════════════════════════════════════

إجمالي الفحوصات / Total Inspections:     {total}
الفحوصات المقبولة / Passed Inspections:   {passed}
الفحوصات المرفوضة / Failed Inspections:   {failed}
معدل النجاح / Pass Rate:                 {pass_rate:.1f}%

متوسط المعايير / Average Criteria Scores:
─────────────────────────────────────────────────────────────
جودة السطح / Surface Quality:           {(result['avg_surface'] or 0):.1f}/5
دقة الأبعاد / Dimensional Accuracy:      {(result['avg_dimensional'] or 0):.1f}/5
ثبات اللون / Color Consistency:         {(result['avg_color'] or 0):.1f}/5

التقييم العام / Overall Assessment:
─────────────────────────────────────────────────────────────
"""
            
            if pass_rate >= 95:
                report_text += "ممتاز / Excellent - الجودة في أعلى مستوياتها\n"
            elif pass_rate >= 90:
                report_text += "جيد جداً / Very Good - جودة عالية\n"
            elif pass_rate >= 80:
                report_text += "جيد / Good - جودة مقبولة\n"
            else:
                report_text += "يحتاج تحسين / Needs Improvement - الجودة تحتاج تطوير\n"
                
        else:
            report_text = f"تقرير {report_type} غير متاح حالياً\nReport {report_type} not available yet"
            
        self.quality_report_display.setPlainText(report_text)
        conn.close()
        
    def refresh_kpis(self):
        """Refresh KPI values"""
        # This would calculate and update KPI values
        # For now, showing placeholder implementation
        pass
        
    def generate_custom_report(self):
        """Generate custom report"""
        report_name = self.custom_report_name.text().strip()
        data_source = self.custom_data_source.currentText()
        columns = self.custom_columns.toPlainText().strip()
        filters = self.custom_filters.toPlainText().strip()
        group_by = self.custom_group_by.text().strip()
        order_by = self.custom_order_by.text().strip()
        
        if not report_name:
            QMessageBox.warning(self, "تحذير / Warning", "يرجى إدخال اسم التقرير\nPlease enter report name")
            return
            
        # Build SQL query based on data source
        table_map = {
            'المبيعات / Sales': 'sales',
            'المشتريات / Purchases': 'purchase_orders',
            'المخزون / Inventory': 'materials',
            'الإنتاج / Production': 'production_plans',
            'الجودة / Quality': 'quality_inspections',
            'الصيانة / Maintenance': 'maintenance_records',
            'العملاء / Customers': 'customers',
            'الموردين / Suppliers': 'suppliers'
        }
        
        table_name = table_map.get(data_source, 'sales')
        
        # Build query
        query = f"SELECT "
        
        if columns:
            query += columns
        else:
            query += "*"
            
        query += f" FROM {table_name}"
        
        if filters:
            query += f" WHERE {filters}"
            
        if group_by:
            query += f" GROUP BY {group_by}"
            
        if order_by:
            query += f" ORDER BY {order_by}"
        else:
            query += " LIMIT 100"
            
        try:
            conn = self.db_manager.get_connection()
            cursor = conn.cursor()
            
            cursor.execute(query)
            results = cursor.fetchall()
            
            if results:
                # Format results
                report_text = f"""
تقرير مخصص: {report_name}
Custom Report: {report_name}

═══════════════════════════════════════════════════════════════

مصدر البيانات / Data Source: {data_source}
عدد السجلات / Record Count: {len(results)}

النتائج / Results:
─────────────────────────────────────────────────────────────

"""
                
                # Add column headers
                if results:
                    headers = list(results[0].keys())
                    header_line = " | ".join(f"{header:<15}" for header in headers)
                    report_text += header_line + "\n"
                    report_text += "─" * len(header_line) + "\n"
                    
                    # Add data rows
                    for result in results[:50]:  # Limit to first 50 rows
                        row_line = " | ".join(f"{str(result[header]):<15}" for header in headers)
                        report_text += row_line + "\n"
                        
                    if len(results) > 50:
                        report_text += f"\n... و {len(results) - 50} سجل آخر / and {len(results) - 50} more records\n"
                        
            else:
                report_text = f"""
تقرير مخصص: {report_name}
Custom Report: {report_name}

═══════════════════════════════════════════════════════════════

لا توجد نتائج للاستعلام المحدد
No results found for the specified query
                """
                
            self.custom_report_display.setPlainText(report_text)
            conn.close()
            
        except Exception as e:
            QMessageBox.critical(self, "خطأ / Error", f"Error generating report: {str(e)}")
            
    def save_custom_report(self):
        """Save custom report configuration"""
        report_name = self.custom_report_name.text().strip()
        
        if not report_name:
            QMessageBox.warning(self, "تحذير / Warning", "يرجى إدخال اسم التقرير\nPlease enter report name")
            return
            
        config = {
            'name': report_name,
            'data_source': self.custom_data_source.currentText(),
            'columns': self.custom_columns.toPlainText(),
            'filters': self.custom_filters.toPlainText(),
            'group_by': self.custom_group_by.text(),
            'order_by': self.custom_order_by.text()
        }
        
        # Save to file
        reports_dir = "custom_reports"
        os.makedirs(reports_dir, exist_ok=True)
        
        filename = f"{reports_dir}/{report_name.replace(' ', '_')}.json"
        
        try:
            with open(filename, 'w', encoding='utf-8') as f:
                json.dump(config, f, ensure_ascii=False, indent=2)
                
            QMessageBox.information(self, "نجح / Success", 
                                  f"تم حفظ التقرير بنجاح\nReport saved successfully\n{filename}")
                                  
        except Exception as e:
            QMessageBox.critical(self, "خطأ / Error", f"Error saving report: {str(e)}")
            
    def load_custom_report(self):
        """Load saved custom report"""
        from PyQt5.QtWidgets import QFileDialog
        
        filename, _ = QFileDialog.getOpenFileName(
            self, "تحميل تقرير مخصص / Load Custom Report",
            "custom_reports", "JSON Files (*.json);;All Files (*)"
        )
        
        if filename:
            try:
                with open(filename, 'r', encoding='utf-8') as f:
                    config = json.load(f)
                    
                self.custom_report_name.setText(config.get('name', ''))
                self.custom_data_source.setCurrentText(config.get('data_source', ''))
                self.custom_columns.setPlainText(config.get('columns', ''))
                self.custom_filters.setPlainText(config.get('filters', ''))
                self.custom_group_by.setText(config.get('group_by', ''))
                self.custom_order_by.setText(config.get('order_by', ''))
                
                QMessageBox.information(self, "نجح / Success", 
                                      "تم تحميل التقرير بنجاح\nReport loaded successfully")
                                      
            except Exception as e:
                QMessageBox.critical(self, "خطأ / Error", f"Error loading report: {str(e)}")
                
    def export_report(self, format_type, content):
        """Export report to file"""
        from PyQt5.QtWidgets import QFileDialog
        
        if format_type == 'pdf':
            filename, _ = QFileDialog.getSaveFileName(
                self, "تصدير PDF / Export PDF", "", "PDF Files (*.pdf)"
            )
            if filename:
                # PDF export would be implemented here
                QMessageBox.information(self, "معلومات / Info", 
                                      "تصدير PDF سيتم تنفيذه قريباً\nPDF export will be implemented soon")
                
        elif format_type == 'excel':
            filename, _ = QFileDialog.getSaveFileName(
                self, "تصدير Excel / Export Excel", "", "Excel Files (*.xlsx)"
            )
            if filename:
                # Excel export would be implemented here
                QMessageBox.information(self, "معلومات / Info", 
                                      "تصدير Excel سيتم تنفيذه قريباً\nExcel export will be implemented soon")
                
    def export_all_reports(self):
        """Export all reports"""
        QMessageBox.information(self, "معلومات / Info", 
                              "تصدير جميع التقارير سيتم تنفيذه قريباً\n"
                              "Export all reports will be implemented soon")
