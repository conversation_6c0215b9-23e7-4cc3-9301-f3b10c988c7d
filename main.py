import sys
import os
from PyQt5.QtWidgets import <PERSON><PERSON><PERSON><PERSON>, QSplashScreen, QLabel, QMainWindow, QStackedWidget
from PyQt5.QtCore import Qt, QTimer
from PyQt5.QtGui import QPixmap, QFont, QIcon
from database.db_manager import DatabaseManager
from ui.login_window import <PERSON>ginWindow
from ui.main_dashboard import MainDashboard
from utils.translator import Translator
from utils.font_manager import FontManager
from utils.locale_manager import LocaleManager
from utils.cultural_manager import CulturalManager

class AlHassanStoneFactory(QMainWindow):
    def __init__(self):
        super().__init__()
        self.app = QApplication(sys.argv)
        self.app.setApplicationName("Al-Hassan Stone Factory Management System")
        self.app.setApplicationVersion("2.0.0")
        
        # Initialize managers
        self.db_manager = DatabaseManager()
        self.translator = Translator()
        self.font_manager = FontManager()
        self.locale_manager = LocaleManager()
        self.cultural_manager = CulturalManager()
        
        # Set application font
        self.font_manager.set_application_font(self.app)
        
        # Initialize database
        self.init_database()
        
        # Show splash screen
        self.show_splash_screen()
        
    def init_database(self):
        """Initialize database and create tables"""
        try:
            self.db_manager.create_tables()
            print("Database initialized successfully")
        except Exception as e:
            print(f"Database initialization error: {e}")
            
    def show_splash_screen(self):
        """Show splash screen"""
        # Create splash screen
        splash_pix = QPixmap(400, 300)
        splash_pix.fill(Qt.white)
        
        splash = QSplashScreen(splash_pix, Qt.WindowStaysOnTopHint)
        
        # Add text to splash screen
        splash.showMessage(
            "Al-Hassan Stone Factory Management System\n"
            "نظام إدارة مصنع الحسن للأحجار\n\n"
            "Loading... جاري التحميل",
            Qt.AlignCenter | Qt.AlignBottom,
            Qt.black
        )
        
        splash.show()
        
        # Process events to show splash screen
        self.app.processEvents()
        
        # Timer to close splash screen and show login
        QTimer.singleShot(3000, lambda: self.show_login(splash))
        
    def show_login(self, splash):
        """Show login window"""
        splash.close()
        
        self.login_window = LoginWindow(self.db_manager, self.translator, 
                                      self.font_manager, self.locale_manager,
                                      self.cultural_manager)
        self.login_window.login_successful.connect(self.show_main_dashboard)
        self.login_window.show()
        
    def show_main_dashboard(self, user_data):
        """Show main dashboard"""
        self.login_window.close()
        
        self.main_dashboard = MainDashboard(
            self.db_manager, user_data, self.translator,
            self.font_manager, self.locale_manager, self.cultural_manager
        )
        self.main_dashboard.show()
        
    def init_ui(self):
        self.setWindowTitle("نظام إدارة مصنع الحسن ستون - Al-Hassan Stone Factory")
        self.setGeometry(100, 100, 1400, 900)
        self.setMinimumSize(1200, 800)
        
        # Set application icon
        self.setWindowIcon(QIcon('assets/icon.png'))
        
        # Create stacked widget for different windows
        self.stacked_widget = QStackedWidget()
        self.setCentralWidget(self.stacked_widget)
        
        # Initialize login window
        self.login_window = LoginWindow(self.db_manager, self.translator, 
                                      self.font_manager, self.locale_manager,
                                      self.cultural_manager)
        self.stacked_widget.addWidget(self.login_window)
        
        # Apply stylesheet
        self.apply_stylesheet()
        
    def apply_stylesheet(self):
        """Apply modern dark theme stylesheet"""
        style = """
        QMainWindow {
            background-color: #2b2b2b;
            color: #ffffff;
        }
        
        QWidget {
            background-color: #2b2b2b;
            color: #ffffff;
            font-family: 'Segoe UI', Arial, sans-serif;
            font-size: 10pt;
        }
        
        QPushButton {
            background-color: #0078d4;
            border: none;
            padding: 8px 16px;
            border-radius: 4px;
            color: white;
            font-weight: bold;
        }
        
        QPushButton:hover {
            background-color: #106ebe;
        }
        
        QPushButton:pressed {
            background-color: #005a9e;
        }
        
        QLineEdit, QTextEdit, QComboBox, QSpinBox, QDoubleSpinBox {
            background-color: #404040;
            border: 1px solid #555555;
            padding: 6px;
            border-radius: 4px;
            color: white;
        }
        
        QLineEdit:focus, QTextEdit:focus, QComboBox:focus {
            border: 2px solid #0078d4;
        }
        
        QTableWidget {
            background-color: #353535;
            alternate-background-color: #404040;
            gridline-color: #555555;
            selection-background-color: #0078d4;
        }
        
        QHeaderView::section {
            background-color: #404040;
            padding: 8px;
            border: 1px solid #555555;
            font-weight: bold;
        }
        
        QTabWidget::pane {
            border: 1px solid #555555;
            background-color: #353535;
        }
        
        QTabBar::tab {
            background-color: #404040;
            padding: 8px 16px;
            margin-right: 2px;
            border-top-left-radius: 4px;
            border-top-right-radius: 4px;
        }
        
        QTabBar::tab:selected {
            background-color: #0078d4;
        }
        
        QMenuBar {
            background-color: #404040;
            border-bottom: 1px solid #555555;
        }
        
        QMenuBar::item {
            padding: 8px 12px;
        }
        
        QMenuBar::item:selected {
            background-color: #0078d4;
        }
        
        QMenu {
            background-color: #404040;
            border: 1px solid #555555;
        }
        
        QMenu::item {
            padding: 6px 20px;
        }
        
        QMenu::item:selected {
            background-color: #0078d4;
        }
        
        QGroupBox {
            font-weight: bold;
            border: 2px solid #555555;
            border-radius: 5px;
            margin-top: 1ex;
            padding-top: 10px;
        }
        
        QGroupBox::title {
            subcontrol-origin: margin;
            left: 10px;
            padding: 0 5px 0 5px;
        }
        """
        self.setStyleSheet(style)
        
    def run(self):
        """Run the application"""
        self.init_ui()
        return self.app.exec_()

def main():
    """Main function"""
    # Set up application paths
    if hasattr(sys, '_MEIPASS'):
        # Running as compiled executable
        os.chdir(sys._MEIPASS)
    
    # Create and run application
    app = AlHassanStoneFactory()
    sys.exit(app.run())

if __name__ == "__main__":
    main()
