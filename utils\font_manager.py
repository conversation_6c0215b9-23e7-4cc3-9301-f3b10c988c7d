from PyQt5.QtGui import QFont, QFontDatabase
from PyQt5.QtCore import QLocale
import os

class FontManager:
    def __init__(self):
        self.font_database = None
        self._initialized = False
        
    def _ensure_initialized(self):
        """Ensure font database is initialized"""
        if not self._initialized:
            self.font_database = QFontDatabase()
            self.load_custom_fonts()
            self._initialized = True

    def load_custom_fonts(self):
        """Load custom fonts for different languages"""
        if self.font_database is None:
            return

        fonts_dir = "assets/fonts"
        if os.path.exists(fonts_dir):
            for font_file in os.listdir(fonts_dir):
                if font_file.endswith(('.ttf', '.otf')):
                    font_path = os.path.join(fonts_dir, font_file)
                    self.font_database.addApplicationFont(font_path)
                    
    def get_font_for_language(self, language_code, size=10, weight=QFont.Normal):
        """Get appropriate font for language"""
        self._ensure_initialized()
        font_families = {
            'ar': ['Tahoma', 'Arial Unicode MS', 'Arial'],
            'ur': ['Tahoma', 'Arial Unicode MS', 'Arial'],
            'fa': ['Tahoma', 'Arial Unicode MS', 'Arial'],
            'he': ['Tahoma', 'Arial Unicode MS', 'Arial'],
            'zh': ['Microsoft YaHei', 'SimHei', 'Arial Unicode MS'],
            'ja': ['Meiryo', 'MS Gothic', 'Arial Unicode MS'],
            'ko': ['Malgun Gothic', 'Gulim', 'Arial Unicode MS'],
            'th': ['Tahoma', 'Arial Unicode MS', 'Arial'],
            'hi': ['Mangal', 'Arial Unicode MS', 'Arial'],
            'default': ['Segoe UI', 'Arial', 'sans-serif']
        }
        
        families = font_families.get(language_code, font_families['default'])
        
        for family in families:
            if self.font_database.hasFamily(family):
                return QFont(family, size, weight)
                
        # Fallback to system default
        return QFont("Arial", size, weight)
        
    def get_ui_font(self, language_code, size=10):
        """Get UI font for language"""
        return self.get_font_for_language(language_code, size, QFont.Normal)
        
    def get_title_font(self, language_code, size=14):
        """Get title font for language"""
        return self.get_font_for_language(language_code, size, QFont.Bold)
        
    def get_button_font(self, language_code, size=10):
        """Get button font for language"""
        return self.get_font_for_language(language_code, size, QFont.Medium)

# Global font manager instance will be created after QApplication
