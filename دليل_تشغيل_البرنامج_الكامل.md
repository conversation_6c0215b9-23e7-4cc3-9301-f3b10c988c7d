# دليل تشغيل نظام إدارة مصنع الحسن ستون - الكامل

## 🚀 تشغيل البرنامج

### الطريقة الأولى: تشغيل مباشر
```bash
python main.py
```

### الطريقة الثانية: استخدام الملف المساعد
```bash
.\run_app.bat
```

## 🔐 تسجيل الدخول

### بيانات المدير الافتراضية:
- **اسم المستخدم:** admin
- **كلمة المرور:** admin123

### إنشاء مستخدم جديد:
1. سجل دخول كمدير
2. اذهب إلى "إدارة المستخدمين"
3. انقر "إضافة مستخدم جديد"
4. املأ البيانات المطلوبة

## 📊 الوحدات الرئيسية

### 1. إدارة العملاء (Customers Module)
- **إضافة عملاء جدد**
- **تعديل بيانات العملاء**
- **البحث والفلترة**
- **عرض تاريخ المعاملات**

**كيفية الاستخدام:**
1. انقر على "العملاء" في القائمة الجانبية
2. انقر "إضافة عميل جديد"
3. املأ البيانات: الاسم، الهاتف، العنوان، البريد الإلكتروني
4. احفظ البيانات

### 2. إدارة الشاحنات (Trucks Module)
- **تسجيل الشاحنات**
- **متابعة حالة الشاحنات**
- **إدارة السائقين**
- **تتبع الرحلات**

**كيفية الاستخدام:**
1. اذهب إلى "الشاحنات"
2. سجل شاحنة جديدة برقم اللوحة
3. أضف بيانات السائق
4. تتبع الرحلات والحمولات

### 3. إدارة الكتل الحجرية (Blocks Module)
- **تسجيل الكتل الحجرية**
- **تصنيف حسب النوع والحجم**
- **متابعة المخزون**
- **تسعير الكتل**

**كيفية الاستخدام:**
1. انقر "الكتل الحجرية"
2. أضف كتلة جديدة
3. حدد النوع والأبعاد
4. سجل السعر والموقع

### 4. إدارة المبيعات (Sales Module)
- **إنشاء فواتير المبيعات**
- **متابعة الطلبات**
- **إدارة المدفوعات**
- **تقارير المبيعات**

**كيفية الاستخدام:**
1. اذهب إلى "المبيعات"
2. انقر "فاتورة جديدة"
3. اختر العميل والمنتجات
4. احسب الإجمالي واطبع الفاتورة

### 5. إدارة المخزون (Inventory Module)
- **متابعة مستويات المخزون**
- **تنبيهات نفاد المخزون**
- **حركات الدخول والخروج**
- **تقييم المخزون**

### 6. إدارة المصروفات (Expenses Module)
- **تسجيل المصروفات اليومية**
- **تصنيف المصروفات**
- **إرفاق الإيصالات**
- **تقارير المصروفات**

### 7. التقارير (Reports Module)
- **تقارير المبيعات**
- **تقارير المصروفات**
- **تقارير المخزون**
- **تقارير الأرباح والخسائر**

### 8. تخطيط الإنتاج (Production Planning)
- **جدولة الإنتاج**
- **متابعة الطلبات**
- **تخصيص الموارد**
- **تتبع التقدم**

### 9. شراء المواد (Material Procurement)
- **طلبات الشراء**
- **إدارة الموردين**
- **متابعة التسليم**
- **فواتير الشراء**

### 10. مراقبة الجودة (Quality Control)
- **فحص المنتجات**
- **معايير الجودة**
- **تقارير الجودة**
- **إجراءات التصحيح**

## 🔧 الإعدادات والتخصيص

### إعدادات النظام:
1. اذهب إلى "الإعدادات"
2. اختر اللغة (عربي/إنجليزي)
3. حدد العملة الافتراضية
4. اضبط إعدادات الطباعة

### إعدادات قاعدة البيانات:
1. انقر "إعدادات قاعدة البيانات"
2. اختر مسار حفظ البيانات
3. اضبط إعدادات النسخ الاحتياطي
4. فعل التشفير للبيانات الحساسة

## 💾 النسخ الاحتياطي والاستعادة

### إنشاء نسخة احتياطية:
1. اذهب إلى "النسخ الاحتياطي"
2. انقر "إنشاء نسخة احتياطية"
3. اختر مكان الحفظ
4. انتظر اكتمال العملية

### استعادة النسخة الاحتياطية:
1. انقر "استعادة نسخة احتياطية"
2. اختر ملف النسخة الاحتياطية
3. تأكيد الاستعادة
4. إعادة تشغيل البرنامج

## 📱 الميزات المتقدمة

### 1. نظام التنبيهات:
- تنبيهات نفاد المخزون
- تذكير بالمواعيد المهمة
- تنبيهات الدفعات المستحقة

### 2. التقارير المتقدمة:
- تقارير مخصصة
- رسوم بيانية تفاعلية
- تصدير للـ Excel/PDF

### 3. إدارة المستخدمين:
- صلاحيات متدرجة
- تتبع نشاط المستخدمين
- أمان متقدم

## 🔍 استكشاف الأخطاء

### مشاكل شائعة وحلولها:

#### 1. البرنامج لا يبدأ:
```bash
# تحقق من تثبيت المتطلبات
pip install -r requirements.txt

# تشغيل مع عرض الأخطاء
python -u main.py
```

#### 2. خطأ في قاعدة البيانات:
- تأكد من وجود مجلد `data`
- تحقق من صلاحيات الكتابة
- أعد إنشاء قاعدة البيانات

#### 3. مشاكل الطباعة:
- تحقق من إعدادات الطابعة
- تأكد من تثبيت تعريفات الطابعة
- جرب طباعة تجريبية

## 📞 الدعم الفني

### للحصول على المساعدة:
1. راجع هذا الدليل أولاً
2. تحقق من ملف `EXECUTABLE_INSTRUCTIONS.md`
3. ابحث في ملفات السجل للأخطاء
4. تواصل مع فريق الدعم

## 🎯 نصائح للاستخدام الأمثل

1. **قم بعمل نسخة احتياطية يومياً**
2. **حدث بيانات العملاء بانتظام**
3. **راجع التقارير أسبوعياً**
4. **تدرب على جميع الوحدات**
5. **استخدم البحث السريع للوصول للبيانات**

---

## ✅ البرنامج جاهز للاستخدام الكامل!

جميع الوحدات تعمل بشكل صحيح ويمكنك البدء في استخدام النظام لإدارة مصنع الحجر بكفاءة عالية.
