from PyQt5.QtWidgets import (QW<PERSON>t, QVBoxLayout, QHBoxLayout, QLabel,
                            QProgressBar, QGroupBox, QGridLayout, QTextEdit,
                            QPushButton, QTableWidget, QTableWidgetItem,
                            QHeaderView, QTabWidget, QFrame, QScrollArea)
from PyQt5.QtCore import Qt, QTimer, pyqtSignal, QThread
from PyQt5.QtGui import QFont, QColor
import psutil
import sqlite3
import os
from datetime import datetime, timedelta
import json

class SystemMonitorModule(QWidget):
    """System monitoring and performance module"""
    
    def __init__(self, db_manager, user_data):
        super().__init__()
        self.db_manager = db_manager
        self.user_data = user_data
        self.init_ui()
        self.setup_monitoring()
        
    def init_ui(self):
        """Initialize the system monitor interface"""
        layout = QVBoxLayout(self)
        
        # Header
        header_label = QLabel("مراقب النظام / System Monitor")
        header_label.setFont(QFont("Arial", 16, QFont.Bold))
        header_label.setStyleSheet("color: #0078d4; margin-bottom: 20px;")
        header_label.setAlignment(Qt.AlignCenter)
        layout.addWidget(header_label)
        
        # Create tabs
        self.tab_widget = QTabWidget()
        
        # System Performance tab
        performance_tab = self.create_performance_tab()
        self.tab_widget.addTab(performance_tab, "الأداء / Performance")
        
        # Database Monitor tab
        database_tab = self.create_database_tab()
        self.tab_widget.addTab(database_tab, "قاعدة البيانات / Database")
        
        # Process Monitor tab
        process_tab = self.create_process_tab()
        self.tab_widget.addTab(process_tab, "العمليات / Processes")
        
        # System Logs tab
        logs_tab = self.create_logs_tab()
        self.tab_widget.addTab(logs_tab, "السجلات / Logs")
        
        layout.addWidget(self.tab_widget)
        
        # Control buttons
        controls_layout = QHBoxLayout()
        
        self.refresh_btn = QPushButton("تحديث / Refresh")
        self.refresh_btn.clicked.connect(self.refresh_all_data)
        controls_layout.addWidget(self.refresh_btn)
        
        self.export_btn = QPushButton("تصدير التقرير / Export Report")
        self.export_btn.clicked.connect(self.export_system_report)
        controls_layout.addWidget(self.export_btn)
        
        controls_layout.addStretch()
        
        self.auto_refresh_btn = QPushButton("تحديث تلقائي / Auto Refresh")
        self.auto_refresh_btn.setCheckable(True)
        self.auto_refresh_btn.toggled.connect(self.toggle_auto_refresh)
        controls_layout.addWidget(self.auto_refresh_btn)
        
        layout.addLayout(controls_layout)
        
        # Apply styling
        self.apply_monitor_style()
        
    def create_performance_tab(self):
        """Create system performance monitoring tab"""
        widget = QWidget()
        layout = QVBoxLayout(widget)
        
        # System Overview
        overview_group = QGroupBox("نظرة عامة على النظام / System Overview")
        overview_layout = QGridLayout(overview_group)
        
        # CPU Usage
        cpu_label = QLabel("استخدام المعالج / CPU Usage:")
        overview_layout.addWidget(cpu_label, 0, 0)
        
        self.cpu_progress = QProgressBar()
        self.cpu_progress.setStyleSheet("""
            QProgressBar {
                border: 2px solid #555555;
                border-radius: 5px;
                text-align: center;
                background-color: #404040;
            }
            QProgressBar::chunk {
                background-color: #0078d4;
                border-radius: 3px;
            }
        """)
        overview_layout.addWidget(self.cpu_progress, 0, 1)
        
        self.cpu_value_label = QLabel("0%")
        overview_layout.addWidget(self.cpu_value_label, 0, 2)
        
        # Memory Usage
        memory_label = QLabel("استخدام الذاكرة / Memory Usage:")
        overview_layout.addWidget(memory_label, 1, 0)
        
        self.memory_progress = QProgressBar()
        self.memory_progress.setStyleSheet("""
            QProgressBar::chunk {
                background-color: #28a745;
            }
        """)
        overview_layout.addWidget(self.memory_progress, 1, 1)
        
        self.memory_value_label = QLabel("0 MB / 0 MB")
        overview_layout.addWidget(self.memory_value_label, 1, 2)
        
        # Disk Usage
        disk_label = QLabel("استخدام القرص / Disk Usage:")
        overview_layout.addWidget(disk_label, 2, 0)
        
        self.disk_progress = QProgressBar()
        self.disk_progress.setStyleSheet("""
            QProgressBar::chunk {
                background-color: #ffc107;
            }
        """)
        overview_layout.addWidget(self.disk_progress, 2, 1)
        
        self.disk_value_label = QLabel("0 GB / 0 GB")
        overview_layout.addWidget(self.disk_value_label, 2, 2)
        
        # Network Usage
        network_label = QLabel("استخدام الشبكة / Network Usage:")
        overview_layout.addWidget(network_label, 3, 0)
        
        self.network_value_label = QLabel("↑ 0 KB/s ↓ 0 KB/s")
        overview_layout.addWidget(self.network_value_label, 3, 1, 1, 2)
        
        layout.addWidget(overview_group)
        
        # System Information
        info_group = QGroupBox("معلومات النظام / System Information")
        info_layout = QGridLayout(info_group)
        
        self.system_info_labels = {}
        
        info_items = [
            ("نظام التشغيل / OS", "os"),
            ("المعالج / Processor", "cpu"),
            ("إجمالي الذاكرة / Total RAM", "total_ram"),
            ("وقت التشغيل / Uptime", "uptime"),
            ("درجة الحرارة / Temperature", "temperature")
        ]
        
        for i, (label_text, key) in enumerate(info_items):
            label = QLabel(label_text + ":")
            info_layout.addWidget(label, i, 0)
            
            value_label = QLabel("جاري التحميل... / Loading...")
            self.system_info_labels[key] = value_label
            info_layout.addWidget(value_label, i, 1)
            
        layout.addWidget(info_group)
        
        return widget
        
    def create_database_tab(self):
        """Create database monitoring tab"""
        widget = QWidget()
        layout = QVBoxLayout(widget)
        
        # Database Statistics
        stats_group = QGroupBox("إحصائيات قاعدة البيانات / Database Statistics")
        stats_layout = QGridLayout(stats_group)
        
        self.db_stats_labels = {}
        
        stats_items = [
            ("حجم قاعدة البيانات / Database Size", "db_size"),
            ("عدد الجداول / Tables Count", "tables_count"),
            ("إجمالي السجلات / Total Records", "total_records"),
            ("آخر نسخة احتياطية / Last Backup", "last_backup"),
            ("الاتصالات النشطة / Active Connections", "active_connections")
        ]
        
        for i, (label_text, key) in enumerate(stats_items):
            label = QLabel(label_text + ":")
            stats_layout.addWidget(label, i, 0)
            
            value_label = QLabel("جاري التحميل... / Loading...")
            self.db_stats_labels[key] = value_label
            stats_layout.addWidget(value_label, i, 1)
            
        layout.addWidget(stats_group)
        
        # Database Performance
        performance_group = QGroupBox("أداء قاعدة البيانات / Database Performance")
        performance_layout = QVBoxLayout(performance_group)
        
        # Query performance table
        self.query_table = QTableWidget()
        self.query_table.setColumnCount(4)
        self.query_table.setHorizontalHeaderLabels([
            "الاستعلام / Query", "الوقت / Time (ms)", 
            "التكرار / Count", "آخر تنفيذ / Last Executed"
        ])
        self.query_table.horizontalHeader().setStretchLastSection(True)
        performance_layout.addWidget(self.query_table)
        
        layout.addWidget(performance_group)
        
        return widget
        
    def create_process_tab(self):
        """Create process monitoring tab"""
        widget = QWidget()
        layout = QVBoxLayout(widget)
        
        # Process table
        self.process_table = QTableWidget()
        self.process_table.setColumnCount(6)
        self.process_table.setHorizontalHeaderLabels([
            "PID", "الاسم / Name", "المعالج / CPU %", 
            "الذاكرة / Memory", "الحالة / Status", "بدء التشغيل / Started"
        ])
        
        # Set column widths
        header = self.process_table.horizontalHeader()
        header.setStretchLastSection(True)
        header.resizeSection(0, 80)
        header.resizeSection(2, 80)
        header.resizeSection(3, 100)
        header.resizeSection(4, 80)
        
        layout.addWidget(self.process_table)
        
        # Process controls
        controls_layout = QHBoxLayout()
        
        self.kill_process_btn = QPushButton("إنهاء العملية / Kill Process")
        self.kill_process_btn.clicked.connect(self.kill_selected_process)
        self.kill_process_btn.setStyleSheet("background-color: #dc3545;")
        controls_layout.addWidget(self.kill_process_btn)
        
        controls_layout.addStretch()
        
        self.refresh_processes_btn = QPushButton("تحديث العمليات / Refresh Processes")
        self.refresh_processes_btn.clicked.connect(self.refresh_processes)
        controls_layout.addWidget(self.refresh_processes_btn)
        
        layout.addLayout(controls_layout)
        
        return widget
        
    def create_logs_tab(self):
        """Create system logs tab"""
        widget = QWidget()
        layout = QVBoxLayout(widget)
        
        # Log filters
        filters_layout = QHBoxLayout()
        
        filters_layout.addWidget(QLabel("مستوى السجل / Log Level:"))
        
        self.log_level_combo = QComboBox()
        self.log_level_combo.addItems(["الكل / All", "خطأ / Error", "تحذير / Warning", "معلومات / Info"])
        filters_layout.addWidget(self.log_level_combo)
        
        filters_layout.addStretch()
        
        self.clear_logs_btn = QPushButton("مسح السجلات / Clear Logs")
        self.clear_logs_btn.clicked.connect(self.clear_logs)
        filters_layout.addWidget(self.clear_logs_btn)
        
        layout.addLayout(filters_layout)
        
        # Logs display
        self.logs_text = QTextEdit()
        self.logs_text.setReadOnly(True)
        self.logs_text.setFont(QFont("Consolas", 9))
        self.logs_text.setStyleSheet("""
            QTextEdit {
                background-color: #1e1e1e;
                color: #ffffff;
                border: 1px solid #555555;
                border-radius: 4px;
            }
        """)
        layout.addWidget(self.logs_text)
        
        return widget
        
    def setup_monitoring(self):
        """Setup monitoring timers and initial data"""
        # Create timer for regular updates
        self.monitor_timer = QTimer()
        self.monitor_timer.timeout.connect(self.update_system_data)
        
        # Initial data load
        self.update_system_data()
        self.load_database_stats()
        self.refresh_processes()
        self.load_system_logs()
        
    def update_system_data(self):
        """Update system performance data"""
        try:
            # CPU Usage
            cpu_percent = psutil.cpu_percent(interval=1)
            self.cpu_progress.setValue(int(cpu_percent))
            self.cpu_value_label.setText(f"{cpu_percent:.1f}%")
            
            # Memory Usage
            memory = psutil.virtual_memory()
            memory_percent = memory.percent
            memory_used_gb = memory.used / (1024**3)
            memory_total_gb = memory.total / (1024**3)
            
            self.memory_progress.setValue(int(memory_percent))
            self.memory_value_label.setText(f"{memory_used_gb:.1f} GB / {memory_total_gb:.1f} GB")
            
            # Disk Usage
            disk = psutil.disk_usage('/')
            disk_percent = (disk.used / disk.total) * 100
            disk_used_gb = disk.used / (1024**3)
            disk_total_gb = disk.total / (1024**3)
            
            self.disk_progress.setValue(int(disk_percent))
            self.disk_value_label.setText(f"{disk_used_gb:.1f} GB / {disk_total_gb:.1f} GB")
            
            # Network Usage
            network = psutil.net_io_counters()
            if hasattr(self, 'prev_network'):
                bytes_sent = network.bytes_sent - self.prev_network.bytes_sent
                bytes_recv = network.bytes_recv - self.prev_network.bytes_recv
                
                kb_sent = bytes_sent / 1024
                kb_recv = bytes_recv / 1024
                
                self.network_value_label.setText(f"↑ {kb_sent:.1f} KB/s ↓ {kb_recv:.1f} KB/s")
            
            self.prev_network = network
            
            # System Information
            import platform
            
            self.system_info_labels['os'].setText(f"{platform.system()} {platform.release()}")
            self.system_info_labels['cpu'].setText(platform.processor()[:50] + "...")
            self.system_info_labels['total_ram'].setText(f"{memory_total_gb:.1f} GB")
            
            # Uptime
            boot_time = datetime.fromtimestamp(psutil.boot_time())
            uptime = datetime.now() - boot_time
            uptime_str = str(uptime).split('.')[0]  # Remove microseconds
            self.system_info_labels['uptime'].setText(uptime_str)
            
            # Temperature (if available)
            try:
                temps = psutil.sensors_temperatures()
                if temps:
                    temp_info = list(temps.values())[0][0]
                    self.system_info_labels['temperature'].setText(f"{temp_info.current}°C")
                else:
                    self.system_info_labels['temperature'].setText("غير متاح / N/A")
            except:
                self.system_info_labels['temperature'].setText("غير متاح / N/A")
                
        except Exception as e:
            print(f"Error updating system data: {e}")
            
    def load_database_stats(self):
        """Load database statistics"""
        try:
            db_path = self.db_manager.db_path
            
            # Database size
            if os.path.exists(db_path):
                db_size = os.path.getsize(db_path)
                db_size_mb = db_size / (1024 * 1024)
                self.db_stats_labels['db_size'].setText(f"{db_size_mb:.2f} MB")
            else:
                self.db_stats_labels['db_size'].setText("غير موجود / Not Found")
                
            # Database statistics
            conn = self.db_manager.get_connection()
            cursor = conn.cursor()
            
            # Tables count
            cursor.execute("SELECT COUNT(*) FROM sqlite_master WHERE type='table'")
            tables_count = cursor.fetchone()[0]
            self.db_stats_labels['tables_count'].setText(str(tables_count))
            
            # Total records (approximate)
            cursor.execute("SELECT SUM(seq) FROM sqlite_sequence")
            result = cursor.fetchone()
            total_records = result[0] if result and result[0] else 0
            self.db_stats_labels['total_records'].setText(f"{total_records:,}")
            
            # Last backup
            cursor.execute("""
                SELECT created_at FROM backup_logs 
                WHERE status = 'success' 
                ORDER BY created_at DESC LIMIT 1
            """)
            result = cursor.fetchone()
            if result:
                self.db_stats_labels['last_backup'].setText(result[0])
            else:
                self.db_stats_labels['last_backup'].setText("لا يوجد / None")
                
            # Active connections (always 1 for SQLite)
            self.db_stats_labels['active_connections'].setText("1")
            
            conn.close()
            
        except Exception as e:
            print(f"Error loading database stats: {e}")
            for label in self.db_stats_labels.values():
                label.setText("خطأ / Error")
                
    def refresh_processes(self):
        """Refresh process list"""
        try:
            processes = []
            for proc in psutil.process_iter(['pid', 'name', 'cpu_percent', 'memory_info', 'status', 'create_time']):
                try:
                    processes.append(proc.info)
                except (psutil.NoSuchProcess, psutil.AccessDenied):
                    continue
                    
            # Sort by CPU usage
            processes.sort(key=lambda x: x['cpu_percent'] or 0, reverse=True)
            
            # Update table
            self.process_table.setRowCount(len(processes[:50]))  # Show top 50 processes
            
            for i, proc in enumerate(processes[:50]):
                self.process_table.setItem(i, 0, QTableWidgetItem(str(proc['pid'])))
                self.process_table.setItem(i, 1, QTableWidgetItem(proc['name'] or 'N/A'))
                self.process_table.setItem(i, 2, QTableWidgetItem(f"{proc['cpu_percent'] or 0:.1f}%"))
                
                memory_mb = (proc['memory_info'].rss / (1024 * 1024)) if proc['memory_info'] else 0
                self.process_table.setItem(i, 3, QTableWidgetItem(f"{memory_mb:.1f} MB"))
                
                self.process_table.setItem(i, 4, QTableWidgetItem(proc['status'] or 'Unknown'))
                
                if proc['create_time']:
                    start_time = datetime.fromtimestamp(proc['create_time']).strftime('%H:%M:%S')
                    self.process_table.setItem(i, 5, QTableWidgetItem(start_time))
                else:
                    self.process_table.setItem(i, 5, QTableWidgetItem('N/A'))
                    
        except Exception as e:
            print(f"Error refreshing processes: {e}")
            
    def load_system_logs(self):
        """Load system logs"""
        try:
            conn = self.db_manager.get_connection()
            cursor = conn.cursor()
            
            cursor.execute("""
                SELECT timestamp, action, u.full_name
                FROM activity_logs al
                LEFT JOIN users u ON al.user_id = u.id
                ORDER BY timestamp DESC
                LIMIT 100
            """)
            
            logs = cursor.fetchall()
            
            log_text = ""
            for log in logs:
                timestamp = log[0]
                action = log[1]
                user = log[2] or 'System'
                
                log_text += f"[{timestamp}] {user}: {action}\n"
                
            self.logs_text.setPlainText(log_text)
            conn.close()
            
        except Exception as e:
            self.logs_text.setPlainText(f"Error loading logs: {e}")
            
    def kill_selected_process(self):
        """Kill selected process"""
        current_row = self.process_table.currentRow()
        if current_row >= 0:
            pid_item = self.process_table.item(current_row, 0)
            if pid_item:
                pid = int(pid_item.text())
                
                try:
                    proc = psutil.Process(pid)
                    proc.terminate()
                    
                    # Refresh process list
                    self.refresh_processes()
                    
                except (psutil.NoSuchProcess, psutil.AccessDenied) as e:
                    from PyQt5.QtWidgets import QMessageBox
                    QMessageBox.warning(self, "تحذير / Warning", 
                                      f"فشل في إنهاء العملية\nFailed to kill process: {e}")
                                      
    def clear_logs(self):
        """Clear system logs"""
        from PyQt5.QtWidgets import QMessageBox
        
        reply = QMessageBox.question(
            self, "مسح السجلات / Clear Logs",
            "هل تريد مسح جميع السجلات؟\nDo you want to clear all logs?",
            QMessageBox.Yes | QMessageBox.No,
            QMessageBox.No
        )
        
        if reply == QMessageBox.Yes:
            try:
                conn = self.db_manager.get_connection()
                cursor = conn.cursor()
                cursor.execute("DELETE FROM activity_logs")
                conn.commit()
                conn.close()
                
                self.logs_text.clear()
                
                QMessageBox.information(self, "نجح / Success", 
                                      "تم مسح السجلات بنجاح\nLogs cleared successfully")
                                      
            except Exception as e:
                QMessageBox.critical(self, "خطأ / Error", 
                                   f"فشل في مسح السجلات\nFailed to clear logs: {e}")
                                   
    def toggle_auto_refresh(self, enabled):
        """Toggle auto refresh"""
        if enabled:
            self.monitor_timer.start(5000)  # Update every 5 seconds
            self.auto_refresh_btn.setText("إيقاف التحديث / Stop Auto Refresh")
        else:
            self.monitor_timer.stop()
            self.auto_refresh_btn.setText("تحديث تلقائي / Auto Refresh")
            
    def refresh_all_data(self):
        """Refresh all monitoring data"""
        self.update_system_data()
        self.load_database_stats()
        self.refresh_processes()
        self.load_system_logs()
        
    def export_system_report(self):
        """Export system monitoring report"""
        from PyQt5.QtWidgets import QFileDialog
        
        file_path, _ = QFileDialog.getSaveFileName(
            self, "تصدير تقرير النظام / Export System Report",
            f"system_report_{datetime.now().strftime('%Y%m%d_%H%M%S')}.txt",
            "Text Files (*.txt)"
        )
        
        if file_path:
            try:
                report_content = self.generate_system_report()
                
                with open(file_path, 'w', encoding='utf-8') as f:
                    f.write(report_content)
                    
                from PyQt5.QtWidgets import QMessageBox
                QMessageBox.information(self, "نجح / Success", 
                                      f"تم تصدير التقرير بنجاح\nReport exported successfully to:\n{file_path}")
                                      
            except Exception as e:
                from PyQt5.QtWidgets import QMessageBox
                QMessageBox.critical(self, "خطأ / Error", 
                                   f"فشل في تصدير التقرير\nFailed to export report: {e}")
                                   
    def generate_system_report(self):
        """Generate comprehensive system report"""
        report = f"""
تقرير مراقبة النظام / System Monitoring Report
تاريخ التقرير / Report Date: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}

=== معلومات النظام / System Information ===
نظام التشغيل / Operating System: {self.system_info_labels['os'].text()}
المعالج / Processor: {self.system_info_labels['cpu'].text()}
إجمالي الذاكرة / Total RAM: {self.system_info_labels['total_ram'].text()}
وقت التشغيل / Uptime: {self.system_info_labels['uptime'].text()}
درجة الحرارة / Temperature: {self.system_info_labels['temperature'].text()}

=== الأداء الحالي / Current Performance ===
استخدام المعالج / CPU Usage: {self.cpu_value_label.text()}
استخدام الذاكرة / Memory Usage: {self.memory_value_label.text()}
استخدام القرص / Disk Usage: {self.disk_value_label.text()}
استخدام الشبكة / Network Usage: {self.network_value_label.text()}

=== إحصائيات قاعدة البيانات / Database Statistics ===
حجم قاعدة البيانات / Database Size: {self.db_stats_labels['db_size'].text()}
عدد الجداول / Tables Count: {self.db_stats_labels['tables_count'].text()}
إجمالي السجلات / Total Records: {self.db_stats_labels['total_records'].text()}
آخر نسخة احتياطية / Last Backup: {self.db_stats_labels['last_backup'].text()}
الاتصالات النشطة / Active Connections: {self.db_stats_labels['active_connections'].text()}

=== العمليات الأكثر استهلاكاً / Top Processes ===
"""
        
        # Add top processes
        for row in range(min(10, self.process_table.rowCount())):
            pid = self.process_table.item(row, 0).text()
            name = self.process_table.item(row, 1).text()
            cpu = self.process_table.item(row, 2).text()
            memory = self.process_table.item(row, 3).text()
            
            report += f"PID: {pid}, Name: {name}, CPU: {cpu}, Memory: {memory}\n"
            
        return report
        
    def apply_monitor_style(self):
        """Apply monitoring-specific styling"""
        self.setStyleSheet("""
            QWidget {
                background-color: #2b2b2b;
                color: #ffffff;
                font-family: 'Segoe UI', Arial, sans-serif;
            }
            
            QGroupBox {
                font-weight: bold;
                border: 2px solid #555555;
                border-radius: 8px;
                margin-top: 1ex;
                padding-top: 10px;
                margin-bottom: 10px;
            }
            
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 10px;
                padding: 0 5px 0 5px;
                color: #0078d4;
            }
            
            QTableWidget {
                background-color: #404040;
                alternate-background-color: #353535;
                selection-background-color: #0078d4;
                gridline-color: #555555;
                border: 1px solid #555555;
                border-radius: 4px;
            }
            
            QTableWidget::item {
                padding: 8px;
                border: none;
            }
            
            QHeaderView::section {
                background-color: #353535;
                color: white;
                padding: 8px;
                border: 1px solid #555555;
                font-weight: bold;
            }
            
            QPushButton {
                background-color: #0078d4;
                color: white;
                border: none;
                padding: 8px 16px;
                border-radius: 4px;
                font-weight: bold;
            }
            
            QPushButton:hover {
                background-color: #106ebe;
            }
            
            QPushButton:pressed {
                background-color: #005a9e;
            }
            
            QTabWidget::pane {
                border: 1px solid #555555;
                background-color: #2b2b2b;
            }
            
            QTabBar::tab {
                background-color: #404040;
                color: white;
                padding: 8px 16px;
                margin-right: 2px;
                border-top-left-radius: 4px;
                border-top-right-radius: 4px;
            }
            
            QTabBar::tab:selected {
                background-color: #0078d4;
            }
            
            QTabBar::tab:hover {
                background-color: #555555;
            }
        """)
