from PyQt5.QtWidgets import (QDialog, QVBoxLayout, QHBoxLayout, QLabel, 
                            QComboBox, QPushButton, QMessageBox, QCheckBox)
from PyQt5.QtCore import Qt, pyqtSignal
from PyQt5.QtGui import QFont
from utils.config import Config
from utils.translator import translator

class LanguageSelector(QDialog):
    language_changed = pyqtSignal(str)
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.config = Config()
        self.init_ui()
        
    def init_ui(self):
        self.setWindowTitle(translator.tr("language_settings"))
        self.setFixedSize(400, 250)
        self.setModal(True)
        
        layout = QVBoxLayout()
        
        # Title
        title = QLabel(translator.tr("select_language"))
        title.setFont(QFont("Arial", 14, QFont.Bold))
        title.setAlignment(Qt.AlignCenter)
        title.setStyleSheet("color: #0078d4; margin-bottom: 20px;")
        layout.addWidget(title)
        
        # Language selection
        lang_layout = QHBoxLayout()
        
        lang_label = QLabel(translator.tr("language") + ":")
        lang_label.setFont(QFont("Arial", 11))
        lang_layout.addWidget(lang_label)
        
        self.language_combo = QComboBox()
        self.language_combo.setFont(QFont("Arial", 11))
        self.populate_languages()
        lang_layout.addWidget(self.language_combo)
        
        layout.addLayout(lang_layout)
        
        # RTL checkbox
        self.rtl_checkbox = QCheckBox(translator.tr("enable_rtl_layout"))
        self.rtl_checkbox.setFont(QFont("Arial", 10))
        layout.addWidget(self.rtl_checkbox)
        
        # Preview text
        preview_label = QLabel(translator.tr("preview") + ":")
        preview_label.setFont(QFont("Arial", 10))
        layout.addWidget(preview_label)
        
        self.preview_text = QLabel()
        self.preview_text.setStyleSheet("""
            background-color: #404040;
            padding: 10px;
            border-radius: 4px;
            border: 1px solid #555555;
        """)
        self.preview_text.setWordWrap(True)
        layout.addWidget(self.preview_text)
        
        # Connect signals
        self.language_combo.currentTextChanged.connect(self.update_preview)
        self.rtl_checkbox.toggled.connect(self.update_preview)
        
        # Buttons
        buttons_layout = QHBoxLayout()
        
        apply_btn = QPushButton(translator.tr("apply"))
        apply_btn.setFont(QFont("Arial", 10, QFont.Bold))
        apply_btn.clicked.connect(self.apply_language)
        
        cancel_btn = QPushButton(translator.tr("cancel"))
        cancel_btn.setFont(QFont("Arial", 10))
        cancel_btn.clicked.connect(self.reject)
        
        buttons_layout.addWidget(apply_btn)
        buttons_layout.addWidget(cancel_btn)
        
        layout.addLayout(buttons_layout)
        
        self.setLayout(layout)
        
        # Load current settings
        self.load_current_settings()
        self.update_preview()
        
    def populate_languages(self):
        """Populate language combo box"""
        supported_languages = self.config.get_supported_languages()
        
        for code, name in supported_languages.items():
            self.language_combo.addItem(f"{name} ({code.upper()})", code)
            
    def load_current_settings(self):
        """Load current language settings"""
        current_lang = self.config.get_language()
        
        # Set current language in combo
        for i in range(self.language_combo.count()):
            if self.language_combo.itemData(i) == current_lang:
                self.language_combo.setCurrentIndex(i)
                break
                
        # Set RTL checkbox
        self.rtl_checkbox.setChecked(self.config.is_rtl_language(current_lang))
        
    def update_preview(self):
        """Update preview text"""
        selected_lang = self.language_combo.currentData()
        
        if selected_lang:
            # Update RTL checkbox based on language
            is_rtl = self.config.is_rtl_language(selected_lang)
            self.rtl_checkbox.setChecked(is_rtl)
            
            # Show preview text
            preview_text = f"""
            {translator.get_text('app_title', selected_lang)}
            
            {translator.get_text('welcome', selected_lang)}
            
            {translator.get_text('dashboard', selected_lang)} | {translator.get_text('customers', selected_lang)} | {translator.get_text('inventory', selected_lang)}
            """
            
            self.preview_text.setText(preview_text)
            
            # Set text direction for preview
            if is_rtl:
                self.preview_text.setAlignment(Qt.AlignRight)
                self.preview_text.setLayoutDirection(Qt.RightToLeft)
            else:
                self.preview_text.setAlignment(Qt.AlignLeft)
                self.preview_text.setLayoutDirection(Qt.LeftToRight)
                
    def apply_language(self):
        """Apply selected language"""
        selected_lang = self.language_combo.currentData()
        
        if selected_lang:
            # Save to config
            self.config.set_language(selected_lang)
            
            # Update translator
            translator.set_language(selected_lang)
            
            # Emit signal
            self.language_changed.emit(selected_lang)
            
            # Show restart message
            QMessageBox.information(
                self, 
                translator.tr("language_changed"),
                translator.tr("restart_required")
            )
            
            self.accept()
