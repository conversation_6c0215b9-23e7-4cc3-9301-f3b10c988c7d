import sqlite3
import os
from datetime import datetime
import hashlib
import json
from cryptography.fernet import Fernet
import base64

class DatabaseManager:
    def __init__(self, db_path="data/al_hassan_stone.db"):
        self.db_path = db_path
        self.encryption_key = self._get_or_create_encryption_key()
        self.cipher_suite = Fernet(self.encryption_key)
        self.ensure_data_directory()
        
    def _get_or_create_encryption_key(self):
        """Get or create encryption key for sensitive data"""
        key_file = "config/encryption.key"
        os.makedirs(os.path.dirname(key_file), exist_ok=True)
        
        if os.path.exists(key_file):
            with open(key_file, 'rb') as f:
                return f.read()
        else:
            key = Fernet.generate_key()
            with open(key_file, 'wb') as f:
                f.write(key)
            return key
            
    def encrypt_sensitive_data(self, data):
        """Encrypt sensitive data"""
        if isinstance(data, str):
            data = data.encode()
        return self.cipher_suite.encrypt(data).decode()
        
    def decrypt_sensitive_data(self, encrypted_data):
        """Decrypt sensitive data"""
        if isinstance(encrypted_data, str):
            encrypted_data = encrypted_data.encode()
        return self.cipher_suite.decrypt(encrypted_data).decode()
        
    def ensure_data_directory(self):
        """Create data directory if it doesn't exist"""
        os.makedirs(os.path.dirname(self.db_path), exist_ok=True)
        
    def get_connection(self):
        """Get database connection"""
        conn = sqlite3.connect(self.db_path)
        conn.row_factory = sqlite3.Row
        # Enable foreign key constraints
        conn.execute("PRAGMA foreign_keys = ON")
        return conn
        
    def create_tables(self):
        """Create all necessary tables"""
        conn = self.get_connection()
        cursor = conn.cursor()
        
        # Users table
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS users (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                username TEXT UNIQUE NOT NULL,
                password_hash TEXT NOT NULL,
                full_name TEXT NOT NULL,
                role TEXT NOT NULL,
                permissions TEXT,
                is_active BOOLEAN DEFAULT 1,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                last_login TIMESTAMP,
                failed_login_attempts INTEGER DEFAULT 0,
                locked_until TIMESTAMP
            )
        ''')
        
        # Customers table
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS customers (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                name TEXT NOT NULL,
                phone TEXT,
                address TEXT,
                email TEXT,
                balance REAL DEFAULT 0,
                credit_limit REAL DEFAULT 0,
                payment_terms INTEGER DEFAULT 30,
                tax_id TEXT,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        ''')
        
        # Suppliers table
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS suppliers (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                name TEXT NOT NULL,
                phone TEXT,
                address TEXT,
                email TEXT,
                balance REAL DEFAULT 0,
                payment_terms INTEGER DEFAULT 30,
                tax_id TEXT,
                rating INTEGER DEFAULT 5,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        ''')
        
        # Material categories table
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS material_categories (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                name TEXT NOT NULL,
                description TEXT,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        ''')
        
        # Materials table
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS materials (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                category_id INTEGER,
                name TEXT NOT NULL,
                description TEXT,
                unit TEXT NOT NULL,
                unit_cost REAL DEFAULT 0,
                reorder_level INTEGER DEFAULT 0,
                current_stock REAL DEFAULT 0,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (category_id) REFERENCES material_categories (id)
            )
        ''')
        
        # Purchase orders table
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS purchase_orders (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                po_number TEXT UNIQUE NOT NULL,
                supplier_id INTEGER NOT NULL,
                order_date DATE NOT NULL,
                expected_delivery DATE,
                actual_delivery DATE,
                total_amount REAL NOT NULL,
                status TEXT DEFAULT 'pending',
                notes TEXT,
                created_by INTEGER,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (supplier_id) REFERENCES suppliers (id),
                FOREIGN KEY (created_by) REFERENCES users (id)
            )
        ''')
        
        # Purchase order items table
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS purchase_order_items (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                po_id INTEGER NOT NULL,
                material_id INTEGER NOT NULL,
                quantity REAL NOT NULL,
                unit_price REAL NOT NULL,
                total_price REAL NOT NULL,
                received_quantity REAL DEFAULT 0,
                FOREIGN KEY (po_id) REFERENCES purchase_orders (id),
                FOREIGN KEY (material_id) REFERENCES materials (id)
            )
        ''')
        
        # Trucks table
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS trucks (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                truck_number TEXT NOT NULL,
                supplier_id INTEGER,
                arrival_date DATE NOT NULL,
                total_weight REAL NOT NULL,
                price_per_ton REAL NOT NULL,
                total_cost REAL NOT NULL,
                quality_grade TEXT DEFAULT 'A',
                notes TEXT,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (supplier_id) REFERENCES suppliers (id)
            )
        ''')
        
        # Blocks table
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS blocks (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                block_number TEXT UNIQUE NOT NULL,
                truck_id INTEGER NOT NULL,
                length REAL NOT NULL,
                width REAL NOT NULL,
                height REAL NOT NULL,
                weight REAL NOT NULL,
                granite_type TEXT NOT NULL,
                quality_grade TEXT DEFAULT 'A',
                status TEXT DEFAULT 'available',
                location TEXT,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (truck_id) REFERENCES trucks (id)
            )
        ''')
        
        # Production plans table
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS production_plans (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                plan_number TEXT UNIQUE NOT NULL,
                plan_date DATE NOT NULL,
                start_date DATE,
                end_date DATE,
                status TEXT DEFAULT 'draft',
                priority INTEGER DEFAULT 3,
                notes TEXT,
                created_by INTEGER,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (created_by) REFERENCES users (id)
            )
        ''')
        
        # Production plan items table
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS production_plan_items (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                plan_id INTEGER NOT NULL,
                block_id INTEGER NOT NULL,
                target_slabs INTEGER NOT NULL,
                slab_length REAL NOT NULL,
                slab_width REAL NOT NULL,
                slab_thickness REAL NOT NULL,
                estimated_waste REAL DEFAULT 0,
                actual_slabs INTEGER DEFAULT 0,
                actual_waste REAL DEFAULT 0,
                status TEXT DEFAULT 'pending',
                FOREIGN KEY (plan_id) REFERENCES production_plans (id),
                FOREIGN KEY (block_id) REFERENCES blocks (id)
            )
        ''')
        
        # Slabs table
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS slabs (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                slab_number TEXT UNIQUE NOT NULL,
                block_id INTEGER NOT NULL,
                production_plan_item_id INTEGER,
                length REAL NOT NULL,
                width REAL NOT NULL,
                thickness REAL NOT NULL,
                area REAL NOT NULL,
                granite_type TEXT NOT NULL,
                quality_grade TEXT DEFAULT 'A',
                status TEXT DEFAULT 'in_stock',
                location TEXT,
                defects TEXT,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (block_id) REFERENCES blocks (id),
                FOREIGN KEY (production_plan_item_id) REFERENCES production_plan_items (id)
            )
        ''')
        
        # Quality inspections table
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS quality_inspections (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                inspection_number TEXT UNIQUE NOT NULL,
                slab_id INTEGER NOT NULL,
                inspector_id INTEGER NOT NULL,
                inspection_date DATE NOT NULL,
                overall_grade TEXT NOT NULL,
                surface_quality INTEGER DEFAULT 5,
                dimensional_accuracy INTEGER DEFAULT 5,
                color_consistency INTEGER DEFAULT 5,
                defects TEXT,
                corrective_actions TEXT,
                status TEXT DEFAULT 'passed',
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (slab_id) REFERENCES slabs (id),
                FOREIGN KEY (inspector_id) REFERENCES users (id)
            )
        ''')
        
        # Equipment table
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS equipment (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                equipment_code TEXT UNIQUE NOT NULL,
                name TEXT NOT NULL,
                type TEXT NOT NULL,
                manufacturer TEXT,
                model TEXT,
                serial_number TEXT,
                purchase_date DATE,
                purchase_cost REAL,
                location TEXT,
                status TEXT DEFAULT 'operational',
                last_maintenance DATE,
                next_maintenance DATE,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        ''')
        
        # Maintenance schedules table
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS maintenance_schedules (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                equipment_id INTEGER NOT NULL,
                maintenance_type TEXT NOT NULL,
                frequency_days INTEGER NOT NULL,
                last_performed DATE,
                next_due DATE NOT NULL,
                is_active BOOLEAN DEFAULT 1,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (equipment_id) REFERENCES equipment (id)
            )
        ''')
        
        # Maintenance records table
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS maintenance_records (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                maintenance_number TEXT UNIQUE NOT NULL,
                equipment_id INTEGER NOT NULL,
                maintenance_type TEXT NOT NULL,
                scheduled_date DATE,
                actual_date DATE NOT NULL,
                technician_id INTEGER,
                duration_hours REAL,
                cost REAL DEFAULT 0,
                parts_used TEXT,
                work_performed TEXT,
                status TEXT DEFAULT 'completed',
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (equipment_id) REFERENCES equipment (id),
                FOREIGN KEY (technician_id) REFERENCES users (id)
            )
        ''')
        
        # Sales quotations table
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS sales_quotations (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                quotation_number TEXT UNIQUE NOT NULL,
                customer_id INTEGER NOT NULL,
                quotation_date DATE NOT NULL,
                valid_until DATE NOT NULL,
                total_amount REAL NOT NULL,
                discount_percent REAL DEFAULT 0,
                discount_amount REAL DEFAULT 0,
                tax_percent REAL DEFAULT 0,
                tax_amount REAL DEFAULT 0,
                final_amount REAL NOT NULL,
                status TEXT DEFAULT 'draft',
                notes TEXT,
                created_by INTEGER,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (customer_id) REFERENCES customers (id),
                FOREIGN KEY (created_by) REFERENCES users (id)
            )
        ''')
        
        # Sales quotation items table
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS sales_quotation_items (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                quotation_id INTEGER NOT NULL,
                slab_id INTEGER,
                description TEXT NOT NULL,
                quantity REAL NOT NULL,
                unit_price REAL NOT NULL,
                total_price REAL NOT NULL,
                FOREIGN KEY (quotation_id) REFERENCES sales_quotations (id),
                FOREIGN KEY (slab_id) REFERENCES slabs (id)
            )
        ''')
        
        # Sales table
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS sales (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                invoice_number TEXT UNIQUE NOT NULL,
                quotation_id INTEGER,
                customer_id INTEGER NOT NULL,
                sale_date DATE NOT NULL,
                total_amount REAL NOT NULL,
                discount_percent REAL DEFAULT 0,
                discount_amount REAL DEFAULT 0,
                tax_percent REAL DEFAULT 0,
                tax_amount REAL DEFAULT 0,
                final_amount REAL NOT NULL,
                paid_amount REAL DEFAULT 0,
                status TEXT DEFAULT 'pending',
                payment_terms INTEGER DEFAULT 30,
                due_date DATE,
                notes TEXT,
                created_by INTEGER,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (quotation_id) REFERENCES sales_quotations (id),
                FOREIGN KEY (customer_id) REFERENCES customers (id),
                FOREIGN KEY (created_by) REFERENCES users (id)
            )
        ''')
        
        # Sale items table
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS sale_items (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                sale_id INTEGER NOT NULL,
                slab_id INTEGER NOT NULL,
                quantity REAL NOT NULL,
                unit_price REAL NOT NULL,
                total_price REAL NOT NULL,
                FOREIGN KEY (sale_id) REFERENCES sales (id),
                FOREIGN KEY (slab_id) REFERENCES slabs (id)
            )
        ''')
        
        # Payments table
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS payments (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                payment_number TEXT UNIQUE NOT NULL,
                sale_id INTEGER,
                customer_id INTEGER NOT NULL,
                payment_date DATE NOT NULL,
                amount REAL NOT NULL,
                payment_method TEXT NOT NULL,
                reference_number TEXT,
                notes TEXT,
                created_by INTEGER,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (sale_id) REFERENCES sales (id),
                FOREIGN KEY (customer_id) REFERENCES customers (id),
                FOREIGN KEY (created_by) REFERENCES users (id)
            )
        ''')
        
        # Expenses table
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS expenses (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                expense_number TEXT UNIQUE NOT NULL,
                category TEXT NOT NULL,
                subcategory TEXT,
                description TEXT NOT NULL,
                amount REAL NOT NULL,
                expense_date DATE NOT NULL,
                supplier_id INTEGER,
                payment_method TEXT,
                reference_number TEXT,
                is_recurring BOOLEAN DEFAULT 0,
                recurrence_frequency TEXT,
                created_by INTEGER,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (supplier_id) REFERENCES suppliers (id),
                FOREIGN KEY (created_by) REFERENCES users (id)
            )
        ''')
        
        # KPI definitions table
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS kpi_definitions (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                name TEXT NOT NULL,
                description TEXT,
                calculation_method TEXT NOT NULL,
                target_value REAL,
                unit TEXT,
                category TEXT,
                is_active BOOLEAN DEFAULT 1,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        ''')
        
        # KPI values table
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS kpi_values (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                kpi_id INTEGER NOT NULL,
                period_start DATE NOT NULL,
                period_end DATE NOT NULL,
                actual_value REAL NOT NULL,
                target_value REAL,
                variance REAL,
                calculated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (kpi_id) REFERENCES kpi_definitions (id)
            )
        ''')
        
        # Integration settings table
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS integration_settings (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                system_name TEXT NOT NULL,
                system_type TEXT NOT NULL,
                connection_string TEXT,
                api_key TEXT,
                settings_json TEXT,
                is_active BOOLEAN DEFAULT 0,
                last_sync TIMESTAMP,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        ''')
        
        # Backup logs table
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS backup_logs (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                backup_type TEXT NOT NULL,
                file_path TEXT NOT NULL,
                file_size INTEGER,
                status TEXT NOT NULL,
                error_message TEXT,
                created_by INTEGER,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (created_by) REFERENCES users (id)
            )
        ''')
        
        # Activity logs table
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS activity_logs (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                user_id INTEGER,
                action TEXT NOT NULL,
                table_name TEXT,
                record_id INTEGER,
                old_values TEXT,
                new_values TEXT,
                ip_address TEXT,
                user_agent TEXT,
                timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (user_id) REFERENCES users (id)
            )
        ''')
        
        # System settings table
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS system_settings (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                setting_key TEXT UNIQUE NOT NULL,
                setting_value TEXT,
                setting_type TEXT DEFAULT 'string',
                description TEXT,
                is_encrypted BOOLEAN DEFAULT 0,
                updated_by INTEGER,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (updated_by) REFERENCES users (id)
            )
        ''')
        
        conn.commit()
        
        # Create default data
        self.create_default_data()
        
        conn.close()
        
    def create_default_data(self):
        """Create default data"""
        conn = self.get_connection()
        cursor = conn.cursor()
        
        # Create default admin user if not exists
        cursor.execute("SELECT id FROM users WHERE username = 'admin'")
        if cursor.fetchone() is None:
            password_hash = hashlib.sha256('admin123'.encode()).hexdigest()
            cursor.execute('''
                INSERT INTO users (username, password_hash, full_name, role, permissions)
                VALUES (?, ?, ?, ?, ?)
            ''', ('admin', password_hash, 'System Administrator', 'admin', 
                  json.dumps(['all'])))
                  
        # Create default material categories
        categories = [
            ('Raw Materials', 'Granite blocks and raw stone materials'),
            ('Cutting Tools', 'Diamond blades, cutting discs, and tools'),
            ('Consumables', 'Polishing compounds, adhesives, sealers'),
            ('Maintenance', 'Spare parts and maintenance supplies'),
            ('Safety Equipment', 'Safety gear and protective equipment')
        ]
        
        for name, description in categories:
            cursor.execute('''
                INSERT OR IGNORE INTO material_categories (name, description)
                VALUES (?, ?)
            ''', (name, description))
            
        # Create default KPIs
        kpis = [
            ('Production Efficiency', 'Percentage of planned vs actual production', 'percentage', 85.0, '%', 'Production'),
            ('Waste Percentage', 'Percentage of material waste in production', 'percentage', 5.0, '%', 'Production'),
            ('Customer Satisfaction', 'Average customer satisfaction rating', 'average', 4.5, 'stars', 'Sales'),
            ('Inventory Turnover', 'Number of times inventory is sold per period', 'ratio', 6.0, 'times', 'Inventory'),
            ('Equipment Uptime', 'Percentage of equipment operational time', 'percentage', 95.0, '%', 'Maintenance'),
            ('Order Fulfillment Time', 'Average days to fulfill customer orders', 'average', 7.0, 'days', 'Sales'),
            ('Quality Pass Rate', 'Percentage of products passing quality inspection', 'percentage', 98.0, '%', 'Quality'),
            ('Profit Margin', 'Gross profit margin percentage', 'percentage', 25.0, '%', 'Financial')
        ]
        
        for name, description, method, target, unit, category in kpis:
            cursor.execute('''
                INSERT OR IGNORE INTO kpi_definitions 
                (name, description, calculation_method, target_value, unit, category)
                VALUES (?, ?, ?, ?, ?, ?)
            ''', (name, description, method, target, unit, category))
            
        # Create default system settings
        settings = [
            ('company_name', 'Al-Hassan Stone Factory', 'string', 'Company name'),
            ('company_address', '', 'string', 'Company address'),
            ('company_phone', '', 'string', 'Company phone number'),
            ('company_email', '', 'string', 'Company email address'),
            ('tax_rate', '0.15', 'float', 'Default tax rate'),
            ('currency_symbol', 'SAR', 'string', 'Currency symbol'),
            ('backup_frequency', 'daily', 'string', 'Automatic backup frequency'),
            ('session_timeout', '30', 'integer', 'Session timeout in minutes'),
            ('max_login_attempts', '3', 'integer', 'Maximum login attempts before lockout'),
            ('lockout_duration', '15', 'integer', 'Account lockout duration in minutes')
        ]
        
        for key, value, type_, description in settings:
            cursor.execute('''
                INSERT OR IGNORE INTO system_settings 
                (setting_key, setting_value, setting_type, description)
                VALUES (?, ?, ?, ?)
            ''', (key, value, type_, description))
            
        conn.commit()
        conn.close()
        
    def authenticate_user(self, username, password):
        """Authenticate user login with security measures"""
        conn = self.get_connection()
        cursor = conn.cursor()
        
        # Check if user exists and is not locked
        cursor.execute('''
            SELECT id, username, password_hash, full_name, role, permissions,
                   is_active, failed_login_attempts, locked_until
            FROM users 
            WHERE username = ? AND is_active = 1
        ''', (username,))
        
        user = cursor.fetchone()
        
        if not user:
            conn.close()
            return None
            
        # Check if account is locked
        if user['locked_until'] and datetime.now() < datetime.fromisoformat(user['locked_until']):
            conn.close()
            return None
            
        # Verify password
        password_hash = hashlib.sha256(password.encode()).hexdigest()
        
        if user['password_hash'] == password_hash:
            # Successful login - reset failed attempts and update last login
            cursor.execute('''
                UPDATE users 
                SET last_login = CURRENT_TIMESTAMP, 
                    failed_login_attempts = 0,
                    locked_until = NULL
                WHERE id = ?
            ''', (user['id'],))
            conn.commit()
            
            # Log successful login
            self.log_activity(user['id'], f"User logged in: {username}")
            
            conn.close()
            return dict(user)
        else:
            # Failed login - increment failed attempts
            failed_attempts = user['failed_login_attempts'] + 1
            max_attempts = int(self.get_system_setting('max_login_attempts', 3))
            
            if failed_attempts >= max_attempts:
                # Lock account
                lockout_duration = int(self.get_system_setting('lockout_duration', 15))
                locked_until = datetime.now().replace(microsecond=0) + \
                              timedelta(minutes=lockout_duration)
                
                cursor.execute('''
                    UPDATE users 
                    SET failed_login_attempts = ?, locked_until = ?
                    WHERE id = ?
                ''', (failed_attempts, locked_until.isoformat(), user['id']))
            else:
                cursor.execute('''
                    UPDATE users 
                    SET failed_login_attempts = ?
                    WHERE id = ?
                ''', (failed_attempts, user['id']))
                
            conn.commit()
            conn.close()
            return None
            
    def log_activity(self, user_id, action, table_name=None, record_id=None, 
                    old_values=None, new_values=None, ip_address=None, user_agent=None):
        """Log user activity with enhanced details"""
        conn = self.get_connection()
        cursor = conn.cursor()
        
        cursor.execute('''
            INSERT INTO activity_logs 
            (user_id, action, table_name, record_id, old_values, new_values, ip_address, user_agent)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?)
        ''', (user_id, action, table_name, record_id, 
              json.dumps(old_values) if old_values else None,
              json.dumps(new_values) if new_values else None,
              ip_address, user_agent))
        
        conn.commit()
        conn.close()
        
    def get_system_setting(self, key, default_value=None):
        """Get system setting value"""
        conn = self.get_connection()
        cursor = conn.cursor()
        
        cursor.execute('''
            SELECT setting_value, setting_type, is_encrypted
            FROM system_settings 
            WHERE setting_key = ?
        ''', (key,))
        
        result = cursor.fetchone()
        conn.close()
        
        if result:
            value = result['setting_value']
            
            # Decrypt if encrypted
            if result['is_encrypted']:
                value = self.decrypt_sensitive_data(value)
                
            # Convert to appropriate type
            if result['setting_type'] == 'integer':
                return int(value)
            elif result['setting_type'] == 'float':
                return float(value)
            elif result['setting_type'] == 'boolean':
                return value.lower() in ('true', '1', 'yes')
            else:
                return value
        
        return default_value
        
    def set_system_setting(self, key, value, setting_type='string', is_encrypted=False, user_id=None):
        """Set system setting value"""
        conn = self.get_connection()
        cursor = conn.cursor()
        
        # Encrypt if needed
        if is_encrypted:
            value = self.encrypt_sensitive_data(str(value))
            
        cursor.execute('''
            INSERT OR REPLACE INTO system_settings 
            (setting_key, setting_value, setting_type, is_encrypted, updated_by)
            VALUES (?, ?, ?, ?, ?)
        ''', (key, str(value), setting_type, is_encrypted, user_id))
        
        conn.commit()
        conn.close()
        
    def backup_database(self, backup_path, user_id=None):
        """Create database backup"""
        try:
            import shutil
            
            # Create backup directory if it doesn't exist
            os.makedirs(os.path.dirname(backup_path), exist_ok=True)
            
            # Copy database file
            shutil.copy2(self.db_path, backup_path)
            
            # Get file size
            file_size = os.path.getsize(backup_path)
            
            # Log backup
            conn = self.get_connection()
            cursor = conn.cursor()
            
            cursor.execute('''
                INSERT INTO backup_logs 
                (backup_type, file_path, file_size, status, created_by)
                VALUES (?, ?, ?, ?, ?)
            ''', ('manual', backup_path, file_size, 'success', user_id))
            
            conn.commit()
            conn.close()
            
            return True, "Backup created successfully"
            
        except Exception as e:
            # Log failed backup
            conn = self.get_connection()
            cursor = conn.cursor()
            
            cursor.execute('''
                INSERT INTO backup_logs 
                (backup_type, file_path, status, error_message, created_by)
                VALUES (?, ?, ?, ?, ?)
            ''', ('manual', backup_path, 'failed', str(e), user_id))
            
            conn.commit()
            conn.close()
            
            return False, f"Backup failed: {str(e)}"
            
    def restore_database(self, backup_path, user_id=None):
        """Restore database from backup"""
        try:
            import shutil
            
            if not os.path.exists(backup_path):
                return False, "Backup file not found"
                
            # Create backup of current database
            current_backup = f"{self.db_path}.restore_backup_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
            shutil.copy2(self.db_path, current_backup)
            
            # Restore from backup
            shutil.copy2(backup_path, self.db_path)
            
            # Log restore
            if user_id:
                self.log_activity(user_id, f"Database restored from: {backup_path}")
                
            return True, "Database restored successfully"
            
        except Exception as e:
            return False, f"Restore failed: {str(e)}"
