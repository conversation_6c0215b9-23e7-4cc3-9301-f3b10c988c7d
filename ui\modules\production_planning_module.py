from PyQt5.QtWidgets import (QWidget, QVBoxLayout, QHBoxLayout, QTableWidget, 
                            QTableWidgetItem, QPushButton, QLineEdit, QLabel,
                            QDialog, QFormLayout, QMessageBox, QHeaderView,
                            QDateEdit, QDoubleSpinBox, QComboBox, QTextEdit,
                            QSpinBox, QTabWidget, QGroupBox, QGridLayout,
                            QProgressBar, QSplitter, QTreeWidget, QTreeWidgetItem)
from PyQt5.QtCore import Qt, QDate, QTimer, pyqtSignal
from PyQt5.QtGui import QFont, QColor
from datetime import datetime, timedelta
import json

class ProductionPlanningModule(QWidget):
    def __init__(self, db_manager, user_data):
        super().__init__()
        self.db_manager = db_manager
        self.user_data = user_data
        self.init_ui()
        self.load_production_plans()
        
    def init_ui(self):
        layout = QVBoxLayout()
        
        # Header
        header_layout = QHBoxLayout()
        
        title = QLabel("تخطيط الإنتاج / Production Planning")
        title.setStyleSheet("font-size: 16pt; font-weight: bold; color: #0078d4;")
        header_layout.addWidget(title)
        
        header_layout.addStretch()
        
        # New plan button
        new_plan_btn = QPushButton("خطة إنتاج جديدة / New Production Plan")
        new_plan_btn.setStyleSheet("background-color: #28a745;")
        new_plan_btn.clicked.connect(self.create_new_plan)
        header_layout.addWidget(new_plan_btn)
        
        # Optimize button
        optimize_btn = QPushButton("تحسين الخطة / Optimize Plan")
        optimize_btn.setStyleSheet("background-color: #17a2b8;")
        optimize_btn.clicked.connect(self.optimize_cutting_plan)
        header_layout.addWidget(optimize_btn)
        
        layout.addLayout(header_layout)
        
        # Create splitter for main content
        splitter = QSplitter(Qt.Horizontal)
        
        # Left panel - Plans list
        left_panel = self.create_plans_panel()
        splitter.addWidget(left_panel)
        
        # Right panel - Plan details
        right_panel = self.create_plan_details_panel()
        splitter.addWidget(right_panel)
        
        splitter.setSizes([400, 600])
        layout.addWidget(splitter)
        
        self.setLayout(layout)
        
    def create_plans_panel(self):
        """Create production plans list panel"""
        widget = QWidget()
        layout = QVBoxLayout(widget)
        
        # Filter controls
        filter_layout = QHBoxLayout()
        
        status_label = QLabel("الحالة / Status:")
        self.status_filter = QComboBox()
        self.status_filter.addItems([
            "الكل / All", "مسودة / Draft", "مجدولة / Scheduled", 
            "قيد التنفيذ / In Progress", "مكتملة / Completed", "ملغية / Cancelled"
        ])
        self.status_filter.currentTextChanged.connect(self.filter_plans)
        
        filter_layout.addWidget(status_label)
        filter_layout.addWidget(self.status_filter)
        filter_layout.addStretch()
        
        layout.addLayout(filter_layout)
        
        # Plans table
        self.plans_table = QTableWidget()
        self.plans_table.setColumnCount(6)
        self.plans_table.setHorizontalHeaderLabels([
            "رقم الخطة / Plan No.", "تاريخ الخطة / Plan Date", 
            "تاريخ البداية / Start Date", "تاريخ النهاية / End Date",
            "الحالة / Status", "الأولوية / Priority"
        ])
        
        # Set column widths
        header = self.plans_table.horizontalHeader()
        header.setSectionResizeMode(QHeaderView.Stretch)
        
        self.plans_table.selectionModel().selectionChanged.connect(self.on_plan_selected)
        
        layout.addWidget(self.plans_table)
        
        return widget
        
    def create_plan_details_panel(self):
        """Create plan details panel"""
        widget = QWidget()
        layout = QVBoxLayout(widget)
        
        # Plan info
        info_group = QGroupBox("معلومات الخطة / Plan Information")
        info_layout = QFormLayout(info_group)
        
        self.plan_number_label = QLabel("-")
        self.plan_status_label = QLabel("-")
        self.plan_progress_bar = QProgressBar()
        
        info_layout.addRow("رقم الخطة / Plan Number:", self.plan_number_label)
        info_layout.addRow("الحالة / Status:", self.plan_status_label)
        info_layout.addRow("التقدم / Progress:", self.plan_progress_bar)
        
        layout.addWidget(info_group)
        
        # Plan items
        items_group = QGroupBox("عناصر الخطة / Plan Items")
        items_layout = QVBoxLayout(items_group)
        
        # Items table
        self.plan_items_table = QTableWidget()
        self.plan_items_table.setColumnCount(8)
        self.plan_items_table.setHorizontalHeaderLabels([
            "رقم الكتلة / Block No.", "الهدف / Target Slabs", 
            "الطول / Length", "العرض / Width", "السماكة / Thickness",
            "الفاقد المتوقع / Est. Waste", "الفعلي / Actual", "الحالة / Status"
        ])
        
        items_header = self.plan_items_table.horizontalHeader()
        items_header.setSectionResizeMode(QHeaderView.Stretch)
        
        items_layout.addWidget(self.plan_items_table)
        
        # Action buttons
        actions_layout = QHBoxLayout()
        
        start_btn = QPushButton("بدء التنفيذ / Start Execution")
        start_btn.clicked.connect(self.start_plan_execution)
        
        complete_btn = QPushButton("إكمال / Complete")
        complete_btn.clicked.connect(self.complete_plan_item)
        
        cancel_btn = QPushButton("إلغاء / Cancel")
        cancel_btn.clicked.connect(self.cancel_plan)
        
        actions_layout.addWidget(start_btn)
        actions_layout.addWidget(complete_btn)
        actions_layout.addWidget(cancel_btn)
        actions_layout.addStretch()
        
        items_layout.addLayout(actions_layout)
        
        layout.addWidget(items_group)
        
        return widget
        
    def load_production_plans(self):
        """Load production plans from database"""
        conn = self.db_manager.get_connection()
        cursor = conn.cursor()
        
        cursor.execute("""
            SELECT pp.id, pp.plan_number, pp.plan_date, pp.start_date, 
                   pp.end_date, pp.status, pp.priority, pp.notes,
                   COUNT(ppi.id) as total_items,
                   SUM(CASE WHEN ppi.status = 'completed' THEN 1 ELSE 0 END) as completed_items
            FROM production_plans pp
            LEFT JOIN production_plan_items ppi ON pp.id = ppi.plan_id
            GROUP BY pp.id
            ORDER BY pp.created_at DESC
        """)
        
        plans = cursor.fetchall()
        
        self.plans_table.setRowCount(len(plans))
        
        for row, plan in enumerate(plans):
            self.plans_table.setItem(row, 0, QTableWidgetItem(plan['plan_number']))
            self.plans_table.setItem(row, 1, QTableWidgetItem(plan['plan_date']))
            self.plans_table.setItem(row, 2, QTableWidgetItem(plan['start_date'] or '-'))
            self.plans_table.setItem(row, 3, QTableWidgetItem(plan['end_date'] or '-'))
            
            # Status with color coding
            status_item = QTableWidgetItem(plan['status'])
            if plan['status'] == 'completed':
                status_item.setBackground(QColor('#28a745'))
            elif plan['status'] == 'in_progress':
                status_item.setBackground(QColor('#ffc107'))
            elif plan['status'] == 'cancelled':
                status_item.setBackground(QColor('#dc3545'))
            
            self.plans_table.setItem(row, 4, status_item)
            self.plans_table.setItem(row, 5, QTableWidgetItem(str(plan['priority'])))
            
            # Store plan ID in first column
            self.plans_table.item(row, 0).setData(Qt.UserRole, plan['id'])
            
        conn.close()
        
    def on_plan_selected(self):
        """Handle plan selection"""
        current_row = self.plans_table.currentRow()
        if current_row >= 0:
            plan_id = self.plans_table.item(current_row, 0).data(Qt.UserRole)
            self.load_plan_details(plan_id)
            
    def load_plan_details(self, plan_id):
        """Load plan details"""
        conn = self.db_manager.get_connection()
        cursor = conn.cursor()
        
        # Get plan info
        cursor.execute("""
            SELECT * FROM production_plans WHERE id = ?
        """, (plan_id,))
        
        plan = cursor.fetchone()
        
        if plan:
            self.plan_number_label.setText(plan['plan_number'])
            self.plan_status_label.setText(plan['status'])
            
            # Get plan items
            cursor.execute("""
                SELECT ppi.*, b.block_number
                FROM production_plan_items ppi
                JOIN blocks b ON ppi.block_id = b.id
                WHERE ppi.plan_id = ?
            """, (plan_id,))
            
            items = cursor.fetchall()
            
            self.plan_items_table.setRowCount(len(items))
            
            completed_items = 0
            
            for row, item in enumerate(items):
                self.plan_items_table.setItem(row, 0, QTableWidgetItem(item['block_number']))
                self.plan_items_table.setItem(row, 1, QTableWidgetItem(str(item['target_slabs'])))
                self.plan_items_table.setItem(row, 2, QTableWidgetItem(f"{item['slab_length']:.2f}"))
                self.plan_items_table.setItem(row, 3, QTableWidgetItem(f"{item['slab_width']:.2f}"))
                self.plan_items_table.setItem(row, 4, QTableWidgetItem(f"{item['slab_thickness']:.2f}"))
                self.plan_items_table.setItem(row, 5, QTableWidgetItem(f"{item['estimated_waste']:.2f}%"))
                self.plan_items_table.setItem(row, 6, QTableWidgetItem(str(item['actual_slabs'])))
                self.plan_items_table.setItem(row, 7, QTableWidgetItem(item['status']))
                
                if item['status'] == 'completed':
                    completed_items += 1
                    
            # Update progress bar
            if len(items) > 0:
                progress = int((completed_items / len(items)) * 100)
                self.plan_progress_bar.setValue(progress)
            else:
                self.plan_progress_bar.setValue(0)
                
        conn.close()
        
    def create_new_plan(self):
        """Create new production plan"""
        dialog = ProductionPlanDialog(self.db_manager, self.user_data)
        if dialog.exec_() == QDialog.Accepted:
            self.load_production_plans()
            
    def optimize_cutting_plan(self):
        """Optimize cutting plan to minimize waste"""
        current_row = self.plans_table.currentRow()
        if current_row < 0:
            QMessageBox.warning(self, "تحذير / Warning", 
                              "يرجى اختيار خطة إنتاج\nPlease select a production plan")
            return
            
        plan_id = self.plans_table.item(current_row, 0).data(Qt.UserRole)
        
        # Run optimization algorithm
        optimizer = CuttingOptimizer(self.db_manager)
        optimized_plan = optimizer.optimize_plan(plan_id)
        
        if optimized_plan:
            QMessageBox.information(self, "نجح / Success", 
                                  f"تم تحسين الخطة بنجاح\nPlan optimized successfully\n"
                                  f"توفير في الفاقد: {optimized_plan['waste_reduction']:.1f}%")
            self.load_plan_details(plan_id)
        else:
            QMessageBox.warning(self, "تحذير / Warning", 
                              "لا يمكن تحسين الخطة\nCannot optimize plan")
            
    def start_plan_execution(self):
        """Start plan execution"""
        current_row = self.plans_table.currentRow()
        if current_row < 0:
            return
            
        plan_id = self.plans_table.item(current_row, 0).data(Qt.UserRole)
        
        conn = self.db_manager.get_connection()
        cursor = conn.cursor()
        
        cursor.execute("""
            UPDATE production_plans 
            SET status = 'in_progress', start_date = ?
            WHERE id = ?
        """, (datetime.now().date().isoformat(), plan_id))
        
        conn.commit()
        conn.close()
        
        self.load_production_plans()
        
    def complete_plan_item(self):
        """Complete selected plan item"""
        current_row = self.plan_items_table.currentRow()
        if current_row < 0:
            return
            
        # This would open a dialog to record actual production results
        QMessageBox.information(self, "معلومات / Info", 
                              "سيتم إضافة نافذة تسجيل النتائج الفعلية\n"
                              "Actual results recording dialog will be added")
        
    def cancel_plan(self):
        """Cancel production plan"""
        current_row = self.plans_table.currentRow()
        if current_row < 0:
            return
            
        reply = QMessageBox.question(self, "تأكيد / Confirm",
                                   "هل تريد إلغاء هذه الخطة؟\nDo you want to cancel this plan?",
                                   QMessageBox.Yes | QMessageBox.No)
        
        if reply == QMessageBox.Yes:
            plan_id = self.plans_table.item(current_row, 0).data(Qt.UserRole)
            
            conn = self.db_manager.get_connection()
            cursor = conn.cursor()
            
            cursor.execute("""
                UPDATE production_plans 
                SET status = 'cancelled'
                WHERE id = ?
            """, (plan_id,))
            
            conn.commit()
            conn.close()
            
            self.load_production_plans()
            
    def filter_plans(self):
        """Filter plans by status"""
        filter_text = self.status_filter.currentText()
        
        for row in range(self.plans_table.rowCount()):
            if "الكل" in filter_text or "All" in filter_text:
                self.plans_table.setRowHidden(row, False)
            else:
                status_item = self.plans_table.item(row, 4)
                if status_item:
                    should_show = any(keyword in filter_text.lower() 
                                    for keyword in status_item.text().lower().split())
                    self.plans_table.setRowHidden(row, not should_show)

class ProductionPlanDialog(QDialog):
    def __init__(self, db_manager, user_data, plan_id=None):
        super().__init__()
        self.db_manager = db_manager
        self.user_data = user_data
        self.plan_id = plan_id
        self.plan_items = []
        self.init_ui()
        
        if plan_id:
            self.load_plan_data()
            
    def init_ui(self):
        self.setWindowTitle("خطة إنتاج / Production Plan")
        self.setFixedSize(800, 600)
        
        layout = QVBoxLayout()
        
        # Plan header
        header_group = QGroupBox("معلومات الخطة / Plan Information")
        header_layout = QFormLayout(header_group)
        
        self.plan_number_input = QLineEdit()
        self.plan_number_input.setText(self.generate_plan_number())
        self.plan_number_input.setReadOnly(True)
        header_layout.addRow("رقم الخطة / Plan Number:", self.plan_number_input)
        
        self.plan_date_input = QDateEdit()
        self.plan_date_input.setDate(QDate.currentDate())
        self.plan_date_input.setCalendarPopup(True)
        header_layout.addRow("تاريخ الخطة / Plan Date:", self.plan_date_input)
        
        self.priority_input = QSpinBox()
        self.priority_input.setRange(1, 5)
        self.priority_input.setValue(3)
        header_layout.addRow("الأولوية / Priority:", self.priority_input)
        
        self.notes_input = QTextEdit()
        self.notes_input.setMaximumHeight(60)
        header_layout.addRow("ملاحظات / Notes:", self.notes_input)
        
        layout.addWidget(header_group)
        
        # Available blocks
        blocks_group = QGroupBox("الكتل المتاحة / Available Blocks")
        blocks_layout = QVBoxLayout(blocks_group)
        
        self.blocks_table = QTableWidget()
        self.blocks_table.setColumnCount(6)
        self.blocks_table.setHorizontalHeaderLabels([
            "رقم الكتلة / Block No.", "النوع / Type", "الطول / Length",
            "العرض / Width", "الارتفاع / Height", "إضافة / Add"
        ])
        
        self.load_available_blocks()
        blocks_layout.addWidget(self.blocks_table)
        
        layout.addWidget(blocks_group)
        
        # Plan items
        items_group = QGroupBox("عناصر الخطة / Plan Items")
        items_layout = QVBoxLayout(items_group)
        
        self.items_table = QTableWidget()
        self.items_table.setColumnCount(7)
        self.items_table.setHorizontalHeaderLabels([
            "رقم الكتلة / Block No.", "الهدف / Target", "الطول / Length",
            "العرض / Width", "السماكة / Thickness", "الفاقد / Waste", "حذف / Remove"
        ])
        
        items_layout.addWidget(self.items_table)
        
        layout.addWidget(items_group)
        
        # Buttons
        buttons_layout = QHBoxLayout()
        
        save_btn = QPushButton("حفظ / Save")
        save_btn.clicked.connect(self.save_plan)
        
        cancel_btn = QPushButton("إلغاء / Cancel")
        cancel_btn.clicked.connect(self.reject)
        
        buttons_layout.addWidget(save_btn)
        buttons_layout.addWidget(cancel_btn)
        
        layout.addLayout(buttons_layout)
        
        self.setLayout(layout)
        
    def generate_plan_number(self):
        """Generate unique plan number"""
        timestamp = datetime.now().strftime("%Y%m%d%H%M%S")
        return f"PLAN-{timestamp}"
        
    def load_available_blocks(self):
        """Load available blocks"""
        conn = self.db_manager.get_connection()
        cursor = conn.cursor()
        
        cursor.execute("""
            SELECT id, block_number, granite_type, length, width, height
            FROM blocks
            WHERE status = 'available'
            ORDER BY block_number
        """)
        
        blocks = cursor.fetchall()
        
        self.blocks_table.setRowCount(len(blocks))
        
        for row, block in enumerate(blocks):
            self.blocks_table.setItem(row, 0, QTableWidgetItem(block['block_number']))
            self.blocks_table.setItem(row, 1, QTableWidgetItem(block['granite_type']))
            self.blocks_table.setItem(row, 2, QTableWidgetItem(f"{block['length']:.2f}"))
            self.blocks_table.setItem(row, 3, QTableWidgetItem(f"{block['width']:.2f}"))
            self.blocks_table.setItem(row, 4, QTableWidgetItem(f"{block['height']:.2f}"))
            
            # Add button
            add_btn = QPushButton("إضافة / Add")
            add_btn.setFixedSize(80, 25)
            add_btn.clicked.connect(lambda checked, bid=block['id']: self.add_block_to_plan(bid))
            self.blocks_table.setCellWidget(row, 5, add_btn)
            
        conn.close()
        
    def add_block_to_plan(self, block_id):
        """Add block to production plan"""
        dialog = PlanItemDialog(self.db_manager, block_id)
        if dialog.exec_() == QDialog.Accepted:
            item_data = dialog.get_item_data()
            item_data['block_id'] = block_id
            self.plan_items.append(item_data)
            self.update_items_table()
            
    def update_items_table(self):
        """Update plan items table"""
        self.items_table.setRowCount(len(self.plan_items))
        
        for row, item in enumerate(self.plan_items):
            # Get block info
            conn = self.db_manager.get_connection()
            cursor = conn.cursor()
            cursor.execute("SELECT block_number FROM blocks WHERE id = ?", (item['block_id'],))
            block = cursor.fetchone()
            conn.close()
            
            self.items_table.setItem(row, 0, QTableWidgetItem(block['block_number'] if block else ''))
            self.items_table.setItem(row, 1, QTableWidgetItem(str(item['target_slabs'])))
            self.items_table.setItem(row, 2, QTableWidgetItem(f"{item['slab_length']:.2f}"))
            self.items_table.setItem(row, 3, QTableWidgetItem(f"{item['slab_width']:.2f}"))
            self.items_table.setItem(row, 4, QTableWidgetItem(f"{item['slab_thickness']:.2f}"))
            self.items_table.setItem(row, 5, QTableWidgetItem(f"{item['estimated_waste']:.1f}%"))
            
            # Remove button
            remove_btn = QPushButton("حذف / Remove")
            remove_btn.setFixedSize(80, 25)
            remove_btn.setStyleSheet("background-color: #dc3545;")
            remove_btn.clicked.connect(lambda checked, r=row: self.remove_item(r))
            self.items_table.setCellWidget(row, 6, remove_btn)
            
    def remove_item(self, row):
        """Remove item from plan"""
        if 0 <= row < len(self.plan_items):
            self.plan_items.pop(row)
            self.update_items_table()
            
    def save_plan(self):
        """Save production plan"""
        if not self.plan_items:
            QMessageBox.warning(self, "تحذير / Warning", 
                              "يرجى إضافة عناصر للخطة\nPlease add items to the plan")
            return
            
        conn = self.db_manager.get_connection()
        cursor = conn.cursor()
        
        try:
            # Insert plan
            cursor.execute("""
                INSERT INTO production_plans 
                (plan_number, plan_date, priority, notes, status, created_by)
                VALUES (?, ?, ?, ?, 'draft', ?)
            """, (self.plan_number_input.text(),
                 self.plan_date_input.date().toString("yyyy-MM-dd"),
                 self.priority_input.value(),
                 self.notes_input.toPlainText(),
                 self.user_data['id']))
            
            plan_id = cursor.lastrowid
            
            # Insert plan items
            for item in self.plan_items:
                cursor.execute("""
                    INSERT INTO production_plan_items 
                    (plan_id, block_id, target_slabs, slab_length, slab_width, 
                     slab_thickness, estimated_waste)
                    VALUES (?, ?, ?, ?, ?, ?, ?)
                """, (plan_id, item['block_id'], item['target_slabs'],
                     item['slab_length'], item['slab_width'], 
                     item['slab_thickness'], item['estimated_waste']))
            
            conn.commit()
            
            QMessageBox.information(self, "نجح / Success", 
                                  "تم حفظ خطة الإنتاج بنجاح\nProduction plan saved successfully")
            
            self.accept()
            
        except Exception as e:
            QMessageBox.critical(self, "خطأ / Error", f"Error saving plan: {str(e)}")
        finally:
            conn.close()

class PlanItemDialog(QDialog):
    def __init__(self, db_manager, block_id):
        super().__init__()
        self.db_manager = db_manager
        self.block_id = block_id
        self.init_ui()
        self.load_block_info()
        
    def init_ui(self):
        self.setWindowTitle("عنصر خطة الإنتاج / Plan Item")
        self.setFixedSize(400, 300)
        
        layout = QFormLayout()
        
        # Block info (read-only)
        self.block_info_label = QLabel()
        layout.addRow("معلومات الكتلة / Block Info:", self.block_info_label)
        
        # Target slabs
        self.target_slabs_input = QSpinBox()
        self.target_slabs_input.setRange(1, 1000)
        self.target_slabs_input.setValue(10)
        layout.addRow("عدد البلاطات المستهدف / Target Slabs:", self.target_slabs_input)
        
        # Slab dimensions
        self.slab_length_input = QDoubleSpinBox()
        self.slab_length_input.setRange(0.1, 10.0)
        self.slab_length_input.setSuffix(" م")
        self.slab_length_input.setDecimals(2)
        layout.addRow("طول البلاطة / Slab Length:", self.slab_length_input)
        
        self.slab_width_input = QDoubleSpinBox()
        self.slab_width_input.setRange(0.1, 10.0)
        self.slab_width_input.setSuffix(" م")
        self.slab_width_input.setDecimals(2)
        layout.addRow("عرض البلاطة / Slab Width:", self.slab_width_input)
        
        self.slab_thickness_input = QDoubleSpinBox()
        self.slab_thickness_input.setRange(1.0, 10.0)
        self.slab_thickness_input.setSuffix(" سم")
        self.slab_thickness_input.setDecimals(1)
        self.slab_thickness_input.setValue(2.0)
        layout.addRow("سماكة البلاطة / Slab Thickness:", self.slab_thickness_input)
        
        # Estimated waste
        self.estimated_waste_input = QDoubleSpinBox()
        self.estimated_waste_input.setRange(0.0, 50.0)
        self.estimated_waste_input.setSuffix(" %")
        self.estimated_waste_input.setDecimals(1)
        self.estimated_waste_input.setValue(5.0)
        layout.addRow("الفاقد المتوقع / Estimated Waste:", self.estimated_waste_input)
        
        # Buttons
        buttons_layout = QHBoxLayout()
        
        ok_btn = QPushButton("موافق / OK")
        ok_btn.clicked.connect(self.accept)
        
        cancel_btn = QPushButton("إلغاء / Cancel")
        cancel_btn.clicked.connect(self.reject)
        
        buttons_layout.addWidget(ok_btn)
        buttons_layout.addWidget(cancel_btn)
        
        layout.addRow(buttons_layout)
        
        self.setLayout(layout)
        
    def load_block_info(self):
        """Load block information"""
        conn = self.db_manager.get_connection()
        cursor = conn.cursor()
        
        cursor.execute("""
            SELECT block_number, granite_type, length, width, height
            FROM blocks WHERE id = ?
        """, (self.block_id,))
        
        block = cursor.fetchone()
        
        if block:
            info_text = f"{block['block_number']} - {block['granite_type']}\n"
            info_text += f"{block['length']} × {block['width']} × {block['height']} م"
            self.block_info_label.setText(info_text)
            
            # Set default slab dimensions based on block
            self.slab_length_input.setValue(min(block['length'], 3.0))
            self.slab_width_input.setValue(min(block['width'], 2.0))
            
        conn.close()
        
    def get_item_data(self):
        """Get item data"""
        return {
            'target_slabs': self.target_slabs_input.value(),
            'slab_length': self.slab_length_input.value(),
            'slab_width': self.slab_width_input.value(),
            'slab_thickness': self.slab_thickness_input.value(),
            'estimated_waste': self.estimated_waste_input.value()
        }

class CuttingOptimizer:
    """Cutting optimization algorithm"""
    
    def __init__(self, db_manager):
        self.db_manager = db_manager
        
    def optimize_plan(self, plan_id):
        """Optimize cutting plan to minimize waste"""
        conn = self.db_manager.get_connection()
        cursor = conn.cursor()
        
        # Get plan items
        cursor.execute("""
            SELECT ppi.*, b.length as block_length, b.width as block_width, b.height as block_height
            FROM production_plan_items ppi
            JOIN blocks b ON ppi.block_id = b.id
            WHERE ppi.plan_id = ?
        """, (plan_id,))
        
        items = cursor.fetchall()
        
        total_waste_before = 0
        total_waste_after = 0
        
        for item in items:
            # Calculate current waste
            block_volume = item['block_length'] * item['block_width'] * item['block_height']
            slab_volume = (item['slab_length'] * item['slab_width'] * 
                          item['slab_thickness'] / 100) * item['target_slabs']
            
            current_waste = ((block_volume - slab_volume) / block_volume) * 100
            total_waste_before += current_waste
            
            # Optimize cutting pattern
            optimized = self.optimize_cutting_pattern(
                item['block_length'], item['block_width'], item['block_height'],
                item['slab_length'], item['slab_width'], item['slab_thickness'] / 100
            )
            
            if optimized:
                # Update plan item with optimized values
                cursor.execute("""
                    UPDATE production_plan_items 
                    SET target_slabs = ?, estimated_waste = ?
                    WHERE id = ?
                """, (optimized['target_slabs'], optimized['waste_percent'], item['id']))
                
                total_waste_after += optimized['waste_percent']
            else:
                total_waste_after += current_waste
                
        conn.commit()
        conn.close()
        
        if total_waste_before > 0:
            waste_reduction = ((total_waste_before - total_waste_after) / total_waste_before) * 100
            return {'waste_reduction': waste_reduction}
        
        return None
        
    def optimize_cutting_pattern(self, block_length, block_width, block_height, 
                                slab_length, slab_width, slab_thickness):
        """Optimize cutting pattern for a single block"""
        
        # Calculate how many slabs can fit in each dimension
        slabs_length = int(block_length / slab_length)
        slabs_width = int(block_width / slab_width)
        slabs_height = int(block_height / slab_thickness)
        
        # Try different orientations
        orientations = [
            (slabs_length, slabs_width, slabs_height),
            (slabs_width, slabs_length, slabs_height),
            (int(block_length / slab_width), int(block_width / slab_length), slabs_height)
        ]
        
        best_yield = 0
        best_config = None
        
        for orientation in orientations:
            total_slabs = orientation[0] * orientation[1] * orientation[2]
            if total_slabs > best_yield:
                best_yield = total_slabs
                best_config = orientation
                
        if best_config:
            block_volume = block_length * block_width * block_height
            used_volume = (best_yield * slab_length * slab_width * slab_thickness)
            waste_percent = ((block_volume - used_volume) / block_volume) * 100
            
            return {
                'target_slabs': best_yield,
                'waste_percent': waste_percent
            }
            
        return None
