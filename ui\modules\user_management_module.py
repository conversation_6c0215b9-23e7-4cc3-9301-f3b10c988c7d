from PyQt5.QtWidgets import (QWidget, QVBoxLayout, QHBoxLayout, QTableWidget, 
                            QTableWidgetItem, QPushButton, QLineEdit, QLabel,
                            QDialog, QFormLayout, QMessageBox, QHeaderView,
                            QComboBox, QCheckBox, QGroupBox, QListWidget,
                            QListWidgetItem, QTabWidget, QTextEdit)
from PyQt5.QtCore import Qt, pyqtSignal
from PyQt5.QtGui import QFont, QColor
from datetime import datetime
import hashlib
import json

class UserManagementModule(QWidget):
    def __init__(self, db_manager, user_data):
        super().__init__()
        self.db_manager = db_manager
        self.user_data = user_data
        self.init_ui()
        self.load_users()
        
    def init_ui(self):
        layout = QVBoxLayout()
        
        # Header
        header_layout = QHBoxLayout()
        
        title = QLabel("إدارة المستخدمين / User Management")
        title.setStyleSheet("font-size: 16pt; font-weight: bold; color: #0078d4;")
        header_layout.addWidget(title)
        
        header_layout.addStretch()
        
        # New user button
        new_user_btn = QPushButton("مستخدم جديد / New User")
        new_user_btn.setStyleSheet("background-color: #28a745;")
        new_user_btn.clicked.connect(self.create_new_user)
        header_layout.addWidget(new_user_btn)
        
        # Roles management button
        roles_btn = QPushButton("إدارة الأدوار / Manage Roles")
        roles_btn.setStyleSheet("background-color: #17a2b8;")
        roles_btn.clicked.connect(self.manage_roles)
        header_layout.addWidget(roles_btn)
        
        layout.addLayout(header_layout)
        
        # Create tabs
        self.tab_widget = QTabWidget()
        
        # Users tab
        users_tab = self.create_users_tab()
        self.tab_widget.addTab(users_tab, "المستخدمون / Users")
        
        # Activity logs tab
        activity_tab = self.create_activity_tab()
        self.tab_widget.addTab(activity_tab, "سجل النشاط / Activity Log")
        
        # System settings tab
        settings_tab = self.create_settings_tab()
        self.tab_widget.addTab(settings_tab, "إعدادات النظام / System Settings")
        
        layout.addWidget(self.tab_widget)
        
        self.setLayout(layout)
        
    def create_users_tab(self):
        """Create users management tab"""
        widget = QWidget()
        layout = QVBoxLayout(widget)
        
        # Filter controls
        filter_layout = QHBoxLayout()
        
        role_label = QLabel("الدور / Role:")
        self.role_filter = QComboBox()
        self.role_filter.addItems([
            "الكل / All", "مدير / Admin", "مستخدم / User", 
            "فني / Technician", "مفتش جودة / Quality Inspector"
        ])
        self.role_filter.currentTextChanged.connect(self.filter_users)
        
        status_label = QLabel("الحالة / Status:")
        self.status_filter = QComboBox()
        self.status_filter.addItems([
            "الكل / All", "نشط / Active", "غير نشط / Inactive", "مقفل / Locked"
        ])
        self.status_filter.currentTextChanged.connect(self.filter_users)
        
        filter_layout.addWidget(role_label)
        filter_layout.addWidget(self.role_filter)
        filter_layout.addWidget(status_label)
        filter_layout.addWidget(self.status_filter)
        filter_layout.addStretch()
        
        layout.addLayout(filter_layout)
        
        # Users table
        self.users_table = QTableWidget()
        self.users_table.setColumnCount(7)
        self.users_table.setHorizontalHeaderLabels([
            "اسم المستخدم / Username", "الاسم الكامل / Full Name", 
            "الدور / Role", "آخر دخول / Last Login", "محاولات فاشلة / Failed Attempts",
            "الحالة / Status", "الإجراءات / Actions"
        ])
        
        # Set column widths
        header = self.users_table.horizontalHeader()
        header.setSectionResizeMode(QHeaderView.Stretch)
        
        layout.addWidget(self.users_table)
        
        return widget
        
    def create_activity_tab(self):
        """Create activity log tab"""
        widget = QWidget()
        layout = QVBoxLayout(widget)
        
        # Filter controls
        filter_layout = QHBoxLayout()
        
        user_label = QLabel("المستخدم / User:")
        self.activity_user_filter = QComboBox()
        self.load_users_filter()
        
        action_label = QLabel("الإجراء / Action:")
        self.activity_action_filter = QLineEdit()
        self.activity_action_filter.setPlaceholderText("البحث في الإجراءات / Search actions")
        
        refresh_btn = QPushButton("تحديث / Refresh")
        refresh_btn.clicked.connect(self.load_activity_logs)
        
        filter_layout.addWidget(user_label)
        filter_layout.addWidget(self.activity_user_filter)
        filter_layout.addWidget(action_label)
        filter_layout.addWidget(self.activity_action_filter)
        filter_layout.addWidget(refresh_btn)
        filter_layout.addStretch()
        
        layout.addLayout(filter_layout)
        
        # Activity logs table
        self.activity_table = QTableWidget()
        self.activity_table.setColumnCount(6)
        self.activity_table.setHorizontalHeaderLabels([
            "المستخدم / User", "الإجراء / Action", "الجدول / Table",
            "عنوان IP / IP Address", "التوقيت / Timestamp", "التفاصيل / Details"
        ])
        
        # Set column widths
        header = self.activity_table.horizontalHeader()
        header.setSectionResizeMode(QHeaderView.Stretch)
        
        layout.addWidget(self.activity_table)
        
        return widget
        
    def create_settings_tab(self):
        """Create system settings tab"""
        widget = QWidget()
        layout = QVBoxLayout(widget)
        
        # Company information
        company_group = QGroupBox("معلومات الشركة / Company Information")
        company_layout = QFormLayout(company_group)
        
        self.company_name_input = QLineEdit()
        company_layout.addRow("اسم الشركة / Company Name:", self.company_name_input)
        
        self.company_address_input = QTextEdit()
        self.company_address_input.setMaximumHeight(60)
        company_layout.addRow("العنوان / Address:", self.company_address_input)
        
        self.company_phone_input = QLineEdit()
        company_layout.addRow("الهاتف / Phone:", self.company_phone_input)
        
        self.company_email_input = QLineEdit()
        company_layout.addRow("البريد الإلكتروني / Email:", self.company_email_input)
        
        layout.addWidget(company_group)
        
        # Security settings
        security_group = QGroupBox("إعدادات الأمان / Security Settings")
        security_layout = QFormLayout(security_group)
        
        self.session_timeout_input = QLineEdit()
        security_layout.addRow("انتهاء الجلسة (دقيقة) / Session Timeout (minutes):", self.session_timeout_input)
        
        self.max_login_attempts_input = QLineEdit()
        security_layout.addRow("محاولات الدخول القصوى / Max Login Attempts:", self.max_login_attempts_input)
        
        self.lockout_duration_input = QLineEdit()
        security_layout.addRow("مدة القفل (دقيقة) / Lockout Duration (minutes):", self.lockout_duration_input)
        
        layout.addWidget(security_group)
        
        # Financial settings
        financial_group = QGroupBox("الإعدادات المالية / Financial Settings")
        financial_layout = QFormLayout(financial_group)
        
        self.tax_rate_input = QLineEdit()
        financial_layout.addRow("معدل الضريبة / Tax Rate:", self.tax_rate_input)
        
        self.currency_symbol_input = QLineEdit()
        financial_layout.addRow("رمز العملة / Currency Symbol:", self.currency_symbol_input)
        
        layout.addWidget(financial_group)
        
        # Backup settings
        backup_group = QGroupBox("إعدادات النسخ الاحتياطي / Backup Settings")
        backup_layout = QFormLayout(backup_group)
        
        self.backup_frequency_combo = QComboBox()
        self.backup_frequency_combo.addItems([
            "يومي / Daily", "أسبوعي / Weekly", "شهري / Monthly"
        ])
        backup_layout.addRow("تكرار النسخ الاحتياطي / Backup Frequency:", self.backup_frequency_combo)
        
        layout.addWidget(backup_group)
        
        # Save settings button
        save_settings_btn = QPushButton("حفظ الإعدادات / Save Settings")
        save_settings_btn.clicked.connect(self.save_system_settings)
        layout.addWidget(save_settings_btn)
        
        # Load current settings
        self.load_system_settings()
        
        return widget
        
    def load_users_filter(self):
        """Load users for activity filter"""
        conn = self.db_manager.get_connection()
        cursor = conn.cursor()
        
        cursor.execute("SELECT id, full_name FROM users ORDER BY full_name")
        users = cursor.fetchall()
        
        self.activity_user_filter.addItem("الكل / All", None)
        for user in users:
            self.activity_user_filter.addItem(user['full_name'], user['id'])
            
        conn.close()
        
    def load_users(self):
        """Load users from database"""
        conn = self.db_manager.get_connection()
        cursor = conn.cursor()
        
        cursor.execute("""
            SELECT id, username, full_name, role, last_login, 
                   failed_login_attempts, is_active, locked_until
            FROM users
            ORDER BY created_at DESC
        """)
        
        users = cursor.fetchall()
        
        self.users_table.setRowCount(len(users))
        
        for row, user in enumerate(users):
            self.users_table.setItem(row, 0, QTableWidgetItem(user['username']))
            self.users_table.setItem(row, 1, QTableWidgetItem(user['full_name']))
            self.users_table.setItem(row, 2, QTableWidgetItem(user['role']))
            self.users_table.setItem(row, 3, QTableWidgetItem(user['last_login'] or 'لم يدخل / Never'))
            self.users_table.setItem(row, 4, QTableWidgetItem(str(user['failed_login_attempts'])))
            
            # Status with color coding
            if user['locked_until'] and datetime.now() < datetime.fromisoformat(user['locked_until']):
                status = "مقفل / Locked"
                status_color = QColor('#dc3545')
            elif user['is_active']:
                status = "نشط / Active"
                status_color = QColor('#28a745')
            else:
                status = "غير نشط / Inactive"
                status_color = QColor('#6c757d')
                
            status_item = QTableWidgetItem(status)
            status_item.setBackground(status_color)
            self.users_table.setItem(row, 5, status_item)
            
            # Action buttons
            actions_widget = QWidget()
            actions_layout = QHBoxLayout(actions_widget)
            actions_layout.setContentsMargins(5, 5, 5, 5)
            
            edit_btn = QPushButton("تعديل / Edit")
            edit_btn.setFixedSize(70, 25)
            edit_btn.clicked.connect(lambda checked, uid=user['id']: self.edit_user(uid))
            
            reset_btn = QPushButton("إعادة تعيين / Reset")
            reset_btn.setFixedSize(90, 25)
            reset_btn.setStyleSheet("background-color: #ffc107;")
            reset_btn.clicked.connect(lambda checked, uid=user['id']: self.reset_password(uid))
            
            actions_layout.addWidget(edit_btn)
            actions_layout.addWidget(reset_btn)
            
            self.users_table.setCellWidget(row, 6, actions_widget)
            
        conn.close()
        
    def load_activity_logs(self):
        """Load activity logs from database"""
        conn = self.db_manager.get_connection()
        cursor = conn.cursor()
        
        cursor.execute("""
            SELECT al.id, u.full_name, al.action, al.table_name, 
                   al.ip_address, al.timestamp, al.old_values, al.new_values
            FROM activity_logs al
            LEFT JOIN users u ON al.user_id = u.id
            ORDER BY al.timestamp DESC
            LIMIT 1000
        """)
        
        logs = cursor.fetchall()
        
        self.activity_table.setRowCount(len(logs))
        
        for row, log in enumerate(logs):
            self.activity_table.setItem(row, 0, QTableWidgetItem(log['full_name'] or 'نظام / System'))
            self.activity_table.setItem(row, 1, QTableWidgetItem(log['action']))
            self.activity_table.setItem(row, 2, QTableWidgetItem(log['table_name'] or '-'))
            self.activity_table.setItem(row, 3, QTableWidgetItem(log['ip_address'] or '-'))
            self.activity_table.setItem(row, 4, QTableWidgetItem(log['timestamp']))
            
            # Details button
            details_btn = QPushButton("التفاصيل / Details")
            details_btn.setFixedSize(80, 25)
            details_btn.clicked.connect(lambda checked, lid=log['id']: self.view_log_details(lid))
            self.activity_table.setCellWidget(row, 5, details_btn)
            
        conn.close()
        
    def load_system_settings(self):
        """Load system settings"""
        settings = {
            'company_name': self.company_name_input,
            'company_address': self.company_address_input,
            'company_phone': self.company_phone_input,
            'company_email': self.company_email_input,
            'session_timeout': self.session_timeout_input,
            'max_login_attempts': self.max_login_attempts_input,
            'lockout_duration': self.lockout_duration_input,
            'tax_rate': self.tax_rate_input,
            'currency_symbol': self.currency_symbol_input
        }
        
        for key, widget in settings.items():
            value = self.db_manager.get_system_setting(key, '')
            if isinstance(widget, QTextEdit):
                widget.setPlainText(str(value))
            else:
                widget.setText(str(value))
                
        # Backup frequency
        backup_freq = self.db_manager.get_system_setting('backup_frequency', 'daily')
        freq_map = {'daily': 0, 'weekly': 1, 'monthly': 2}
        self.backup_frequency_combo.setCurrentIndex(freq_map.get(backup_freq, 0))
        
    def create_new_user(self):
        """Create new user"""
        dialog = UserDialog(self.db_manager)
        if dialog.exec_() == QDialog.Accepted:
            self.load_users()
            
    def edit_user(self, user_id):
        """Edit user"""
        dialog = UserDialog(self.db_manager, user_id)
        if dialog.exec_() == QDialog.Accepted:
            self.load_users()
            
    def reset_password(self, user_id):
        """Reset user password"""
        reply = QMessageBox.question(self, "إعادة تعيين كلمة المرور / Reset Password",
                                   "هل تريد إعادة تعيين كلمة المرور إلى القيمة الافتراضية؟\n"
                                   "Do you want to reset password to default?",
                                   QMessageBox.Yes | QMessageBox.No)
        
        if reply == QMessageBox.Yes:
            conn = self.db_manager.get_connection()
            cursor = conn.cursor()
            
            try:
                # Reset password to 'password123'
                default_password = 'password123'
                password_hash = hashlib.sha256(default_password.encode()).hexdigest()
                
                cursor.execute("""
                    UPDATE users 
                    SET password_hash = ?, failed_login_attempts = 0, locked_until = NULL
                    WHERE id = ?
                """, (password_hash, user_id))
                
                conn.commit()
                
                QMessageBox.information(self, "نجح / Success", 
                                      f"تم إعادة تعيين كلمة المرور بنجاح\n"
                                      f"Password reset successfully\n"
                                      f"كلمة المرور الجديدة: {default_password}")
                
                self.load_users()
                
            except Exception as e:
                QMessageBox.critical(self, "خطأ / Error", f"Error resetting password: {str(e)}")
            finally:
                conn.close()
                
    def manage_roles(self):
        """Open roles management dialog"""
        dialog = RolesManagementDialog(self.db_manager)
        dialog.exec_()
        
    def filter_users(self):
        """Filter users by role and status"""
        role_filter = self.role_filter.currentText()
        status_filter = self.status_filter.currentText()
        
        for row in range(self.users_table.rowCount()):
            show_row = True
            
            # Filter by role
            if not ("الكل" in role_filter or "All" in role_filter):
                role_item = self.users_table.item(row, 2)
                if role_item:
                    if not any(keyword in role_filter.lower() 
                             for keyword in role_item.text().lower().split()):
                        show_row = False
                        
            # Filter by status
            if show_row and not ("الكل" in status_filter or "All" in status_filter):
                status_item = self.users_table.item(row, 5)
                if status_item:
                    if not any(keyword in status_filter.lower() 
                             for keyword in status_item.text().lower().split()):
                        show_row = False
                        
            self.users_table.setRowHidden(row, not show_row)
            
    def view_log_details(self, log_id):
        """View activity log details"""
        dialog = ActivityLogDetailsDialog(self.db_manager, log_id)
        dialog.exec_()
        
    def save_system_settings(self):
        """Save system settings"""
        try:
            # Save all settings
            self.db_manager.set_system_setting('company_name', self.company_name_input.text(), user_id=self.user_data['id'])
            self.db_manager.set_system_setting('company_address', self.company_address_input.toPlainText(), user_id=self.user_data['id'])
            self.db_manager.set_system_setting('company_phone', self.company_phone_input.text(), user_id=self.user_data['id'])
            self.db_manager.set_system_setting('company_email', self.company_email_input.text(), user_id=self.user_data['id'])
            self.db_manager.set_system_setting('session_timeout', self.session_timeout_input.text(), 'integer', user_id=self.user_data['id'])
            self.db_manager.set_system_setting('max_login_attempts', self.max_login_attempts_input.text(), 'integer', user_id=self.user_data['id'])
            self.db_manager.set_system_setting('lockout_duration', self.lockout_duration_input.text(), 'integer', user_id=self.user_data['id'])
            self.db_manager.set_system_setting('tax_rate', self.tax_rate_input.text(), 'float', user_id=self.user_data['id'])
            self.db_manager.set_system_setting('currency_symbol', self.currency_symbol_input.text(), user_id=self.user_data['id'])
            
            # Backup frequency
            freq_map = ['daily', 'weekly', 'monthly']
            backup_freq = freq_map[self.backup_frequency_combo.currentIndex()]
            self.db_manager.set_system_setting('backup_frequency', backup_freq, user_id=self.user_data['id'])
            
            QMessageBox.information(self, "نجح / Success", 
                                  "تم حفظ الإعدادات بنجاح\nSettings saved successfully")
            
        except Exception as e:
            QMessageBox.critical(self, "خطأ / Error", f"Error saving settings: {str(e)}")

class UserDialog(QDialog):
    def __init__(self, db_manager, user_id=None):
        super().__init__()
        self.db_manager = db_manager
        self.user_id = user_id
        self.init_ui()
        
        if user_id:
            self.load_user_data()
            
    def init_ui(self):
        self.setWindowTitle("مستخدم / User")
        self.setFixedSize(500, 600)
        
        layout = QVBoxLayout()
        
        # User information
        info_group = QGroupBox("معلومات المستخدم / User Information")
        info_layout = QFormLayout(info_group)
        
        self.username_input = QLineEdit()
        self.username_input.setPlaceholderText("اسم المستخدم / Username")
        info_layout.addRow("اسم المستخدم / Username:", self.username_input)
        
        self.full_name_input = QLineEdit()
        self.full_name_input.setPlaceholderText("الاسم الكامل / Full name")
        info_layout.addRow("الاسم الكامل / Full Name:", self.full_name_input)
        
        self.role_combo = QComboBox()
        self.role_combo.addItems([
            "مستخدم / User", "مدير / Admin", "فني / Technician", 
            "مفتش جودة / Quality Inspector", "محاسب / Accountant"
        ])
        info_layout.addRow("الدور / Role:", self.role_combo)
        
        self.is_active_checkbox = QCheckBox("نشط / Active")
        self.is_active_checkbox.setChecked(True)
        info_layout.addRow("الحالة / Status:", self.is_active_checkbox)
        
        layout.addWidget(info_group)
        
        # Password section (only for new users)
        if not self.user_id:
            password_group = QGroupBox("كلمة المرور / Password")
            password_layout = QFormLayout(password_group)
            
            self.password_input = QLineEdit()
            self.password_input.setEchoMode(QLineEdit.Password)
            self.password_input.setPlaceholderText("كلمة المرور / Password")
            password_layout.addRow("كلمة المرور / Password:", self.password_input)
            
            self.confirm_password_input = QLineEdit()
            self.confirm_password_input.setEchoMode(QLineEdit.Password)
            self.confirm_password_input.setPlaceholderText("تأكيد كلمة المرور / Confirm password")
            password_layout.addRow("تأكيد كلمة المرور / Confirm Password:", self.confirm_password_input)
            
            layout.addWidget(password_group)
        
        # Permissions section
        permissions_group = QGroupBox("الصلاحيات / Permissions")
        permissions_layout = QVBoxLayout(permissions_group)
        
        self.permissions_list = QListWidget()
        self.permissions_list.setSelectionMode(QListWidget.MultiSelection)
        
        # Add permission items
        permissions = [
            ("customers", "إدارة العملاء / Manage Customers"),
            ("suppliers", "إدارة الموردين / Manage Suppliers"),
            ("trucks", "إدارة الشاحنات / Manage Trucks"),
            ("blocks", "إدارة الكتل / Manage Blocks"),
            ("slabs", "إدارة البلاطات / Manage Slabs"),
            ("sales", "إدارة المبيعات / Manage Sales"),
            ("inventory", "إدارة المخزون / Manage Inventory"),
            ("expenses", "إدارة المصروفات / Manage Expenses"),
            ("reports", "التقارير / Reports"),
            ("production", "تخطيط الإنتاج / Production Planning"),
            ("procurement", "المشتريات / Procurement"),
            ("quality", "مراقبة الجودة / Quality Control"),
            ("maintenance", "الصيانة / Maintenance"),
            ("quotations", "عروض الأسعار / Quotations"),
            ("users", "إدارة المستخدمين / User Management"),
            ("settings", "إعدادات النظام / System Settings")
        ]
        
        for perm_key, perm_name in permissions:
            item = QListWidgetItem(perm_name)
            item.setData(Qt.UserRole, perm_key)
            self.permissions_list.addItem(item)
            
        permissions_layout.addWidget(self.permissions_list)
        
        layout.addWidget(permissions_group)
        
        # Buttons
        buttons_layout = QHBoxLayout()
        
        save_btn = QPushButton("حفظ / Save")
        save_btn.clicked.connect(self.save_user)
        
        cancel_btn = QPushButton("إلغاء / Cancel")
        cancel_btn.clicked.connect(self.reject)
        
        buttons_layout.addWidget(save_btn)
        buttons_layout.addWidget(cancel_btn)
        
        layout.addLayout(buttons_layout)
        
        self.setLayout(layout)
        
    def load_user_data(self):
        """Load existing user data"""
        conn = self.db_manager.get_connection()
        cursor = conn.cursor()
        
        cursor.execute("SELECT * FROM users WHERE id = ?", (self.user_id,))
        user = cursor.fetchone()
        
        if user:
            self.username_input.setText(user['username'])
            self.full_name_input.setText(user['full_name'])
            
            # Set role
            role_map = {
                'user': 0,
                'admin': 1,
                'technician': 2,
                'quality_inspector': 3,
                'accountant': 4
            }
            self.role_combo.setCurrentIndex(role_map.get(user['role'], 0))
            
            self.is_active_checkbox.setChecked(user['is_active'])
            
            # Load permissions
            if user['permissions']:
                try:
                    permissions = json.loads(user['permissions'])
                    for i in range(self.permissions_list.count()):
                        item = self.permissions_list.item(i)
                        perm_key = item.data(Qt.UserRole)
                        if perm_key in permissions:
                            item.setSelected(True)
                except:
                    pass
                    
        conn.close()
        
    def save_user(self):
        """Save user data"""
        username = self.username_input.text().strip()
        full_name = self.full_name_input.text().strip()
        
        if not username:
            QMessageBox.warning(self, "تحذير / Warning", "يرجى إدخال اسم المستخدم\nPlease enter username")
            return
            
        if not full_name:
            QMessageBox.warning(self, "تحذير / Warning", "يرجى إدخال الاسم الكامل\nPlease enter full name")
            return
            
        # Validate password for new users
        if not self.user_id:
            password = self.password_input.text()
            confirm_password = self.confirm_password_input.text()
            
            if not password:
                QMessageBox.warning(self, "تحذير / Warning", "يرجى إدخال كلمة المرور\nPlease enter password")
                return
                
            if password != confirm_password:
                QMessageBox.warning(self, "تحذير / Warning", "كلمات المرور غير متطابقة\nPasswords do not match")
                return
                
        # Get selected permissions
        selected_permissions = []
        for i in range(self.permissions_list.count()):
            item = self.permissions_list.item(i)
            if item.isSelected():
                selected_permissions.append(item.data(Qt.UserRole))
                
        conn = self.db_manager.get_connection()
        cursor = conn.cursor()
        
        try:
            role_map = ['user', 'admin', 'technician', 'quality_inspector', 'accountant']
            role = role_map[self.role_combo.currentIndex()]
            
            if self.user_id:
                # Update existing user
                cursor.execute("""
                    UPDATE users 
                    SET username = ?, full_name = ?, role = ?, permissions = ?, is_active = ?
                    WHERE id = ?
                """, (username, full_name, role, json.dumps(selected_permissions),
                     self.is_active_checkbox.isChecked(), self.user_id))
            else:
                # Add new user
                password_hash = hashlib.sha256(self.password_input.text().encode()).hexdigest()
                
                cursor.execute("""
                    INSERT INTO users 
                    (username, password_hash, full_name, role, permissions, is_active)
                    VALUES (?, ?, ?, ?, ?, ?)
                """, (username, password_hash, full_name, role, 
                     json.dumps(selected_permissions), self.is_active_checkbox.isChecked()))
                
            conn.commit()
            self.accept()
            
        except Exception as e:
            QMessageBox.critical(self, "خطأ / Error", f"Error saving user: {str(e)}")
        finally:
            conn.close()

class RolesManagementDialog(QDialog):
    def __init__(self, db_manager):
        super().__init__()
        self.db_manager = db_manager
        self.init_ui()
        
    def init_ui(self):
        self.setWindowTitle("إدارة الأدوار / Roles Management")
        self.setFixedSize(600, 400)
        
        layout = QVBoxLayout()
        
        info_label = QLabel("إدارة أدوار المستخدمين وصلاحياتهم\nManage user roles and their permissions")
        info_label.setStyleSheet("background-color: #404040; padding: 10px; border-radius: 4px;")
        layout.addWidget(info_label)
        
        # Roles information
        roles_info = QTextEdit()
        roles_info.setReadOnly(True)
        roles_info.setPlainText("""
الأدوار المتاحة / Available Roles:

1. مدير / Admin
   - صلاحيات كاملة لجميع وظائف النظام
   - Full access to all system functions

2. مستخدم / User  
   - صلاحيات أساسية للعمليات اليومية
   - Basic permissions for daily operations

3. فني / Technician
   - صلاحيات الصيانة والإنتاج
   - Maintenance and production permissions

4. مفتش جودة / Quality Inspector
   - صلاحيات مراقبة الجودة والفحص
   - Quality control and inspection permissions

5. محاسب / Accountant
   - صلاحيات المالية والتقارير
   - Financial and reporting permissions

يمكن تخصيص الصلاحيات لكل مستخدم بشكل فردي
Permissions can be customized for each user individually
        """)
        
        layout.addWidget(roles_info)
        
        # Close button
        close_btn = QPushButton("إغلاق / Close")
        close_btn.clicked.connect(self.accept)
        layout.addWidget(close_btn)
        
        self.setLayout(layout)

class ActivityLogDetailsDialog(QDialog):
    def __init__(self, db_manager, log_id):
        super().__init__()
        self.db_manager = db_manager
        self.log_id = log_id
        self.init_ui()
        self.load_log_details()
        
    def init_ui(self):
        self.setWindowTitle("تفاصيل سجل النشاط / Activity Log Details")
        self.setFixedSize(600, 500)
        
        layout = QVBoxLayout()
        
        # Log details
        self.details_text = QTextEdit()
        self.details_text.setReadOnly(True)
        self.details_text.setFont(QFont("Courier", 10))
        layout.addWidget(self.details_text)
        
        # Close button
        close_btn = QPushButton("إغلاق / Close")
        close_btn.clicked.connect(self.accept)
        layout.addWidget(close_btn)
        
        self.setLayout(layout)
        
    def load_log_details(self):
        """Load activity log details"""
        conn = self.db_manager.get_connection()
        cursor = conn.cursor()
        
        cursor.execute("""
            SELECT al.*, u.full_name
            FROM activity_logs al
            LEFT JOIN users u ON al.user_id = u.id
            WHERE al.id = ?
        """, (self.log_id,))
        
        log = cursor.fetchone()
        
        if log:
            details_text = f"""
تفاصيل سجل النشاط / Activity Log Details
═══════════════════════════════════════════════════════════════

المستخدم / User: {log['full_name'] or 'نظام / System'}
الإجراء / Action: {log['action']}
الجدول / Table: {log['table_name'] or '-'}
معرف السجل / Record ID: {log['record_id'] or '-'}
عنوان IP / IP Address: {log['ip_address'] or '-'}
وكيل المستخدم / User Agent: {log['user_agent'] or '-'}
التوقيت / Timestamp: {log['timestamp']}

القيم القديمة / Old Values:
─────────────────────────────────────────────────────────────
{log['old_values'] or 'لا توجد / None'}

القيم الجديدة / New Values:
─────────────────────────────────────────────────────────────
{log['new_values'] or 'لا توجد / None'}
            """
            
            self.details_text.setPlainText(details_text)
            
        conn.close()
