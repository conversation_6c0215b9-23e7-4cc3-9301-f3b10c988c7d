from PyQt5.QtCore import QDate, QDateTime, QLocale
from datetime import datetime, date
import calendar
from hijri_converter import Hij<PERSON>, Gregorian
import locale
import re

class CulturalManager:
    def __init__(self):
        self.current_culture = 'en-US'
        self.calendar_system = 'gregorian'
        self.number_format = 'western'
        self.currency_symbol = '$'
        self.date_format = 'dd/MM/yyyy'
        self.time_format = '24h'
        self.week_start = 'sunday'
        
        # Cultural preferences by region
        self.cultural_presets = {
            'ar-SA': {  # Saudi Arabia
                'calendar_system': 'hijri',
                'number_format': 'arabic-indic',
                'currency_symbol': 'ر.س',
                'date_format': 'dd/MM/yyyy',
                'time_format': '12h',
                'week_start': 'saturday',
                'decimal_separator': '.',
                'thousand_separator': ',',
                'currency_position': 'after'
            },
            'ar-EG': {  # Egypt
                'calendar_system': 'gregorian',
                'number_format': 'arabic-indic',
                'currency_symbol': 'ج.م',
                'date_format': 'dd/MM/yyyy',
                'time_format': '12h',
                'week_start': 'saturday',
                'decimal_separator': '.',
                'thousand_separator': ',',
                'currency_position': 'after'
            },
            'en-US': {  # United States
                'calendar_system': 'gregorian',
                'number_format': 'western',
                'currency_symbol': '$',
                'date_format': 'MM/dd/yyyy',
                'time_format': '12h',
                'week_start': 'sunday',
                'decimal_separator': '.',
                'thousand_separator': ',',
                'currency_position': 'before'
            },
            'en-GB': {  # United Kingdom
                'calendar_system': 'gregorian',
                'number_format': 'western',
                'currency_symbol': '£',
                'date_format': 'dd/MM/yyyy',
                'time_format': '24h',
                'week_start': 'monday',
                'decimal_separator': '.',
                'thousand_separator': ',',
                'currency_position': 'before'
            },
            'fr-FR': {  # France
                'calendar_system': 'gregorian',
                'number_format': 'western',
                'currency_symbol': '€',
                'date_format': 'dd/MM/yyyy',
                'time_format': '24h',
                'week_start': 'monday',
                'decimal_separator': ',',
                'thousand_separator': ' ',
                'currency_position': 'after'
            },
            'de-DE': {  # Germany
                'calendar_system': 'gregorian',
                'number_format': 'western',
                'currency_symbol': '€',
                'date_format': 'dd.MM.yyyy',
                'time_format': '24h',
                'week_start': 'monday',
                'decimal_separator': ',',
                'thousand_separator': '.',
                'currency_position': 'after'
            },
            'es-ES': {  # Spain
                'calendar_system': 'gregorian',
                'number_format': 'western',
                'currency_symbol': '€',
                'date_format': 'dd/MM/yyyy',
                'time_format': '24h',
                'week_start': 'monday',
                'decimal_separator': ',',
                'thousand_separator': '.',
                'currency_position': 'after'
            },
            'tr-TR': {  # Turkey
                'calendar_system': 'gregorian',
                'number_format': 'western',
                'currency_symbol': '₺',
                'date_format': 'dd.MM.yyyy',
                'time_format': '24h',
                'week_start': 'monday',
                'decimal_separator': ',',
                'thousand_separator': '.',
                'currency_position': 'after'
            },
            'ru-RU': {  # Russia
                'calendar_system': 'gregorian',
                'number_format': 'western',
                'currency_symbol': '₽',
                'date_format': 'dd.MM.yyyy',
                'time_format': '24h',
                'week_start': 'monday',
                'decimal_separator': ',',
                'thousand_separator': ' ',
                'currency_position': 'after'
            },
            'zh-CN': {  # China
                'calendar_system': 'gregorian',
                'number_format': 'chinese',
                'currency_symbol': '¥',
                'date_format': 'yyyy/MM/dd',
                'time_format': '24h',
                'week_start': 'monday',
                'decimal_separator': '.',
                'thousand_separator': ',',
                'currency_position': 'before'
            },
            'ur-PK': {  # Pakistan (Urdu)
                'calendar_system': 'hijri',
                'number_format': 'urdu',
                'currency_symbol': '₨',
                'date_format': 'dd/MM/yyyy',
                'time_format': '12h',
                'week_start': 'sunday',
                'decimal_separator': '.',
                'thousand_separator': ',',
                'currency_position': 'before'
            }
        }
        
        # Number system mappings
        self.number_systems = {
            'western': '0123456789',
            'arabic-indic': '٠١٢٣٤٥٦٧٨٩',
            'persian': '۰۱۲۳۴۵۶۷۸۹',
            'urdu': '۰۱۲۳۴۵۶۷۸۹',
            'chinese': '〇一二三四五六七八九',
            'devanagari': '०१२३४५६७८९'
        }
        
    def set_culture(self, culture_code):
        """Set cultural preferences"""
        self.current_culture = culture_code
        
        if culture_code in self.cultural_presets:
            preset = self.cultural_presets[culture_code]
            self.calendar_system = preset['calendar_system']
            self.number_format = preset['number_format']
            self.currency_symbol = preset['currency_symbol']
            self.date_format = preset['date_format']
            self.time_format = preset['time_format']
            self.week_start = preset['week_start']
            
    def get_available_cultures(self):
        """Get list of available cultures"""
        return {
            'ar-SA': 'العربية (السعودية)',
            'ar-EG': 'العربية (مصر)',
            'en-US': 'English (United States)',
            'en-GB': 'English (United Kingdom)',
            'fr-FR': 'Français (France)',
            'de-DE': 'Deutsch (Deutschland)',
            'es-ES': 'Español (España)',
            'tr-TR': 'Türkçe (Türkiye)',
            'ru-RU': 'Русский (Россия)',
            'zh-CN': '中文 (中国)',
            'ur-PK': 'اردو (پاکستان)'
        }
        
    def get_calendar_systems(self):
        """Get available calendar systems"""
        return {
            'gregorian': 'Gregorian Calendar',
            'hijri': 'Hijri Calendar (Islamic)',
            'persian': 'Persian Calendar',
            'hebrew': 'Hebrew Calendar'
        }
        
    def format_number(self, number, decimal_places=2):
        """Format number according to cultural preferences"""
        if isinstance(number, str):
            try:
                number = float(number)
            except ValueError:
                return str(number)
                
        # Get cultural settings
        preset = self.cultural_presets.get(self.current_culture, self.cultural_presets['en-US'])
        decimal_sep = preset.get('decimal_separator', '.')
        thousand_sep = preset.get('thousand_separator', ',')
        
        # Format the number
        formatted = f"{number:,.{decimal_places}f}"
        
        # Replace separators if needed
        if decimal_sep != '.' or thousand_sep != ',':
            formatted = formatted.replace(',', '|TEMP|')  # Temporary placeholder
            formatted = formatted.replace('.', decimal_sep)
            formatted = formatted.replace('|TEMP|', thousand_sep)
            
        # Convert to local number system
        if self.number_format in self.number_systems:
            western_digits = self.number_systems['western']
            local_digits = self.number_systems[self.number_format]
            
            for i, digit in enumerate(western_digits):
                formatted = formatted.replace(digit, local_digits[i])
                
        return formatted
        
    def format_currency(self, amount, show_symbol=True):
        """Format currency according to cultural preferences"""
        preset = self.cultural_presets.get(self.current_culture, self.cultural_presets['en-US'])
        
        formatted_amount = self.format_number(amount, 2)
        
        if not show_symbol:
            return formatted_amount
            
        currency_symbol = preset.get('currency_symbol', '$')
        currency_position = preset.get('currency_position', 'before')
        
        if currency_position == 'before':
            return f"{currency_symbol}{formatted_amount}"
        else:
            return f"{formatted_amount} {currency_symbol}"
            
    def format_date(self, date_obj, format_type='short'):
        """Format date according to cultural preferences"""
        if isinstance(date_obj, str):
            try:
                date_obj = datetime.strptime(date_obj, '%Y-%m-%d').date()
            except ValueError:
                return str(date_obj)
                
        if self.calendar_system == 'hijri':
            return self._format_hijri_date(date_obj, format_type)
        elif self.calendar_system == 'persian':
            return self._format_persian_date(date_obj, format_type)
        else:
            return self._format_gregorian_date(date_obj, format_type)
            
    def _format_gregorian_date(self, date_obj, format_type):
        """Format Gregorian date"""
        preset = self.cultural_presets.get(self.current_culture, self.cultural_presets['en-US'])
        date_format = preset.get('date_format', 'dd/MM/yyyy')
        
        if format_type == 'long':
            if self.current_culture.startswith('ar'):
                months_ar = [
                    'يناير', 'فبراير', 'مارس', 'أبريل', 'مايو', 'يونيو',
                    'يوليو', 'أغسطس', 'سبتمبر', 'أكتوبر', 'نوفمبر', 'ديسمبر'
                ]
                return f"{date_obj.day} {months_ar[date_obj.month-1]} {date_obj.year}"
            elif self.current_culture.startswith('fr'):
                months_fr = [
                    'janvier', 'février', 'mars', 'avril', 'mai', 'juin',
                    'juillet', 'août', 'septembre', 'octobre', 'novembre', 'décembre'
                ]
                return f"{date_obj.day} {months_fr[date_obj.month-1]} {date_obj.year}"
            elif self.current_culture.startswith('de'):
                months_de = [
                    'Januar', 'Februar', 'März', 'April', 'Mai', 'Juni',
                    'Juli', 'August', 'September', 'Oktober', 'November', 'Dezember'
                ]
                return f"{date_obj.day}. {months_de[date_obj.month-1]} {date_obj.year}"
            elif self.current_culture.startswith('es'):
                months_es = [
                    'enero', 'febrero', 'marzo', 'abril', 'mayo', 'junio',
                    'julio', 'agosto', 'septiembre', 'octubre', 'noviembre', 'diciembre'
                ]
                return f"{date_obj.day} de {months_es[date_obj.month-1]} de {date_obj.year}"
            else:
                return date_obj.strftime('%B %d, %Y')
                
        # Short format
        formatted = date_format.replace('yyyy', str(date_obj.year))
        formatted = formatted.replace('MM', f"{date_obj.month:02d}")
        formatted = formatted.replace('dd', f"{date_obj.day:02d}")
        
        # Convert numbers if needed
        if self.number_format in self.number_systems:
            western_digits = self.number_systems['western']
            local_digits = self.number_systems[self.number_format]
            
            for i, digit in enumerate(western_digits):
                formatted = formatted.replace(digit, local_digits[i])
                
        return formatted
        
    def _format_hijri_date(self, date_obj, format_type):
        """Format Hijri (Islamic) date"""
        try:
            # Convert Gregorian to Hijri
            hijri_date = Gregorian(date_obj.year, date_obj.month, date_obj.day).to_hijri()
            
            if format_type == 'long':
                hijri_months = [
                    'محرم', 'صفر', 'ربيع الأول', 'ربيع الثاني', 'جمادى الأولى', 'جمادى الثانية',
                    'رجب', 'شعبان', 'رمضان', 'شوال', 'ذو القعدة', 'ذو الحجة'
                ]
                return f"{hijri_date.day} {hijri_months[hijri_date.month-1]} {hijri_date.year} هـ"
            else:
                formatted = f"{hijri_date.day:02d}/{hijri_date.month:02d}/{hijri_date.year} هـ"
                
                # Convert to Arabic-Indic numerals
                if self.number_format == 'arabic-indic':
                    western_digits = self.number_systems['western']
                    arabic_digits = self.number_systems['arabic-indic']
                    
                    for i, digit in enumerate(western_digits):
                        formatted = formatted.replace(digit, arabic_digits[i])
                        
                return formatted
                
        except Exception:
            # Fallback to Gregorian if conversion fails
            return self._format_gregorian_date(date_obj, format_type)
            
    def _format_persian_date(self, date_obj, format_type):
        """Format Persian (Jalali) date"""
        # This is a simplified implementation
        # In a real application, you would use a proper Persian calendar library
        return self._format_gregorian_date(date_obj, format_type)
        
    def format_time(self, time_obj):
        """Format time according to cultural preferences"""
        if isinstance(time_obj, str):
            try:
                time_obj = datetime.strptime(time_obj, '%H:%M:%S').time()
            except ValueError:
                return str(time_obj)
                
        if self.time_format == '12h':
            hour = time_obj.hour
            am_pm = 'AM' if hour < 12 else 'PM'
            
            if self.current_culture.startswith('ar'):
                am_pm = 'ص' if hour < 12 else 'م'
            elif self.current_culture.startswith('ur'):
                am_pm = 'صبح' if hour < 12 else 'شام'
                
            if hour == 0:
                hour = 12
            elif hour > 12:
                hour -= 12
                
            formatted = f"{hour}:{time_obj.minute:02d} {am_pm}"
        else:
            formatted = f"{time_obj.hour:02d}:{time_obj.minute:02d}"
            
        # Convert numbers if needed
        if self.number_format in self.number_systems:
            western_digits = self.number_systems['western']
            local_digits = self.number_systems[self.number_format]
            
            for i, digit in enumerate(western_digits):
                formatted = formatted.replace(digit, local_digits[i])
                
        return formatted
        
    def get_week_start_day(self):
        """Get the first day of the week"""
        preset = self.cultural_presets.get(self.current_culture, self.cultural_presets['en-US'])
        return preset.get('week_start', 'sunday')
        
    def get_weekend_days(self):
        """Get weekend days for the culture"""
        if self.current_culture.startswith('ar') or self.current_culture.startswith('ur'):
            return ['friday', 'saturday']  # Islamic weekend
        elif self.current_culture == 'en-US':
            return ['saturday', 'sunday']  # Western weekend
        else:
            return ['saturday', 'sunday']  # Default Western weekend
            
    def parse_number(self, number_string):
        """Parse a number string according to cultural format"""
        if not number_string:
            return 0.0
            
        # Convert from local number system to western
        if self.number_format in self.number_systems:
            western_digits = self.number_systems['western']
            local_digits = self.number_systems[self.number_format]
            
            for i, local_digit in enumerate(local_digits):
                number_string = number_string.replace(local_digit, western_digits[i])
                
        # Handle cultural separators
        preset = self.cultural_presets.get(self.current_culture, self.cultural_presets['en-US'])
        decimal_sep = preset.get('decimal_separator', '.')
        thousand_sep = preset.get('thousand_separator', ',')
        
        # Remove thousand separators and convert decimal separator
        number_string = number_string.replace(thousand_sep, '')
        number_string = number_string.replace(decimal_sep, '.')
        
        try:
            return float(number_string)
        except ValueError:
            return 0.0
            
    def get_month_names(self, calendar_system=None):
        """Get month names for the specified calendar system"""
        if calendar_system is None:
            calendar_system = self.calendar_system
            
        if calendar_system == 'hijri':
            return [
                'محرم', 'صفر', 'ربيع الأول', 'ربيع الثاني', 'جمادى الأولى', 'جمادى الثانية',
                'رجب', 'شعبان', 'رمضان', 'شوال', 'ذو القعدة', 'ذو الحجة'
            ]
        elif self.current_culture.startswith('ar'):
            return [
                'يناير', 'فبراير', 'مارس', 'أبريل', 'مايو', 'يونيو',
                'يوليو', 'أغسطس', 'سبتمبر', 'أكتوبر', 'نوفمبر', 'ديسمبر'
            ]
        elif self.current_culture.startswith('fr'):
            return [
                'janvier', 'février', 'mars', 'avril', 'mai', 'juin',
                'juillet', 'août', 'septembre', 'octobre', 'novembre', 'décembre'
            ]
        elif self.current_culture.startswith('de'):
            return [
                'Januar', 'Februar', 'März', 'April', 'Mai', 'Juni',
                'Juli', 'August', 'September', 'Oktober', 'November', 'Dezember'
            ]
        elif self.current_culture.startswith('es'):
            return [
                'enero', 'febrero', 'marzo', 'abril', 'mayo', 'junio',
                'julio', 'agosto', 'septiembre', 'octubre', 'noviembre', 'diciembre'
            ]
        else:
            return [
                'January', 'February', 'March', 'April', 'May', 'June',
                'July', 'August', 'September', 'October', 'November', 'December'
            ]
            
    def get_day_names(self):
        """Get day names for the current culture"""
        if self.current_culture.startswith('ar'):
            return ['الأحد', 'الإثنين', 'الثلاثاء', 'الأربعاء', 'الخميس', 'الجمعة', 'السبت']
        elif self.current_culture.startswith('fr'):
            return ['dimanche', 'lundi', 'mardi', 'mercredi', 'jeudi', 'vendredi', 'samedi']
        elif self.current_culture.startswith('de'):
            return ['Sonntag', 'Montag', 'Dienstag', 'Mittwoch', 'Donnerstag', 'Freitag', 'Samstag']
        elif self.current_culture.startswith('es'):
            return ['domingo', 'lunes', 'martes', 'miércoles', 'jueves', 'viernes', 'sábado']
        elif self.current_culture.startswith('ur'):
            return ['اتوار', 'پیر', 'منگل', 'بدھ', 'جمعرات', 'جمعہ', 'ہفتہ']
        else:
            return ['Sunday', 'Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday']

# Global cultural manager instance
cultural_manager = CulturalManager()
