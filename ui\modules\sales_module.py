from PyQt5.QtWidgets import (QWidget, QVBoxLayout, QHBoxLayout, QPushButton, 
                            QTableWidget, QTableWidgetItem, QLineEdit, QLabel,
                            QFormLayout, QDialog, QMessageBox, QComboBox,
                            QGroupBox, QDateEdit, QTextEdit, QHeaderView,
                            QSpinBox, QDoubleSpinBox, QTabWidget, QListWidget,
                            QListWidgetItem, QSplitter)
from PyQt5.QtCore import Qt, QDate, pyqtSignal
from PyQt5.QtGui import QFont
import sqlite3
from datetime import datetime

class SaleItemWidget(QWidget):
    def __init__(self, parent=None):
        super().__init__(parent)
        self.init_ui()
        
    def init_ui(self):
        layout = QHBoxLayout()
        
        self.block_combo = QComboBox()
        self.quantity_spin = QSpinBox()
        self.quantity_spin.setMinimum(1)
        self.quantity_spin.setMaximum(999)
        
        self.price_edit = QDoubleSpinBox()
        self.price_edit.setMaximum(999999)
        self.price_edit.setSuffix(" ريال")
        
        self.total_label = QLabel("0.00 ريال")
        
        self.remove_btn = QPushButton("حذف")
        self.remove_btn.setMaximumWidth(60)
        
        layout.addWidget(QLabel("الكتلة:"))
        layout.addWidget(self.block_combo)
        layout.addWidget(QLabel("الكمية:"))
        layout.addWidget(self.quantity_spin)
        layout.addWidget(QLabel("السعر:"))
        layout.addWidget(self.price_edit)
        layout.addWidget(QLabel("المجموع:"))
        layout.addWidget(self.total_label)
        layout.addWidget(self.remove_btn)
        
        self.setLayout(layout)
        
        # Connect signals
        self.quantity_spin.valueChanged.connect(self.calculate_total)
        self.price_edit.valueChanged.connect(self.calculate_total)
        
    def calculate_total(self):
        total = self.quantity_spin.value() * self.price_edit.value()
        self.total_label.setText(f"{total:.2f} ريال")
        
    def get_total(self):
        return self.quantity_spin.value() * self.price_edit.value()

class SaleDialog(QDialog):
    def __init__(self, parent=None, sale_data=None, db_manager=None, translator=None):
        super().__init__(parent)
        self.translator = translator
        self.db_manager = db_manager
        self.sale_data = sale_data
        self.sale_items = []
        self.init_ui()
        self.load_customers()
        self.load_available_blocks()
        
        if sale_data:
            self.populate_fields()
            
    def init_ui(self):
        self.setWindowTitle("إضافة/تعديل فاتورة مبيعات")
        self.setModal(True)
        self.resize(900, 700)
        
        layout = QVBoxLayout()
        
        # Create tabs
        tab_widget = QTabWidget()
        
        # Sale Info Tab
        sale_tab = QWidget()
        sale_layout = QFormLayout()
        
        self.invoice_number_edit = QLineEdit()
        self.sale_date = QDateEdit()
        self.sale_date.setDate(QDate.currentDate())
        self.sale_date.setCalendarPopup(True)
        
        self.customer_combo = QComboBox()
        self.customer_combo.setEditable(True)
        
        self.payment_method_combo = QComboBox()
        self.payment_method_combo.addItems(["نقدي", "شيك", "تحويل بنكي", "آجل"])
        
        self.payment_status_combo = QComboBox()
        self.payment_status_combo.addItems(["مدفوع", "مستحق", "مدفوع جزئياً"])
        
        self.discount_edit = QDoubleSpinBox()
        self.discount_edit.setMaximum(999999)
        self.discount_edit.setSuffix(" ريال")
        
        self.tax_rate_edit = QDoubleSpinBox()
        self.tax_rate_edit.setMaximum(100)
        self.tax_rate_edit.setSuffix(" %")
        self.tax_rate_edit.setValue(15)  # Default VAT rate
        
        self.notes_edit = QTextEdit()
        
        sale_layout.addRow("رقم الفاتورة:", self.invoice_number_edit)
        sale_layout.addRow("تاريخ البيع:", self.sale_date)
        sale_layout.addRow("العميل:", self.customer_combo)
        sale_layout.addRow("طريقة الدفع:", self.payment_method_combo)
        sale_layout.addRow("حالة الدفع:", self.payment_status_combo)
        sale_layout.addRow("الخصم:", self.discount_edit)
        sale_layout.addRow("معدل الضريبة:", self.tax_rate_edit)
        sale_layout.addRow("ملاحظات:", self.notes_edit)
        
        sale_tab.setLayout(sale_layout)
        tab_widget.addTab(sale_tab, "معلومات البيع")
        
        # Items Tab
        items_tab = QWidget()
        items_layout = QVBoxLayout()
        
        # Add item button
        add_item_btn = QPushButton("إضافة صنف")
        add_item_btn.clicked.connect(self.add_sale_item)
        items_layout.addWidget(add_item_btn)
        
        # Items list
        self.items_widget = QWidget()
        self.items_layout = QVBoxLayout()
        self.items_widget.setLayout(self.items_layout)
        items_layout.addWidget(self.items_widget)
        
        # Totals
        totals_layout = QFormLayout()
        self.subtotal_label = QLabel("0.00 ريال")
        self.discount_amount_label = QLabel("0.00 ريال")
        self.tax_amount_label = QLabel("0.00 ريال")
        self.total_label = QLabel("0.00 ريال")
        
        totals_layout.addRow("المجموع الفرعي:", self.subtotal_label)
        totals_layout.addRow("الخصم:", self.discount_amount_label)
        totals_layout.addRow("الضريبة:", self.tax_amount_label)
        totals_layout.addRow("المجموع الكلي:", self.total_label)
        
        items_layout.addLayout(totals_layout)
        
        items_tab.setLayout(items_layout)
        tab_widget.addTab(items_tab, "الأصناف")
        
        layout.addWidget(tab_widget)
        
        # Buttons
        button_layout = QHBoxLayout()
        self.save_btn = QPushButton("حفظ")
        self.cancel_btn = QPushButton("إلغاء")
        self.calculate_btn = QPushButton("حساب المجموع")
        
        self.save_btn.clicked.connect(self.accept)
        self.cancel_btn.clicked.connect(self.reject)
        self.calculate_btn.clicked.connect(self.calculate_totals)
        
        button_layout.addWidget(self.save_btn)
        button_layout.addWidget(self.cancel_btn)
        button_layout.addWidget(self.calculate_btn)
        layout.addLayout(button_layout)
        
        self.setLayout(layout)
        
        # Add first item
        self.add_sale_item()
        
    def load_customers(self):
        try:
            conn = self.db_manager.get_connection()
            cursor = conn.cursor()
            cursor.execute("SELECT id, name FROM customers ORDER BY name")
            customers = cursor.fetchall()
            
            for customer in customers:
                self.customer_combo.addItem(customer[1], customer[0])
                
            conn.close()
        except Exception as e:
            print(f"Error loading customers: {e}")
            
    def load_available_blocks(self):
        try:
            conn = self.db_manager.get_connection()
            cursor = conn.cursor()
            cursor.execute("""
                SELECT id, block_id, stone_type, selling_price 
                FROM blocks 
                WHERE status = 'متاح'
                ORDER BY block_id
            """)
            self.available_blocks = cursor.fetchall()
            conn.close()
        except Exception as e:
            print(f"Error loading blocks: {e}")
            self.available_blocks = []
            
    def add_sale_item(self):
        item_widget = SaleItemWidget()
        
        # Populate block combo
        for block in self.available_blocks:
            item_widget.block_combo.addItem(
                f"{block[1]} - {block[2]} ({block[3]} ريال)", 
                block[0]
            )
        
        # Set default price
        if self.available_blocks:
            item_widget.price_edit.setValue(self.available_blocks[0][3])
        
        # Connect remove button
        item_widget.remove_btn.clicked.connect(
            lambda: self.remove_sale_item(item_widget)
        )
        
        # Connect calculation
        item_widget.quantity_spin.valueChanged.connect(self.calculate_totals)
        item_widget.price_edit.valueChanged.connect(self.calculate_totals)
        
        self.sale_items.append(item_widget)
        self.items_layout.addWidget(item_widget)
        
    def remove_sale_item(self, item_widget):
        if len(self.sale_items) > 1:  # Keep at least one item
            self.sale_items.remove(item_widget)
            self.items_layout.removeWidget(item_widget)
            item_widget.deleteLater()
            self.calculate_totals()
        
    def calculate_totals(self):
        subtotal = sum(item.get_total() for item in self.sale_items)
        discount = self.discount_edit.value()
        tax_rate = self.tax_rate_edit.value() / 100
        
        discounted_amount = subtotal - discount
        tax_amount = discounted_amount * tax_rate
        total = discounted_amount + tax_amount
        
        self.subtotal_label.setText(f"{subtotal:.2f} ريال")
        self.discount_amount_label.setText(f"{discount:.2f} ريال")
        self.tax_amount_label.setText(f"{tax_amount:.2f} ريال")
        self.total_label.setText(f"{total:.2f} ريال")
        
    def get_data(self):
        items_data = []
        for item in self.sale_items:
            if item.block_combo.currentData():
                items_data.append({
                    'block_id': item.block_combo.currentData(),
                    'quantity': item.quantity_spin.value(),
                    'price': item.price_edit.value(),
                    'total': item.get_total()
                })
        
        subtotal = sum(item.get_total() for item in self.sale_items)
        discount = self.discount_edit.value()
        tax_rate = self.tax_rate_edit.value() / 100
        discounted_amount = subtotal - discount
        tax_amount = discounted_amount * tax_rate
        total = discounted_amount + tax_amount
        
        return {
            'invoice_number': self.invoice_number_edit.text(),
            'sale_date': self.sale_date.date().toString('yyyy-MM-dd'),
            'customer_id': self.customer_combo.currentData(),
            'payment_method': self.payment_method_combo.currentText(),
            'payment_status': self.payment_status_combo.currentText(),
            'subtotal': subtotal,
            'discount': discount,
            'tax_rate': self.tax_rate_edit.value(),
            'tax_amount': tax_amount,
            'total': total,
            'notes': self.notes_edit.toPlainText(),
            'items': items_data
        }

class SalesModule(QWidget):
    def __init__(self, db_manager, translator, parent=None):
        super().__init__(parent)
        self.db_manager = db_manager
        self.translator = translator
        self.init_ui()
        self.load_sales()
        
    def init_ui(self):
        layout = QVBoxLayout()
        
        # Header
        header_layout = QHBoxLayout()
        title_label = QLabel("إدارة المبيعات")
        title_label.setFont(QFont("Arial", 16, QFont.Bold))
        header_layout.addWidget(title_label)
        header_layout.addStretch()
        
        # Search and filters
        self.search_edit = QLineEdit()
        self.search_edit.setPlaceholderText("البحث في المبيعات...")
        self.search_edit.textChanged.connect(self.search_sales)
        
        self.date_from = QDateEdit()
        self.date_from.setDate(QDate.currentDate().addDays(-30))
        self.date_from.setCalendarPopup(True)
        
        self.date_to = QDateEdit()
        self.date_to.setDate(QDate.currentDate())
        self.date_to.setCalendarPopup(True)
        
        self.payment_status_filter = QComboBox()
        self.payment_status_filter.addItems(["الكل", "مدفوع", "مستحق", "مدفوع جزئياً"])
        self.payment_status_filter.currentTextChanged.connect(self.filter_sales)
        
        header_layout.addWidget(QLabel("البحث:"))
        header_layout.addWidget(self.search_edit)
        header_layout.addWidget(QLabel("من:"))
        header_layout.addWidget(self.date_from)
        header_layout.addWidget(QLabel("إلى:"))
        header_layout.addWidget(self.date_to)
        header_layout.addWidget(QLabel("حالة الدفع:"))
        header_layout.addWidget(self.payment_status_filter)
        
        layout.addLayout(header_layout)
        
        # Buttons
        button_layout = QHBoxLayout()
        self.add_btn = QPushButton("فاتورة جديدة")
        self.edit_btn = QPushButton("تعديل")
        self.delete_btn = QPushButton("حذف")
        self.refresh_btn = QPushButton("تحديث")
        self.print_btn = QPushButton("طباعة")
        self.export_btn = QPushButton("تصدير")
        
        self.add_btn.clicked.connect(self.add_sale)
        self.edit_btn.clicked.connect(self.edit_sale)
        self.delete_btn.clicked.connect(self.delete_sale)
        self.refresh_btn.clicked.connect(self.load_sales)
        self.print_btn.clicked.connect(self.print_invoice)
        self.export_btn.clicked.connect(self.export_sales)
        
        button_layout.addWidget(self.add_btn)
        button_layout.addWidget(self.edit_btn)
        button_layout.addWidget(self.delete_btn)
        button_layout.addWidget(self.refresh_btn)
        button_layout.addWidget(self.print_btn)
        button_layout.addWidget(self.export_btn)
        button_layout.addStretch()
        
        layout.addLayout(button_layout)
        
        # Table
        self.table = QTableWidget()
        self.table.setColumnCount(9)
        self.table.setHorizontalHeaderLabels([
            "الرقم", "رقم الفاتورة", "التاريخ", "العميل", "المجموع", 
            "الخصم", "الضريبة", "الإجمالي", "حالة الدفع"
        ])
        
        # Set table properties
        self.table.setSelectionBehavior(QTableWidget.SelectRows)
        self.table.setAlternatingRowColors(True)
        self.table.horizontalHeader().setStretchLastSection(True)
        self.table.horizontalHeader().setSectionResizeMode(QHeaderView.Stretch)
        
        layout.addWidget(self.table)
        
        # Statistics
        stats_layout = QHBoxLayout()
        self.total_sales_label = QLabel("إجمالي المبيعات: 0")
        self.paid_sales_label = QLabel("المدفوع: 0 ريال")
        self.pending_sales_label = QLabel("المستحق: 0 ريال")
        self.today_sales_label = QLabel("مبيعات اليوم: 0 ريال")
        
        stats_layout.addWidget(self.total_sales_label)
        stats_layout.addWidget(self.paid_sales_label)
        stats_layout.addWidget(self.pending_sales_label)
        stats_layout.addWidget(self.today_sales_label)
        stats_layout.addStretch()
        
        layout.addLayout(stats_layout)
        
        self.setLayout(layout)
        
    def load_sales(self):
        try:
            conn = self.db_manager.get_connection()
            cursor = conn.cursor()
            
            cursor.execute("""
                SELECT s.id, s.invoice_number, s.sale_date, c.name, s.subtotal,
                       s.discount, s.tax_amount, s.total, s.payment_status
                FROM sales s
                LEFT JOIN customers c ON s.customer_id = c.id
                ORDER BY s.sale_date DESC
            """)
            
            sales = cursor.fetchall()
            
            self.table.setRowCount(len(sales))
            
            for row, sale in enumerate(sales):
                for col, value in enumerate(sale):
                    if col in [4, 5, 6, 7]:  # Amount columns
                        value = f"{value:,.2f} ريال"
                    elif col == 2:  # Date column
                        value = str(value)[:10]
                    
                    item = QTableWidgetItem(str(value))
                    item.setTextAlignment(Qt.AlignCenter)
                    
                    # Color coding for payment status
                    if col == 8:  # payment status column
                        if value == "مدفوع":
                            item.setBackground(Qt.green)
                        elif value == "مستحق":
                            item.setBackground(Qt.red)
                        elif value == "مدفوع جزئياً":
                            item.setBackground(Qt.yellow)
                    
                    self.table.setItem(row, col, item)
            
            # Update statistics
            self.update_statistics(sales)
            
            conn.close()
            
        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"خطأ في تحميل المبيعات: {str(e)}")
            
    def update_statistics(self, sales):
        total_count = len(sales)
        paid_amount = sum(sale[7] for sale in sales if sale[8] == "مدفوع")
        pending_amount = sum(sale[7] for sale in sales if sale[8] == "مستحق")
        
        # Today's sales
        today = datetime.now().strftime('%Y-%m-%d')
        today_amount = sum(sale[7] for sale in sales if str(sale[2])[:10] == today)
        
        self.total_sales_label.setText(f"إجمالي المبيعات: {total_count}")
        self.paid_sales_label.setText(f"المدفوع: {paid_amount:,.2f} ريال")
        self.pending_sales_label.setText(f"المستحق: {pending_amount:,.2f} ريال")
        self.today_sales_label.setText(f"مبيعات اليوم: {today_amount:,.2f} ريال")
        
    def search_sales(self):
        search_text = self.search_edit.text().lower()
        
        for row in range(self.table.rowCount()):
            show_row = False
            for col in range(self.table.columnCount()):
                item = self.table.item(row, col)
                if item and search_text in item.text().lower():
                    show_row = True
                    break
            self.table.setRowHidden(row, not show_row)
            
    def filter_sales(self):
        payment_status_filter = self.payment_status_filter.currentText()
        
        for row in range(self.table.rowCount()):
            if payment_status_filter == "الكل":
                self.table.setRowHidden(row, False)
            else:
                status_item = self.table.item(row, 8)  # payment status column
                if status_item:
                    show_row = status_item.text() == payment_status_filter
                    self.table.setRowHidden(row, not show_row)
                    
    def add_sale(self):
        dialog = SaleDialog(self, db_manager=self.db_manager, translator=self.translator)
        if dialog.exec_() == QDialog.Accepted:
            data = dialog.get_data()
            self.save_sale(data)
            
    def edit_sale(self):
        current_row = self.table.currentRow()
        if current_row < 0:
            QMessageBox.warning(self, "تحذير", "يرجى اختيار فاتورة للتعديل")
            return
            
        sale_id = self.table.item(current_row, 0).text()
        sale_data = self.get_sale_data(sale_id)
        
        dialog = SaleDialog(self, sale_data, self.db_manager, self.translator)
        if dialog.exec_() == QDialog.Accepted:
            data = dialog.get_data()
            self.update_sale(sale_id, data)
            
    def delete_sale(self):
        current_row = self.table.currentRow()
        if current_row < 0:
            QMessageBox.warning(self, "تحذير", "يرجى اختيار فاتورة للحذف")
            return
            
        invoice_number = self.table.item(current_row, 1).text()
        reply = QMessageBox.question(
            self, "تأكيد الحذف", 
            f"هل أنت متأكد من حذف الفاتورة '{invoice_number}'؟",
            QMessageBox.Yes | QMessageBox.No
        )
        
        if reply == QMessageBox.Yes:
            sale_id = self.table.item(current_row, 0).text()
            self.remove_sale(sale_id)
            
    def print_invoice(self):
        current_row = self.table.currentRow()
        if current_row < 0:
            QMessageBox.warning(self, "تحذير", "يرجى اختيار فاتورة للطباعة")
            return
            
        QMessageBox.information(self, "طباعة", "سيتم تطوير ميزة الطباعة قريباً")
        
    def export_sales(self):
        QMessageBox.information(self, "تصدير", "سيتم تطوير ميزة التصدير قريباً")
        
    def save_sale(self, data):
        try:
            conn = self.db_manager.get_connection()
            cursor = conn.cursor()
            
            # Insert sale
            cursor.execute("""
                INSERT INTO sales (invoice_number, sale_date, customer_id, payment_method,
                                 payment_status, subtotal, discount, tax_rate, tax_amount,
                                 total, notes)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            """, (
                data['invoice_number'], data['sale_date'], data['customer_id'],
                data['payment_method'], data['payment_status'], data['subtotal'],
                data['discount'], data['tax_rate'], data['tax_amount'],
                data['total'], data['notes']
            ))
            
            sale_id = cursor.lastrowid
            
            # Insert sale items
            for item in data['items']:
                cursor.execute("""
                    INSERT INTO sale_items (sale_id, block_id, quantity, price, total)
                    VALUES (?, ?, ?, ?, ?)
                """, (sale_id, item['block_id'], item['quantity'], item['price'], item['total']))
                
                # Update block status to sold
                cursor.execute("UPDATE blocks SET status='مباع' WHERE id=?", (item['block_id'],))
            
            conn.commit()
            conn.close()
            
            QMessageBox.information(self, "نجح", "تم حفظ الفاتورة بنجاح")
            self.load_sales()
            
        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"خطأ في حفظ الفاتورة: {str(e)}")
            
    def update_sale(self, sale_id, data):
        try:
            conn = self.db_manager.get_connection()
            cursor = conn.cursor()
            
            cursor.execute("""
                UPDATE sales 
                SET invoice_number=?, sale_date=?, customer_id=?, payment_method=?,
                    payment_status=?, subtotal=?, discount=?, tax_rate=?, tax_amount=?,
                    total=?, notes=?
                WHERE id=?
            """, (
                data['invoice_number'], data['sale_date'], data['customer_id'],
                data['payment_method'], data['payment_status'], data['subtotal'],
                data['discount'], data['tax_rate'], data['tax_amount'],
                data['total'], data['notes'], sale_id
            ))
            
            conn.commit()
            conn.close()
            
            QMessageBox.information(self, "نجح", "تم تحديث الفاتورة بنجاح")
            self.load_sales()
            
        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"خطأ في تحديث الفاتورة: {str(e)}")
            
    def remove_sale(self, sale_id):
        try:
            conn = self.db_manager.get_connection()
            cursor = conn.cursor()
            
            # Get sale items to update block status
            cursor.execute("SELECT block_id FROM sale_items WHERE sale_id=?", (sale_id,))
            block_ids = cursor.fetchall()
            
            # Update block status back to available
            for block_id in block_ids:
                cursor.execute("UPDATE blocks SET status='متاح' WHERE id=?", (block_id[0],))
            
            # Delete sale items
            cursor.execute("DELETE FROM sale_items WHERE sale_id=?", (sale_id,))
            
            # Delete sale
            cursor.execute("DELETE FROM sales WHERE id=?", (sale_id,))
            
            conn.commit()
            conn.close()
            
            QMessageBox.information(self, "نجح", "تم حذف الفاتورة بنجاح")
            self.load_sales()
            
        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"خطأ في حذف الفاتورة: {str(e)}")
            
    def get_sale_data(self, sale_id):
        try:
            conn = self.db_manager.get_connection()
            cursor = conn.cursor()
            
            cursor.execute("""
                SELECT invoice_number, sale_date, customer_id, payment_method,
                       payment_status, subtotal, discount, tax_rate, tax_amount,
                       total, notes
                FROM sales WHERE id=?
            """, (sale_id,))
            
            result = cursor.fetchone()
            
            if result:
                return {
                    'invoice_number': result[0],
                    'sale_date': result[1],
                    'customer_id': result[2],
                    'payment_method': result[3],
                    'payment_status': result[4],
                    'subtotal': result[5],
                    'discount': result[6],
                    'tax_rate': result[7],
                    'tax_amount': result[8],
                    'total': result[9],
                    'notes': result[10]
                }
            
            conn.close()
            return {}
            
        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"خطأ في جلب بيانات الفاتورة: {str(e)}")
            return {}
