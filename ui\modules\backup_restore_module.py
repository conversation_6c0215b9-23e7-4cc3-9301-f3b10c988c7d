from PyQt5.QtWidgets import (QWidget, QVBoxLayout, QHBoxLayout, QTableWidget, 
                            QTableWidgetItem, QPushButton, QLineEdit, QLabel,
                            QDialog, QFormLayout, QMessageBox, QHeaderView,
                            QFileDialog, QProgressBar, QTextEdit, QGroupBox,
                            QDateEdit, QComboBox, QCheckBox)
from PyQt5.QtCore import Qt, QDate, QThread, pyqtSignal, QTimer
from PyQt5.QtGui import QFont, QColor
from datetime import datetime, timedelta
import os
import shutil
import zipfile
import json

class BackupRestoreModule(QWidget):
    def __init__(self, db_manager, user_data):
        super().__init__()
        self.db_manager = db_manager
        self.user_data = user_data
        self.init_ui()
        self.load_backup_logs()
        self.setup_auto_backup_timer()
        
    def init_ui(self):
        layout = QVBoxLayout()
        
        # Header
        header_layout = QHBoxLayout()
        
        title = QLabel("النسخ الاحتياطي والاستعادة / Backup & Restore")
        title.setStyleSheet("font-size: 16pt; font-weight: bold; color: #0078d4;")
        header_layout.addWidget(title)
        
        header_layout.addStretch()
        
        # Manual backup button
        backup_btn = QPushButton("نسخة احتياطية يدوية / Manual Backup")
        backup_btn.setStyleSheet("background-color: #28a745;")
        backup_btn.clicked.connect(self.create_manual_backup)
        header_layout.addWidget(backup_btn)
        
        # Restore button
        restore_btn = QPushButton("استعادة / Restore")
        restore_btn.setStyleSheet("background-color: #ffc107;")
        restore_btn.clicked.connect(self.restore_backup)
        header_layout.addWidget(restore_btn)
        
        layout.addLayout(header_layout)
        
        # Backup status
        status_group = QGroupBox("حالة النسخ الاحتياطي / Backup Status")
        status_layout = QVBoxLayout(status_group)
        
        # Status info
        info_layout = QHBoxLayout()
        
        self.last_backup_label = QLabel("آخر نسخة احتياطية / Last Backup: -")
        self.next_backup_label = QLabel("النسخة القادمة / Next Backup: -")
        self.auto_backup_status_label = QLabel("النسخ التلقائي / Auto Backup: غير نشط / Inactive")
        
        info_layout.addWidget(self.last_backup_label)
        info_layout.addWidget(self.next_backup_label)
        info_layout.addWidget(self.auto_backup_status_label)
        
        status_layout.addLayout(info_layout)
        
        # Progress bar
        self.progress_bar = QProgressBar()
        self.progress_bar.setVisible(False)
        status_layout.addWidget(self.progress_bar)
        
        # Status message
        self.status_message = QLabel("")
        self.status_message.setStyleSheet("color: #28a745; font-weight: bold;")
        status_layout.addWidget(self.status_message)
        
        layout.addWidget(status_group)
        
        # Auto backup settings
        auto_backup_group = QGroupBox("إعدادات النسخ التلقائي / Auto Backup Settings")
        auto_backup_layout = QFormLayout(auto_backup_group)
        
        self.auto_backup_enabled = QCheckBox("تفعيل النسخ التلقائي / Enable Auto Backup")
        self.auto_backup_enabled.stateChanged.connect(self.toggle_auto_backup)
        auto_backup_layout.addRow("الحالة / Status:", self.auto_backup_enabled)
        
        self.backup_frequency = QComboBox()
        self.backup_frequency.addItems([
            "يومي / Daily", "أسبوعي / Weekly", "شهري / Monthly"
        ])
        auto_backup_layout.addRow("التكرار / Frequency:", self.backup_frequency)
        
        self.backup_time = QLineEdit()
        self.backup_time.setText("02:00")
        self.backup_time.setPlaceholderText("HH:MM (24-hour format)")
        auto_backup_layout.addRow("وقت النسخ / Backup Time:", self.backup_time)
        
        self.max_backups = QLineEdit()
        self.max_backups.setText("30")
        self.max_backups.setPlaceholderText("عدد النسخ المحفوظة / Number of backups to keep")
        auto_backup_layout.addRow("الحد الأقصى للنسخ / Max Backups:", self.max_backups)
        
        save_settings_btn = QPushButton("حفظ الإعدادات / Save Settings")
        save_settings_btn.clicked.connect(self.save_auto_backup_settings)
        auto_backup_layout.addRow(save_settings_btn)
        
        layout.addWidget(auto_backup_group)
        
        # Backup logs table
        logs_group = QGroupBox("سجل النسخ الاحتياطي / Backup Logs")
        logs_layout = QVBoxLayout(logs_group)
        
        self.backup_logs_table = QTableWidget()
        self.backup_logs_table.setColumnCount(6)
        self.backup_logs_table.setHorizontalHeaderLabels([
            "النوع / Type", "التاريخ / Date", "حجم الملف / File Size",
            "الحالة / Status", "مسار الملف / File Path", "الإجراءات / Actions"
        ])
        
        # Set column widths
        header = self.backup_logs_table.horizontalHeader()
        header.setSectionResizeMode(QHeaderView.Stretch)
        
        logs_layout.addWidget(self.backup_logs_table)
        
        layout.addWidget(logs_group)
        
        self.setLayout(layout)
        
        # Load current settings
        self.load_auto_backup_settings()
        
    def setup_auto_backup_timer(self):
        """Setup timer for automatic backups"""
        self.auto_backup_timer = QTimer()
        self.auto_backup_timer.timeout.connect(self.check_auto_backup)
        self.auto_backup_timer.start(60000)  # Check every minute
        
    def load_auto_backup_settings(self):
        """Load auto backup settings"""
        # Load settings from database
        auto_enabled = self.db_manager.get_system_setting('auto_backup_enabled', False)
        backup_freq = self.db_manager.get_system_setting('backup_frequency', 'daily')
        backup_time = self.db_manager.get_system_setting('backup_time', '02:00')
        max_backups = self.db_manager.get_system_setting('max_backups', 30)
        
        self.auto_backup_enabled.setChecked(auto_enabled)
        
        freq_map = {'daily': 0, 'weekly': 1, 'monthly': 2}
        self.backup_frequency.setCurrentIndex(freq_map.get(backup_freq, 0))
        
        self.backup_time.setText(str(backup_time))
        self.max_backups.setText(str(max_backups))
        
        # Update status labels
        self.update_backup_status()
        
    def update_backup_status(self):
        """Update backup status labels"""
        conn = self.db_manager.get_connection()
        cursor = conn.cursor()
        
        # Get last successful backup
        cursor.execute("""
            SELECT created_at FROM backup_logs 
            WHERE status = 'success' 
            ORDER BY created_at DESC 
            LIMIT 1
        """)
        
        last_backup = cursor.fetchone()
        
        if last_backup:
            self.last_backup_label.setText(f"آخر نسخة احتياطية / Last Backup: {last_backup['created_at']}")
        else:
            self.last_backup_label.setText("آخر نسخة احتياطية / Last Backup: لا توجد / None")
            
        # Calculate next backup time
        if self.auto_backup_enabled.isChecked():
            self.auto_backup_status_label.setText("النسخ التلقائي / Auto Backup: نشط / Active")
            # Calculate next backup time based on frequency
            next_backup = self.calculate_next_backup_time()
            self.next_backup_label.setText(f"النسخة القادمة / Next Backup: {next_backup}")
        else:
            self.auto_backup_status_label.setText("النسخ التلقائي / Auto Backup: غير نشط / Inactive")
            self.next_backup_label.setText("النسخة القادمة / Next Backup: -")
            
        conn.close()
        
    def calculate_next_backup_time(self):
        """Calculate next backup time"""
        now = datetime.now()
        backup_time_str = self.backup_time.text()
        
        try:
            backup_hour, backup_minute = map(int, backup_time_str.split(':'))
            
            # Create next backup datetime
            next_backup = now.replace(hour=backup_hour, minute=backup_minute, second=0, microsecond=0)
            
            # If time has passed today, schedule for tomorrow
            if next_backup <= now:
                next_backup += timedelta(days=1)
                
            # Adjust based on frequency
            freq_index = self.backup_frequency.currentIndex()
            if freq_index == 1:  # Weekly
                days_until_next = (6 - now.weekday()) % 7
                if days_until_next == 0 and next_backup <= now:
                    days_until_next = 7
                next_backup = now.replace(hour=backup_hour, minute=backup_minute, second=0, microsecond=0)
                next_backup += timedelta(days=days_until_next)
            elif freq_index == 2:  # Monthly
                if now.day >= next_backup.day and next_backup <= now:
                    # Next month
                    if now.month == 12:
                        next_backup = next_backup.replace(year=now.year + 1, month=1)
                    else:
                        next_backup = next_backup.replace(month=now.month + 1)
                        
            return next_backup.strftime("%Y-%m-%d %H:%M")
            
        except:
            return "غير صحيح / Invalid"
            
    def load_backup_logs(self):
        """Load backup logs from database"""
        conn = self.db_manager.get_connection()
        cursor = conn.cursor()
        
        cursor.execute("""
            SELECT id, backup_type, file_path, file_size, status, 
                   error_message, created_at
            FROM backup_logs
            ORDER BY created_at DESC
            LIMIT 100
        """)
        
        logs = cursor.fetchall()
        
        self.backup_logs_table.setRowCount(len(logs))
        
        for row, log in enumerate(logs):
            self.backup_logs_table.setItem(row, 0, QTableWidgetItem(log['backup_type']))
            self.backup_logs_table.setItem(row, 1, QTableWidgetItem(log['created_at']))
            
            # File size
            if log['file_size']:
                size_mb = log['file_size'] / (1024 * 1024)
                size_text = f"{size_mb:.2f} MB"
            else:
                size_text = "-"
            self.backup_logs_table.setItem(row, 2, QTableWidgetItem(size_text))
            
            # Status with color coding
            status_item = QTableWidgetItem(log['status'])
            if log['status'] == 'success':
                status_item.setBackground(QColor('#28a745'))
            else:
                status_item.setBackground(QColor('#dc3545'))
                
            self.backup_logs_table.setItem(row, 3, status_item)
            self.backup_logs_table.setItem(row, 4, QTableWidgetItem(log['file_path']))
            
            # Action buttons
            actions_widget = QWidget()
            actions_layout = QHBoxLayout(actions_widget)
            actions_layout.setContentsMargins(5, 5, 5, 5)
            
            if log['status'] == 'success' and os.path.exists(log['file_path']):
                restore_btn = QPushButton("استعادة / Restore")
                restore_btn.setFixedSize(80, 25)
                restore_btn.setStyleSheet("background-color: #ffc107;")
                restore_btn.clicked.connect(lambda checked, path=log['file_path']: self.restore_from_path(path))
                actions_layout.addWidget(restore_btn)
                
                delete_btn = QPushButton("حذف / Delete")
                delete_btn.setFixedSize(60, 25)
                delete_btn.setStyleSheet("background-color: #dc3545;")
                delete_btn.clicked.connect(lambda checked, lid=log['id'], path=log['file_path']: self.delete_backup(lid, path))
                actions_layout.addWidget(delete_btn)
            else:
                error_btn = QPushButton("خطأ / Error")
                error_btn.setFixedSize(60, 25)
                error_btn.setStyleSheet("background-color: #dc3545;")
                error_btn.clicked.connect(lambda checked, msg=log['error_message']: self.show_error_details(msg))
                actions_layout.addWidget(error_btn)
                
            self.backup_logs_table.setCellWidget(row, 5, actions_widget)
            
        conn.close()
        
    def create_manual_backup(self):
        """Create manual backup"""
        # Get backup location
        backup_dir = QFileDialog.getExistingDirectory(
            self, "اختر مجلد النسخ الاحتياطي / Select Backup Directory"
        )
        
        if backup_dir:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            backup_filename = f"al_hassan_backup_{timestamp}.zip"
            backup_path = os.path.join(backup_dir, backup_filename)
            
            # Create backup in separate thread
            self.backup_thread = BackupThread(self.db_manager, backup_path, 'manual', self.user_data['id'])
            self.backup_thread.progress_updated.connect(self.update_progress)
            self.backup_thread.status_updated.connect(self.update_status)
            self.backup_thread.backup_completed.connect(self.backup_completed)
            
            self.progress_bar.setVisible(True)
            self.progress_bar.setValue(0)
            self.status_message.setText("جاري إنشاء النسخة الاحتياطية... / Creating backup...")
            
            self.backup_thread.start()
            
    def restore_backup(self):
        """Restore from backup file"""
        backup_file, _ = QFileDialog.getOpenFileName(
            self, "اختر ملف النسخة الاحتياطية / Select Backup File",
            "", "Backup Files (*.zip);;All Files (*)"
        )
        
        if backup_file:
            self.restore_from_path(backup_file)
            
    def restore_from_path(self, backup_path):
        """Restore from specific backup path"""
        reply = QMessageBox.question(
            self, "تأكيد الاستعادة / Confirm Restore",
            "هل أنت متأكد من استعادة النسخة الاحتياطية؟\n"
            "سيتم استبدال البيانات الحالية!\n\n"
            "Are you sure you want to restore the backup?\n"
            "Current data will be replaced!",
            QMessageBox.Yes | QMessageBox.No
        )
        
        if reply == QMessageBox.Yes:
            # Create restore in separate thread
            self.restore_thread = RestoreThread(self.db_manager, backup_path, self.user_data['id'])
            self.restore_thread.progress_updated.connect(self.update_progress)
            self.restore_thread.status_updated.connect(self.update_status)
            self.restore_thread.restore_completed.connect(self.restore_completed)
            
            self.progress_bar.setVisible(True)
            self.progress_bar.setValue(0)
            self.status_message.setText("جاري استعادة النسخة الاحتياطية... / Restoring backup...")
            
            self.restore_thread.start()
            
    def delete_backup(self, log_id, file_path):
        """Delete backup file"""
        reply = QMessageBox.question(
            self, "حذف النسخة الاحتياطية / Delete Backup",
            "هل تريد حذف هذه النسخة الاحتياطية؟\n"
            "Do you want to delete this backup?",
            QMessageBox.Yes | QMessageBox.No
        )
        
        if reply == QMessageBox.Yes:
            try:
                if os.path.exists(file_path):
                    os.remove(file_path)
                    
                # Update database
                conn = self.db_manager.get_connection()
                cursor = conn.cursor()
                cursor.execute("DELETE FROM backup_logs WHERE id = ?", (log_id,))
                conn.commit()
                conn.close()
                
                self.load_backup_logs()
                self.status_message.setText("تم حذف النسخة الاحتياطية / Backup deleted")
                
            except Exception as e:
                QMessageBox.critical(self, "خطأ / Error", f"Error deleting backup: {str(e)}")
                
    def show_error_details(self, error_message):
        """Show backup error details"""
        QMessageBox.critical(self, "تفاصيل الخطأ / Error Details", error_message or "خطأ غير معروف / Unknown error")
        
    def toggle_auto_backup(self, state):
        """Toggle auto backup on/off"""
        self.update_backup_status()
        
    def save_auto_backup_settings(self):
        """Save auto backup settings"""
        try:
            # Validate backup time format
            backup_time_str = self.backup_time.text()
            try:
                hour, minute = map(int, backup_time_str.split(':'))
                if not (0 <= hour <= 23 and 0 <= minute <= 59):
                    raise ValueError()
            except:
                QMessageBox.warning(self, "تحذير / Warning", 
                                  "صيغة الوقت غير صحيحة. استخدم HH:MM\n"
                                  "Invalid time format. Use HH:MM")
                return
                
            # Validate max backups
            try:
                max_backups = int(self.max_backups.text())
                if max_backups < 1:
                    raise ValueError()
            except:
                QMessageBox.warning(self, "تحذير / Warning", 
                                  "عدد النسخ الاحتياطية يجب أن يكون رقماً موجباً\n"
                                  "Max backups must be a positive number")
                return
                
            # Save settings
            freq_map = ['daily', 'weekly', 'monthly']
            backup_freq = freq_map[self.backup_frequency.currentIndex()]
            
            self.db_manager.set_system_setting('auto_backup_enabled', self.auto_backup_enabled.isChecked(), 'boolean', user_id=self.user_data['id'])
            self.db_manager.set_system_setting('backup_frequency', backup_freq, user_id=self.user_data['id'])
            self.db_manager.set_system_setting('backup_time', backup_time_str, user_id=self.user_data['id'])
            self.db_manager.set_system_setting('max_backups', max_backups, 'integer', user_id=self.user_data['id'])
            
            self.update_backup_status()
            
            QMessageBox.information(self, "نجح / Success", 
                                  "تم حفظ إعدادات الن سخ التلقائي\n"
                                  "Auto backup settings saved")
            
        except Exception as e:
            QMessageBox.critical(self, "خطأ / Error", f"Error saving settings: {str(e)}")
            
    def check_auto_backup(self):
        """Check if auto backup should run"""
        if not self.auto_backup_enabled.isChecked():
            return
            
        now = datetime.now()
        backup_time_str = self.backup_time.text()
        
        try:
            backup_hour, backup_minute = map(int, backup_time_str.split(':'))
            
            # Check if it's time for backup (within 1 minute window)
            if (now.hour == backup_hour and 
                backup_minute <= now.minute <= backup_minute + 1):
                
                # Check if backup should run based on frequency
                if self.should_run_auto_backup():
                    self.run_auto_backup()
                    
        except:
            pass
            
    def should_run_auto_backup(self):
        """Check if auto backup should run based on frequency"""
        conn = self.db_manager.get_connection()
        cursor = conn.cursor()
        
        # Get last successful auto backup
        cursor.execute("""
            SELECT created_at FROM backup_logs 
            WHERE backup_type = 'auto' AND status = 'success'
            ORDER BY created_at DESC 
            LIMIT 1
        """)
        
        last_backup = cursor.fetchone()
        conn.close()
        
        if not last_backup:
            return True
            
        last_backup_time = datetime.fromisoformat(last_backup['created_at'])
        now = datetime.now()
        
        freq_index = self.backup_frequency.currentIndex()
        
        if freq_index == 0:  # Daily
            return (now - last_backup_time).days >= 1
        elif freq_index == 1:  # Weekly
            return (now - last_backup_time).days >= 7
        elif freq_index == 2:  # Monthly
            return (now - last_backup_time).days >= 30
            
        return False
        
    def run_auto_backup(self):
        """Run automatic backup"""
        # Create backups directory if it doesn't exist
        backup_dir = "backups/auto"
        os.makedirs(backup_dir, exist_ok=True)
        
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        backup_filename = f"al_hassan_auto_backup_{timestamp}.zip"
        backup_path = os.path.join(backup_dir, backup_filename)
        
        # Create backup in separate thread
        self.backup_thread = BackupThread(self.db_manager, backup_path, 'auto', self.user_data['id'])
        self.backup_thread.backup_completed.connect(self.auto_backup_completed)
        self.backup_thread.start()
        
    def auto_backup_completed(self, success, message):
        """Handle auto backup completion"""
        if success:
            self.cleanup_old_backups()
            
        self.update_backup_status()
        self.load_backup_logs()
        
    def cleanup_old_backups(self):
        """Clean up old backup files"""
        try:
            max_backups = int(self.max_backups.text())
            
            conn = self.db_manager.get_connection()
            cursor = conn.cursor()
            
            # Get old successful backups
            cursor.execute("""
                SELECT id, file_path FROM backup_logs 
                WHERE backup_type = 'auto' AND status = 'success'
                ORDER BY created_at DESC
                OFFSET ?
            """, (max_backups,))
            
            old_backups = cursor.fetchall()
            
            for backup in old_backups:
                # Delete file
                if os.path.exists(backup['file_path']):
                    os.remove(backup['file_path'])
                    
                # Delete log entry
                cursor.execute("DELETE FROM backup_logs WHERE id = ?", (backup['id'],))
                
            conn.commit()
            conn.close()
            
        except Exception as e:
            print(f"Error cleaning up old backups: {e}")
            
    def update_progress(self, value):
        """Update progress bar"""
        self.progress_bar.setValue(value)
        
    def update_status(self, message):
        """Update status message"""
        self.status_message.setText(message)
        
    def backup_completed(self, success, message):
        """Handle backup completion"""
        self.progress_bar.setVisible(False)
        
        if success:
            self.status_message.setText("تم إنشاء النسخة الاحتياطية بنجاح / Backup created successfully")
            self.status_message.setStyleSheet("color: #28a745; font-weight: bold;")
        else:
            self.status_message.setText(f"فشل في إنشاء النسخة الاحتياطية / Backup failed: {message}")
            self.status_message.setStyleSheet("color: #dc3545; font-weight: bold;")
            
        self.load_backup_logs()
        self.update_backup_status()
        
    def restore_completed(self, success, message):
        """Handle restore completion"""
        self.progress_bar.setVisible(False)
        
        if success:
            self.status_message.setText("تم استعادة النسخة الاحتياطية بنجاح / Backup restored successfully")
            self.status_message.setStyleSheet("color: #28a745; font-weight: bold;")
            
            QMessageBox.information(self, "نجح / Success", 
                                  "تم استعادة النسخة الاحتياطية بنجاح\n"
                                  "يرجى إعادة تشغيل التطبيق\n\n"
                                  "Backup restored successfully\n"
                                  "Please restart the application")
        else:
            self.status_message.setText(f"فشل في استعادة النسخة الاحتياطية / Restore failed: {message}")
            self.status_message.setStyleSheet("color: #dc3545; font-weight: bold;")

class BackupThread(QThread):
    progress_updated = pyqtSignal(int)
    status_updated = pyqtSignal(str)
    backup_completed = pyqtSignal(bool, str)
    
    def __init__(self, db_manager, backup_path, backup_type, user_id):
        super().__init__()
        self.db_manager = db_manager
        self.backup_path = backup_path
        self.backup_type = backup_type
        self.user_id = user_id
        
    def run(self):
        try:
            self.status_updated.emit("جاري تحضير النسخة الاحتياطية... / Preparing backup...")
            self.progress_updated.emit(10)
            
            # Create backup directory
            os.makedirs(os.path.dirname(self.backup_path), exist_ok=True)
            
            self.status_updated.emit("جاري نسخ قاعدة البيانات... / Copying database...")
            self.progress_updated.emit(30)
            
            # Create zip file
            with zipfile.ZipFile(self.backup_path, 'w', zipfile.ZIP_DEFLATED) as zipf:
                # Add database file
                if os.path.exists(self.db_manager.db_path):
                    zipf.write(self.db_manager.db_path, 'database/al_hassan_stone.db')
                    
                self.progress_updated.emit(50)
                
                # Add configuration files
                self.status_updated.emit("جاري نسخ ملفات التكوين... / Copying configuration files...")
                
                config_files = [
                    'config/encryption.key',
                    'translations/',
                    'utils/config.py'
                ]
                
                for config_file in config_files:
                    if os.path.exists(config_file):
                        if os.path.isdir(config_file):
                            for root, dirs, files in os.walk(config_file):
                                for file in files:
                                    file_path = os.path.join(root, file)
                                    arcname = os.path.relpath(file_path)
                                    zipf.write(file_path, arcname)
                        else:
                            zipf.write(config_file, config_file)
                            
                self.progress_updated.emit(80)
                
                # Add backup metadata
                self.status_updated.emit("جاري إضافة معلومات النسخة... / Adding backup metadata...")
                
                metadata = {
                    'backup_date': datetime.now().isoformat(),
                    'backup_type': self.backup_type,
                    'created_by': self.user_id,
                    'version': '1.0',
                    'database_path': self.db_manager.db_path
                }
                
                zipf.writestr('backup_metadata.json', json.dumps(metadata, indent=2))
                
                self.progress_updated.emit(90)
                
            self.status_updated.emit("جاري تسجيل النسخة الاحتياطية... / Logging backup...")
            
            # Log backup
            file_size = os.path.getsize(self.backup_path)
            success, message = self.db_manager.backup_database(self.backup_path, self.user_id)
            
            if success:
                # Update the log entry with correct information
                conn = self.db_manager.get_connection()
                cursor = conn.cursor()
                
                cursor.execute("""
                    UPDATE backup_logs 
                    SET backup_type = ?, file_size = ?
                    WHERE file_path = ? AND created_by = ?
                    ORDER BY created_at DESC
                    LIMIT 1
                """, (self.backup_type, file_size, self.backup_path, self.user_id))
                
                conn.commit()
                conn.close()
                
            self.progress_updated.emit(100)
            self.backup_completed.emit(success, message)
            
        except Exception as e:
            error_message = f"Backup error: {str(e)}"
            
            # Log failed backup
            conn = self.db_manager.get_connection()
            cursor = conn.cursor()
            
            cursor.execute("""
                INSERT INTO backup_logs 
                (backup_type, file_path, status, error_message, created_by)
                VALUES (?, ?, 'failed', ?, ?)
            """, (self.backup_type, self.backup_path, error_message, self.user_id))
            
            conn.commit()
            conn.close()
            
            self.backup_completed.emit(False, error_message)

class RestoreThread(QThread):
    progress_updated = pyqtSignal(int)
    status_updated = pyqtSignal(str)
    restore_completed = pyqtSignal(bool, str)
    
    def __init__(self, db_manager, backup_path, user_id):
        super().__init__()
        self.db_manager = db_manager
        self.backup_path = backup_path
        self.user_id = user_id
        
    def run(self):
        try:
            self.status_updated.emit("جاري فحص النسخة الاحتياطية... / Checking backup file...")
            self.progress_updated.emit(10)
            
            if not os.path.exists(self.backup_path):
                raise Exception("Backup file not found")
                
            if not zipfile.is_zipfile(self.backup_path):
                raise Exception("Invalid backup file format")
                
            self.status_updated.emit("جاري استخراج النسخة الاحتياطية... / Extracting backup...")
            self.progress_updated.emit(30)
            
            # Create temporary restore directory
            restore_dir = "temp_restore"
            if os.path.exists(restore_dir):
                shutil.rmtree(restore_dir)
            os.makedirs(restore_dir)
            
            # Extract backup
            with zipfile.ZipFile(self.backup_path, 'r') as zipf:
                zipf.extractall(restore_dir)
                
            self.progress_updated.emit(50)
            
            # Verify backup metadata
            metadata_path = os.path.join(restore_dir, 'backup_metadata.json')
            if os.path.exists(metadata_path):
                with open(metadata_path, 'r') as f:
                    metadata = json.load(f)
                    
                self.status_updated.emit(f"استعادة نسخة من {metadata.get('backup_date', 'Unknown')} / "
                                       f"Restoring backup from {metadata.get('backup_date', 'Unknown')}")
            
            self.progress_updated.emit(70)
            
            # Restore database
            self.status_updated.emit("جاري استعادة قاعدة البيانات... / Restoring database...")
            
            db_backup_path = os.path.join(restore_dir, 'database', 'al_hassan_stone.db')
            if os.path.exists(db_backup_path):
                success, message = self.db_manager.restore_database(db_backup_path, self.user_id)
                if not success:
                    raise Exception(message)
            else:
                raise Exception("Database file not found in backup")
                
            self.progress_updated.emit(90)
            
            # Restore configuration files
            self.status_updated.emit("جاري استعادة ملفات التكوين... / Restoring configuration files...")
            
            config_files = ['config/', 'translations/']
            for config_dir in config_files:
                source_path = os.path.join(restore_dir, config_dir)
                if os.path.exists(source_path):
                    if os.path.exists(config_dir):
                        shutil.rmtree(config_dir)
                    shutil.copytree(source_path, config_dir)
                    
            # Clean up temporary directory
            shutil.rmtree(restore_dir)
            
            self.progress_updated.emit(100)
            self.restore_completed.emit(True, "Restore completed successfully")
            
        except Exception as e:
            error_message = f"Restore error: {str(e)}"
            
            # Clean up temporary directory
            if os.path.exists("temp_restore"):
                shutil.rmtree("temp_restore")
                
            self.restore_completed.emit(False, error_message)
