from PyQt5.QtWidgets import (QWidget, QVBoxLayout, QHBoxLayout, QTabWidget,
                            QGroupBox, QFormLayout, QLabel, QLineEdit, QPushButton,
                            QComboBox, QSpinBox, QDoubleSpinBox, QCheckBox,
                            QTextEdit, QFileDialog, QMessageBox, QSlider,
                            QColorDialog, QFontDialog, QListWidget, QListWidgetItem,
                            QSplitter, QFrame, QScrollArea, QGridLayout,
                            QButtonGroup, QRadioButton, QProgressBar, QDateEdit,
                            QTimeEdit, QTableWidget, QTableWidgetItem, QHeaderView)
from PyQt5.QtCore import Qt, pyqtSignal, QTimer, QThread, pyqtSlot, QDate, QTime
from PyQt5.QtGui import QFont, QColor, QPalette, QPixmap, QIcon
from utils.config import Config
from utils.translator import translator
from utils.cultural_manager import cultural_manager
# from utils.font_manager import font_manager  # Will be passed as parameter
import json
import os
from datetime import datetime, timedelta
import shutil
import sqlite3

class SettingsModule(QWidget):
    settings_changed = pyqtSignal(str, object)
    theme_changed = pyqtSignal(str)
    language_changed = pyqtSignal(str)
    
    def __init__(self, db_manager, user_data):
        super().__init__()
        self.db_manager = db_manager
        self.user_data = user_data
        self.config = Config()
        self.init_ui()
        self.load_settings()
        
    def init_ui(self):
        """Initialize the settings interface"""
        layout = QHBoxLayout(self)
        layout.setContentsMargins(0, 0, 0, 0)
        
        # Create splitter for sidebar and content
        splitter = QSplitter(Qt.Horizontal)
        
        # Create settings sidebar
        self.create_sidebar(splitter)
        
        # Create settings content area
        self.create_content_area(splitter)
        
        # Set splitter proportions
        splitter.setSizes([250, 750])
        layout.addWidget(splitter)
        
        # Apply initial styling
        self.apply_settings_style()
        
    def create_sidebar(self, parent):
        """Create settings navigation sidebar"""
        sidebar_frame = QFrame()
        sidebar_frame.setFixedWidth(250)
        sidebar_frame.setStyleSheet("""
            QFrame {
                background-color: #353535;
                border-right: 1px solid #555555;
            }
        """)
        
        sidebar_layout = QVBoxLayout(sidebar_frame)
        sidebar_layout.setContentsMargins(10, 20, 10, 10)
        
        # Settings title
        title_label = QLabel("إعدادات النظام\nSystem Settings")
        title_label.setFont(QFont("Arial", 14, QFont.Bold))
        title_label.setStyleSheet("color: #0078d4; margin-bottom: 20px;")
        title_label.setAlignment(Qt.AlignCenter)
        sidebar_layout.addWidget(title_label)
        
        # Settings categories
        self.category_list = QListWidget()
        self.category_list.setStyleSheet("""
            QListWidget {
                background-color: transparent;
                border: none;
                outline: none;
            }
            QListWidget::item {
                padding: 12px;
                border-radius: 6px;
                margin: 2px;
                color: white;
                font-weight: bold;
            }
            QListWidget::item:hover {
                background-color: #404040;
            }
            QListWidget::item:selected {
                background-color: #0078d4;
            }
        """)
        
        # Add categories
        categories = [
            ("عام / General", "general", "⚙️"),
            ("المظهر / Appearance", "appearance", "🎨"),
            ("اللغة والثقافة / Language & Culture", "language", "🌍"),
            ("قاعدة البيانات / Database", "database", "🗄️"),
            ("الأمان / Security", "security", "🔒"),
            ("النسخ الاحتياطي / Backup", "backup", "💾"),
            ("الطباعة / Printing", "printing", "🖨️"),
            ("التقارير / Reports", "reports", "📊"),
            ("الإشعارات / Notifications", "notifications", "🔔"),
            ("التكامل / Integration", "integration", "🔗"),
            ("متقدم / Advanced", "advanced", "⚡"),
            ("حول البرنامج / About", "about", "ℹ️")
        ]
        
        for name, key, icon in categories:
            item = QListWidgetItem(f"{icon} {name}")
            item.setData(Qt.UserRole, key)
            self.category_list.addItem(item)
        
        self.category_list.currentItemChanged.connect(self.on_category_changed)
        sidebar_layout.addWidget(self.category_list)
        
        # Quick actions
        sidebar_layout.addWidget(QLabel("إجراءات سريعة / Quick Actions"))
        
        quick_actions_layout = QVBoxLayout()
        
        # Reset to defaults button
        reset_btn = QPushButton("استعادة الافتراضي\nReset to Defaults")
        reset_btn.setStyleSheet("""
            QPushButton {
                background-color: #dc3545;
                color: white;
                border: none;
                padding: 8px;
                border-radius: 4px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #c82333;
            }
        """)
        reset_btn.clicked.connect(self.reset_to_defaults)
        quick_actions_layout.addWidget(reset_btn)
        
        # Export settings button
        export_btn = QPushButton("تصدير الإعدادات\nExport Settings")
        export_btn.setStyleSheet("""
            QPushButton {
                background-color: #28a745;
                color: white;
                border: none;
                padding: 8px;
                border-radius: 4px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #218838;
            }
        """)
        export_btn.clicked.connect(self.export_settings)
        quick_actions_layout.addWidget(export_btn)
        
        # Import settings button
        import_btn = QPushButton("استيراد الإعدادات\nImport Settings")
        import_btn.setStyleSheet("""
            QPushButton {
                background-color: #17a2b8;
                color: white;
                border: none;
                padding: 8px;
                border-radius: 4px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #138496;
            }
        """)
        import_btn.clicked.connect(self.import_settings)
        quick_actions_layout.addWidget(import_btn)
        
        sidebar_layout.addLayout(quick_actions_layout)
        sidebar_layout.addStretch()
        
        parent.addWidget(sidebar_frame)
        
    def create_content_area(self, parent):
        """Create settings content area"""
        content_frame = QFrame()
        content_layout = QVBoxLayout(content_frame)
        content_layout.setContentsMargins(20, 20, 20, 20)
        
        # Content header
        self.content_header = QLabel("اختر فئة من الشريط الجانبي\nSelect a category from the sidebar")
        self.content_header.setFont(QFont("Arial", 16, QFont.Bold))
        self.content_header.setStyleSheet("color: #0078d4; margin-bottom: 20px;")
        content_layout.addWidget(self.content_header)
        
        # Scrollable content area
        scroll_area = QScrollArea()
        scroll_area.setWidgetResizable(True)
        scroll_area.setStyleSheet("""
            QScrollArea {
                border: none;
                background-color: transparent;
            }
        """)
        
        self.content_widget = QWidget()
        self.content_layout = QVBoxLayout(self.content_widget)
        scroll_area.setWidget(self.content_widget)
        
        content_layout.addWidget(scroll_area)
        
        # Action buttons
        buttons_layout = QHBoxLayout()
        
        self.apply_btn = QPushButton("تطبيق / Apply")
        self.apply_btn.setFont(QFont("Arial", 10, QFont.Bold))
        self.apply_btn.setStyleSheet("""
            QPushButton {
                background-color: #0078d4;
                color: white;
                border: none;
                padding: 10px 20px;
                border-radius: 4px;
            }
            QPushButton:hover {
                background-color: #106ebe;
            }
        """)
        self.apply_btn.clicked.connect(self.apply_settings)
        
        self.cancel_btn = QPushButton("إلغاء / Cancel")
        self.cancel_btn.setFont(QFont("Arial", 10))
        self.cancel_btn.setStyleSheet("""
            QPushButton {
                background-color: #6c757d;
                color: white;
                border: none;
                padding: 10px 20px;
                border-radius: 4px;
            }
            QPushButton:hover {
                background-color: #5a6268;
            }
        """)
        self.cancel_btn.clicked.connect(self.cancel_changes)
        
        buttons_layout.addWidget(self.apply_btn)
        buttons_layout.addWidget(self.cancel_btn)
        buttons_layout.addStretch()
        
        content_layout.addLayout(buttons_layout)
        
        parent.addWidget(content_frame)
        
    def on_category_changed(self, current, previous):
        """Handle category selection change"""
        if current:
            category_key = current.data(Qt.UserRole)
            self.show_category_settings(category_key)
            
    def show_category_settings(self, category_key):
        """Show settings for selected category"""
        # Clear current content
        for i in reversed(range(self.content_layout.count())):
            self.content_layout.itemAt(i).widget().setParent(None)
            
        # Update header
        category_names = {
            'general': 'الإعدادات العامة / General Settings',
            'appearance': 'إعدادات المظهر / Appearance Settings',
            'language': 'اللغة والثقافة / Language & Culture',
            'database': 'إعدادات قاعدة البيانات / Database Settings',
            'security': 'إعدادات الأمان / Security Settings',
            'backup': 'إعدادات النسخ الاحتياطي / Backup Settings',
            'printing': 'إعدادات الطباعة / Printing Settings',
            'reports': 'إعدادات التقارير / Reports Settings',
            'notifications': 'إعدادات الإشعارات / Notifications Settings',
            'integration': 'إعدادات التكامل / Integration Settings',
            'advanced': 'الإعدادات المتقدمة / Advanced Settings',
            'about': 'حول البرنامج / About Program'
        }
        
        self.content_header.setText(category_names.get(category_key, category_key))
        
        # Show category-specific settings
        if category_key == 'general':
            self.show_general_settings()
        elif category_key == 'appearance':
            self.show_appearance_settings()
        elif category_key == 'language':
            self.show_language_settings()
        elif category_key == 'database':
            self.show_database_settings()
        elif category_key == 'security':
            self.show_security_settings()
        elif category_key == 'backup':
            self.show_backup_settings()
        elif category_key == 'printing':
            self.show_printing_settings()
        elif category_key == 'reports':
            self.show_reports_settings()
        elif category_key == 'notifications':
            self.show_notifications_settings()
        elif category_key == 'integration':
            self.show_integration_settings()
        elif category_key == 'advanced':
            self.show_advanced_settings()
        elif category_key == 'about':
            self.show_about_info()
            
    def show_general_settings(self):
        """Show general settings"""
        # Company Information
        company_group = QGroupBox("معلومات الشركة / Company Information")
        company_layout = QFormLayout(company_group)
        
        self.company_name_edit = QLineEdit()
        self.company_name_edit.setText(self.config.get('company_name', 'Al-Hassan Stone Factory'))
        company_layout.addRow("اسم الشركة / Company Name:", self.company_name_edit)
        
        self.company_address_edit = QTextEdit()
        self.company_address_edit.setMaximumHeight(80)
        self.company_address_edit.setText(self.config.get('company_address', ''))
        company_layout.addRow("العنوان / Address:", self.company_address_edit)
        
        self.company_phone_edit = QLineEdit()
        self.company_phone_edit.setText(self.config.get('company_phone', ''))
        company_layout.addRow("الهاتف / Phone:", self.company_phone_edit)
        
        self.company_email_edit = QLineEdit()
        self.company_email_edit.setText(self.config.get('company_email', ''))
        company_layout.addRow("البريد الإلكتروني / Email:", self.company_email_edit)
        
        self.content_layout.addWidget(company_group)
        
        # Application Behavior
        behavior_group = QGroupBox("سلوك التطبيق / Application Behavior")
        behavior_layout = QFormLayout(behavior_group)
        
        self.auto_save_checkbox = QCheckBox("حفظ تلقائي / Auto Save")
        self.auto_save_checkbox.setChecked(self.config.get('auto_save', True))
        behavior_layout.addRow(self.auto_save_checkbox)
        
        self.auto_save_interval_spin = QSpinBox()
        self.auto_save_interval_spin.setRange(1, 60)
        self.auto_save_interval_spin.setValue(self.config.get('auto_save_interval', 5))
        self.auto_save_interval_spin.setSuffix(" دقائق / minutes")
        behavior_layout.addRow("فترة الحفظ التلقائي / Auto Save Interval:", self.auto_save_interval_spin)
        
        self.confirm_delete_checkbox = QCheckBox("تأكيد الحذف / Confirm Delete")
        self.confirm_delete_checkbox.setChecked(self.config.get('confirm_delete', True))
        behavior_layout.addRow(self.confirm_delete_checkbox)
        
        self.show_splash_checkbox = QCheckBox("عرض شاشة البداية / Show Splash Screen")
        self.show_splash_checkbox.setChecked(self.config.get('show_splash', True))
        behavior_layout.addRow(self.show_splash_checkbox)
        
        self.content_layout.addWidget(behavior_group)
        
        # Default Values
        defaults_group = QGroupBox("القيم الافتراضية / Default Values")
        defaults_layout = QFormLayout(defaults_group)
        
        self.default_currency_combo = QComboBox()
        self.default_currency_combo.addItems(['SAR', 'USD', 'EUR', 'AED', 'EGP'])
        self.default_currency_combo.setCurrentText(self.config.get('default_currency', 'SAR'))
        defaults_layout.addRow("العملة الافتراضية / Default Currency:", self.default_currency_combo)
        
        self.default_tax_rate_spin = QDoubleSpinBox()
        self.default_tax_rate_spin.setRange(0, 100)
        self.default_tax_rate_spin.setValue(self.config.get('default_tax_rate', 15.0))
        self.default_tax_rate_spin.setSuffix("%")
        defaults_layout.addRow("معدل الضريبة الافتراضي / Default Tax Rate:", self.default_tax_rate_spin)
        
        self.default_payment_terms_spin = QSpinBox()
        self.default_payment_terms_spin.setRange(0, 365)
        self.default_payment_terms_spin.setValue(self.config.get('default_payment_terms', 30))
        self.default_payment_terms_spin.setSuffix(" يوم / days")
        defaults_layout.addRow("شروط الدفع الافتراضية / Default Payment Terms:", self.default_payment_terms_spin)
        
        self.content_layout.addWidget(defaults_group)
        
    def show_appearance_settings(self):
        """Show appearance settings"""
        # Theme Settings
        theme_group = QGroupBox("إعدادات المظهر / Theme Settings")
        theme_layout = QFormLayout(theme_group)
        
        self.theme_combo = QComboBox()
        self.theme_combo.addItems(['Dark / داكن', 'Light / فاتح', 'Auto / تلقائي'])
        current_theme = self.config.get('theme', 'dark')
        theme_index = {'dark': 0, 'light': 1, 'auto': 2}.get(current_theme, 0)
        self.theme_combo.setCurrentIndex(theme_index)
        theme_layout.addRow("المظهر / Theme:", self.theme_combo)
        
        # Color Scheme
        self.primary_color_btn = QPushButton("اختيار اللون الأساسي / Choose Primary Color")
        self.primary_color_btn.clicked.connect(self.choose_primary_color)
        theme_layout.addRow("اللون الأساسي / Primary Color:", self.primary_color_btn)
        
        self.accent_color_btn = QPushButton("اختيار اللون الثانوي / Choose Accent Color")
        self.accent_color_btn.clicked.connect(self.choose_accent_color)
        theme_layout.addRow("اللون الثانوي / Accent Color:", self.accent_color_btn)
        
        self.content_layout.addWidget(theme_group)
        
        # Font Settings
        font_group = QGroupBox("إعدادات الخط / Font Settings")
        font_layout = QFormLayout(font_group)
        
        self.main_font_btn = QPushButton("اختيار الخط الأساسي / Choose Main Font")
        self.main_font_btn.clicked.connect(self.choose_main_font)
        font_layout.addRow("الخط الأساسي / Main Font:", self.main_font_btn)
        
        self.ui_font_size_spin = QSpinBox()
        self.ui_font_size_spin.setRange(8, 24)
        self.ui_font_size_spin.setValue(self.config.get('ui_font_size', 10))
        font_layout.addRow("حجم خط الواجهة / UI Font Size:", self.ui_font_size_spin)
        
        self.report_font_size_spin = QSpinBox()
        self.report_font_size_spin.setRange(8, 24)
        self.report_font_size_spin.setValue(self.config.get('report_font_size', 12))
        font_layout.addRow("حجم خط التقارير / Report Font Size:", self.report_font_size_spin)
        
        self.content_layout.addWidget(font_group)
        
        # Window Settings
        window_group = QGroupBox("إعدادات النافذة / Window Settings")
        window_layout = QFormLayout(window_group)
        
        self.remember_window_size_checkbox = QCheckBox("تذكر حجم النافذة / Remember Window Size")
        self.remember_window_size_checkbox.setChecked(self.config.get('remember_window_size', True))
        window_layout.addRow(self.remember_window_size_checkbox)
        
        self.start_maximized_checkbox = QCheckBox("بدء بحجم كامل / Start Maximized")
        self.start_maximized_checkbox.setChecked(self.config.get('start_maximized', False))
        window_layout.addRow(self.start_maximized_checkbox)
        
        self.show_toolbar_checkbox = QCheckBox("عرض شريط الأدوات / Show Toolbar")
        self.show_toolbar_checkbox.setChecked(self.config.get('show_toolbar', True))
        window_layout.addRow(self.show_toolbar_checkbox)
        
        self.show_statusbar_checkbox = QCheckBox("عرض شريط الحالة / Show Status Bar")
        self.show_statusbar_checkbox.setChecked(self.config.get('show_statusbar', True))
        window_layout.addRow(self.show_statusbar_checkbox)
        
        self.content_layout.addWidget(window_group)
        
    def show_language_settings(self):
        """Show language and culture settings"""
        # Language Settings
        lang_group = QGroupBox("إعدادات اللغة / Language Settings")
        lang_layout = QFormLayout(lang_group)
        
        self.language_combo = QComboBox()
        languages = self.config.get_supported_languages()
        for code, name in languages.items():
            self.language_combo.addItem(f"{name} ({code.upper()})", code)
        
        current_lang = self.config.get_language()
        for i in range(self.language_combo.count()):
            if self.language_combo.itemData(i) == current_lang:
                self.language_combo.setCurrentIndex(i)
                break
                
        lang_layout.addRow("اللغة / Language:", self.language_combo)
        
        self.rtl_checkbox = QCheckBox("تخطيط من اليمين لليسار / Right-to-Left Layout")
        self.rtl_checkbox.setChecked(self.config.is_rtl_language(current_lang))
        lang_layout.addRow(self.rtl_checkbox)
        
        self.content_layout.addWidget(lang_group)
        
        # Cultural Settings
        culture_group = QGroupBox("الإعدادات الثقافية / Cultural Settings")
        culture_layout = QFormLayout(culture_group)
        
        self.culture_combo = QComboBox()
        cultures = cultural_manager.get_available_cultures()
        for code, name in cultures.items():
            self.culture_combo.addItem(f"{name} ({code})", code)
            
        current_culture = self.config.get('culture', 'ar-SA')
        for i in range(self.culture_combo.count()):
            if self.culture_combo.itemData(i) == current_culture:
                self.culture_combo.setCurrentIndex(i)
                break
                
        culture_layout.addRow("الثقافة / Culture:", self.culture_combo)
        
        self.calendar_system_combo = QComboBox()
        calendars = cultural_manager.get_calendar_systems()
        for code, name in calendars.items():
            self.calendar_system_combo.addItem(name, code)
        culture_layout.addRow("نظام التقويم / Calendar System:", self.calendar_system_combo)
        
        self.number_system_combo = QComboBox()
        number_systems = {
            'western': 'Western (0123456789)',
            'arabic-indic': 'Arabic-Indic (٠١٢٣٤٥٦٧٨٩)',
            'persian': 'Persian (۰۱۲۳۴۵۶۷۸۹)'
        }
        for code, name in number_systems.items():
            self.number_system_combo.addItem(name, code)
        culture_layout.addRow("نظام الأرقام / Number System:", self.number_system_combo)
        
        self.content_layout.addWidget(culture_group)
        
        # Date and Time Format
        datetime_group = QGroupBox("تنسيق التاريخ والوقت / Date & Time Format")
        datetime_layout = QFormLayout(datetime_group)
        
        self.date_format_combo = QComboBox()
        self.date_format_combo.addItems([
            "dd/MM/yyyy", "MM/dd/yyyy", "yyyy/MM/dd",
            "dd.MM.yyyy", "dd-MM-yyyy"
        ])
        datetime_layout.addRow("تنسيق التاريخ / Date Format:", self.date_format_combo)
        
        self.time_format_combo = QComboBox()
        self.time_format_combo.addItems(["12 ساعة / 12 Hour", "24 ساعة / 24 Hour"])
        datetime_layout.addRow("تنسيق الوقت / Time Format:", self.time_format_combo)
        
        self.content_layout.addWidget(datetime_group)
        
    def show_database_settings(self):
        """Show database settings"""
        # Database Connection
        db_group = QGroupBox("اتصال قاعدة البيانات / Database Connection")
        db_layout = QFormLayout(db_group)
        
        self.db_path_edit = QLineEdit()
        self.db_path_edit.setText(self.config.get_database_path())
        self.db_path_edit.setReadOnly(True)
        
        db_path_layout = QHBoxLayout()
        db_path_layout.addWidget(self.db_path_edit)
        
        browse_db_btn = QPushButton("تصفح / Browse")
        browse_db_btn.clicked.connect(self.browse_database_path)
        db_path_layout.addWidget(browse_db_btn)
        
        db_layout.addRow("مسار قاعدة البيانات / Database Path:", db_path_layout)
        
        # Test connection button
        test_conn_btn = QPushButton("اختبار الاتصال / Test Connection")
        test_conn_btn.clicked.connect(self.test_database_connection)
        db_layout.addRow(test_conn_btn)
        
        self.content_layout.addWidget(db_group)
        
        # Database Maintenance
        maintenance_group = QGroupBox("صيانة قاعدة البيانات / Database Maintenance")
        maintenance_layout = QVBoxLayout(maintenance_group)
        
        # Database info
        self.db_info_label = QLabel("جاري تحميل معلومات قاعدة البيانات...\nLoading database information...")
        maintenance_layout.addWidget(self.db_info_label)
        
        # Maintenance buttons
        maintenance_buttons_layout = QHBoxLayout()
        
        vacuum_btn = QPushButton("ضغط قاعدة البيانات / Vacuum Database")
        vacuum_btn.clicked.connect(self.vacuum_database)
        maintenance_buttons_layout.addWidget(vacuum_btn)
        
        reindex_btn = QPushButton("إعادة فهرسة / Reindex")
        reindex_btn.clicked.connect(self.reindex_database)
        maintenance_buttons_layout.addWidget(reindex_btn)
        
        analyze_btn = QPushButton("تحليل / Analyze")
        analyze_btn.clicked.connect(self.analyze_database)
        maintenance_buttons_layout.addWidget(analyze_btn)
        
        maintenance_layout.addLayout(maintenance_buttons_layout)
        
        self.content_layout.addWidget(maintenance_group)
        
        # Performance Settings
        performance_group = QGroupBox("إعدادات الأداء / Performance Settings")
        performance_layout = QFormLayout(performance_group)
        
        self.cache_size_spin = QSpinBox()
        self.cache_size_spin.setRange(1000, 100000)
        self.cache_size_spin.setValue(self.config.get('db_cache_size', 10000))
        performance_layout.addRow("حجم التخزين المؤقت / Cache Size:", self.cache_size_spin)
        
        self.page_size_combo = QComboBox()
        self.page_size_combo.addItems(['1024', '2048', '4096', '8192'])
        self.page_size_combo.setCurrentText(str(self.config.get('db_page_size', 4096)))
        performance_layout.addRow("حجم الصفحة / Page Size:", self.page_size_combo)
        
        self.content_layout.addWidget(performance_group)
        
        # Load database info
        self.load_database_info()
        
    def show_security_settings(self):
        """Show security settings"""
        # Authentication Settings
        auth_group = QGroupBox("إعدادات المصادقة / Authentication Settings")
        auth_layout = QFormLayout(auth_group)
        
        self.session_timeout_spin = QSpinBox()
        self.session_timeout_spin.setRange(5, 480)
        self.session_timeout_spin.setValue(self.config.get('session_timeout', 30))
        self.session_timeout_spin.setSuffix(" دقيقة / minutes")
        auth_layout.addRow("انتهاء الجلسة / Session Timeout:", self.session_timeout_spin)
        
        self.max_login_attempts_spin = QSpinBox()
        self.max_login_attempts_spin.setRange(1, 10)
        self.max_login_attempts_spin.setValue(self.config.get('max_login_attempts', 3))
        auth_layout.addRow("محاولات تسجيل الدخول / Max Login Attempts:", self.max_login_attempts_spin)
        
        self.lockout_duration_spin = QSpinBox()
        self.lockout_duration_spin.setRange(1, 60)
        self.lockout_duration_spin.setValue(self.config.get('lockout_duration', 15))
        self.lockout_duration_spin.setSuffix(" دقيقة / minutes")
        auth_layout.addRow("مدة القفل / Lockout Duration:", self.lockout_duration_spin)
        
        self.content_layout.addWidget(auth_group)
        
        # Password Policy
        password_group = QGroupBox("سياسة كلمة المرور / Password Policy")
        password_layout = QFormLayout(password_group)
        
        self.min_password_length_spin = QSpinBox()
        self.min_password_length_spin.setRange(4, 32)
        self.min_password_length_spin.setValue(self.config.get('min_password_length', 8))
        password_layout.addRow("الحد الأدنى لطول كلمة المرور / Min Password Length:", self.min_password_length_spin)
        
        self.require_uppercase_checkbox = QCheckBox("يتطلب أحرف كبيرة / Require Uppercase")
        self.require_uppercase_checkbox.setChecked(self.config.get('require_uppercase', True))
        password_layout.addRow(self.require_uppercase_checkbox)
        
        self.require_numbers_checkbox = QCheckBox("يتطلب أرقام / Require Numbers")
        self.require_numbers_checkbox.setChecked(self.config.get('require_numbers', True))
        password_layout.addRow(self.require_numbers_checkbox)
        
        self.require_symbols_checkbox = QCheckBox("يتطلب رموز / Require Symbols")
        self.require_symbols_checkbox.setChecked(self.config.get('require_symbols', False))
        password_layout.addRow(self.require_symbols_checkbox)
        
        self.content_layout.addWidget(password_group)
        
        # Data Encryption
        encryption_group = QGroupBox("تشفير البيانات / Data Encryption")
        encryption_layout = QFormLayout(encryption_group)
        
        self.encrypt_sensitive_data_checkbox = QCheckBox("تشفير البيانات الحساسة / Encrypt Sensitive Data")
        self.encrypt_sensitive_data_checkbox.setChecked(self.config.get('encrypt_sensitive_data', True))
        encryption_layout.addRow(self.encrypt_sensitive_data_checkbox)
        
        self.encryption_algorithm_combo = QComboBox()
        self.encryption_algorithm_combo.addItems(['AES-256', 'AES-128', 'Fernet'])
        self.encryption_algorithm_combo.setCurrentText(self.config.get('encryption_algorithm', 'Fernet'))
        encryption_layout.addRow("خوارزمية التشفير / Encryption Algorithm:", self.encryption_algorithm_combo)
        
        # Generate new encryption key button
        generate_key_btn = QPushButton("إنشاء مفتاح تشفير جديد / Generate New Encryption Key")
        generate_key_btn.clicked.connect(self.generate_encryption_key)
        encryption_layout.addRow(generate_key_btn)
        
        self.content_layout.addWidget(encryption_group)
        
        # Activity Logging
        logging_group = QGroupBox("تسجيل النشاط / Activity Logging")
        logging_layout = QFormLayout(logging_group)
        
        self.enable_activity_logging_checkbox = QCheckBox("تمكين تسجيل النشاط / Enable Activity Logging")
        self.enable_activity_logging_checkbox.setChecked(self.config.get('enable_activity_logging', True))
        logging_layout.addRow(self.enable_activity_logging_checkbox)
        
        self.log_retention_days_spin = QSpinBox()
        self.log_retention_days_spin.setRange(1, 365)
        self.log_retention_days_spin.setValue(self.config.get('log_retention_days', 90))
        self.log_retention_days_spin.setSuffix(" يوم / days")
        logging_layout.addRow("مدة الاحتفاظ بالسجلات / Log Retention Period:", self.log_retention_days_spin)
        
        self.content_layout.addWidget(logging_group)
        
    def show_backup_settings(self):
        """Show backup settings"""
        # Automatic Backup
        auto_backup_group = QGroupBox("النسخ الاحتياطي التلقائي / Automatic Backup")
        auto_backup_layout = QFormLayout(auto_backup_group)
        
        self.enable_auto_backup_checkbox = QCheckBox("تمكين النسخ الاحتياطي التلقائي / Enable Automatic Backup")
        self.enable_auto_backup_checkbox.setChecked(self.config.get('enable_auto_backup', True))
        auto_backup_layout.addRow(self.enable_auto_backup_checkbox)
        
        self.backup_frequency_combo = QComboBox()
        self.backup_frequency_combo.addItems([
            'يومي / Daily', 'أسبوعي / Weekly', 'شهري / Monthly'
        ])
        frequency_map = {'daily': 0, 'weekly': 1, 'monthly': 2}
        current_frequency = self.config.get('backup_frequency', 'daily')
        self.backup_frequency_combo.setCurrentIndex(frequency_map.get(current_frequency, 0))
        auto_backup_layout.addRow("تكرار النسخ / Backup Frequency:", self.backup_frequency_combo)
        
        self.backup_time_edit = QTimeEdit()
        backup_time = self.config.get('backup_time', '02:00')
        self.backup_time_edit.setTime(QTime.fromString(backup_time, 'hh:mm'))
        auto_backup_layout.addRow("وقت النسخ / Backup Time:", self.backup_time_edit)
        
        self.content_layout.addWidget(auto_backup_group)
        
        # Backup Location
        location_group = QGroupBox("موقع النسخ الاحتياطي / Backup Location")
        location_layout = QFormLayout(location_group)
        
        self.backup_directory_edit = QLineEdit()
        self.backup_directory_edit.setText(self.config.get_backup_directory())
        self.backup_directory_edit.setReadOnly(True)
        
        backup_dir_layout = QHBoxLayout()
        backup_dir_layout.addWidget(self.backup_directory_edit)
        
        browse_backup_btn = QPushButton("تصفح / Browse")
        browse_backup_btn.clicked.connect(self.browse_backup_directory)
        backup_dir_layout.addWidget(browse_backup_btn)
        
        location_layout.addRow("مجلد النسخ الاحتياطي / Backup Directory:", backup_dir_layout)
        
        self.content_layout.addWidget(location_group)
        
        # Backup Retention
        retention_group = QGroupBox("الاحتفاظ بالنسخ / Backup Retention")
        retention_layout = QFormLayout(retention_group)
        
        self.max_backup_files_spin = QSpinBox()
        self.max_backup_files_spin.setRange(1, 100)
        self.max_backup_files_spin.setValue(self.config.get('max_backup_files', 10))
        retention_layout.addRow("الحد الأقصى لملفات النسخ / Max Backup Files:", self.max_backup_files_spin)
        
        self.backup_compression_checkbox = QCheckBox("ضغط النسخ الاحتياطي / Compress Backups")
        self.backup_compression_checkbox.setChecked(self.config.get('backup_compression', True))
        retention_layout.addRow(self.backup_compression_checkbox)
        
        self.content_layout.addWidget(retention_group)
        
        # Cloud Backup
        cloud_group = QGroupBox("النسخ السحابي / Cloud Backup")
        cloud_layout = QFormLayout(cloud_group)
        
        self.enable_cloud_backup_checkbox = QCheckBox("تمكين النسخ السحابي / Enable Cloud Backup")
        self.enable_cloud_backup_checkbox.setChecked(self.config.get('enable_cloud_backup', False))
        cloud_layout.addRow(self.enable_cloud_backup_checkbox)
        
        self.cloud_provider_combo = QComboBox()
        self.cloud_provider_combo.addItems(['Google Drive', 'Dropbox', 'OneDrive', 'AWS S3'])
        cloud_layout.addRow("مزود الخدمة السحابية / Cloud Provider:", self.cloud_provider_combo)
        
        self.cloud_credentials_btn = QPushButton("إعداد بيانات الاعتماد / Setup Credentials")
        self.cloud_credentials_btn.clicked.connect(self.setup_cloud_credentials)
        cloud_layout.addRow(self.cloud_credentials_btn)
        
        self.content_layout.addWidget(cloud_group)
        
    def show_printing_settings(self):
        """Show printing settings"""
        # Default Printer
        printer_group = QGroupBox("الطابعة الافتراضية / Default Printer")
        printer_layout = QFormLayout(printer_group)
        
        self.default_printer_combo = QComboBox()
        # Add available printers (would need to implement printer detection)
        self.default_printer_combo.addItems(['System Default', 'PDF Printer'])
        printer_layout.addRow("الطابعة / Printer:", self.default_printer_combo)
        
        self.content_layout.addWidget(printer_group)
        
        # Page Setup
        page_group = QGroupBox("إعداد الصفحة / Page Setup")
        page_layout = QFormLayout(page_group)
        
        self.paper_size_combo = QComboBox()
        self.paper_size_combo.addItems(['A4', 'A3', 'Letter', 'Legal'])
        self.paper_size_combo.setCurrentText(self.config.get('paper_size', 'A4'))
        page_layout.addRow("حجم الورق / Paper Size:", self.paper_size_combo)
        
        self.orientation_combo = QComboBox()
        self.orientation_combo.addItems(['عمودي / Portrait', 'أفقي / Landscape'])
        page_layout.addRow("الاتجاه / Orientation:", self.orientation_combo)
        
        self.print_margins_spin = QDoubleSpinBox()
        self.print_margins_spin.setRange(0.5, 5.0)
        self.print_margins_spin.setValue(self.config.get('print_margins', 1.0))
        self.print_margins_spin.setSuffix(" سم / cm")
        page_layout.addRow("الهوامش / Margins:", self.print_margins_spin)
        
        self.content_layout.addWidget(page_group)
        
        # Print Quality
        quality_group = QGroupBox("جودة الطباعة / Print Quality")
        quality_layout = QFormLayout(quality_group)
        
        self.print_quality_combo = QComboBox()
        self.print_quality_combo.addItems(['مسودة / Draft', 'عادي / Normal', 'عالي / High'])
        quality_layout.addRow("الجودة / Quality:", self.print_quality_combo)
        
        self.print_color_checkbox = QCheckBox("طباعة ملونة / Color Printing")
        self.print_color_checkbox.setChecked(self.config.get('print_color', True))
        quality_layout.addRow(self.print_color_checkbox)
        
        self.content_layout.addWidget(quality_group)
        
    def show_reports_settings(self):
        """Show reports settings"""
        # Report Generation
        generation_group = QGroupBox("إنشاء التقارير / Report Generation")
        generation_layout = QFormLayout(generation_group)
        
        self.default_report_format_combo = QComboBox()
        self.default_report_format_combo.addItems(['PDF', 'Excel', 'Word', 'HTML'])
        self.default_report_format_combo.setCurrentText(self.config.get('default_report_format', 'PDF'))
        generation_layout.addRow("تنسيق التقرير الافتراضي / Default Report Format:", self.default_report_format_combo)
        
        self.include_logo_checkbox = QCheckBox("تضمين الشعار / Include Logo")
        self.include_logo_checkbox.setChecked(self.config.get('include_logo', True))
        generation_layout.addRow(self.include_logo_checkbox)
        
        self.report_header_edit = QTextEdit()
        self.report_header_edit.setMaximumHeight(60)
        self.report_header_edit.setText(self.config.get('report_header', ''))
        generation_layout.addRow("رأس التقرير / Report Header:", self.report_header_edit)
        
        self.report_footer_edit = QTextEdit()
        self.report_footer_edit.setMaximumHeight(60)
        self.report_footer_edit.setText(self.config.get('report_footer', ''))
        generation_layout.addRow("تذييل التقرير / Report Footer:", self.report_footer_edit)
        
        self.content_layout.addWidget(generation_group)
        
        # Chart Settings
        chart_group = QGroupBox("إعدادات الرسوم البيانية / Chart Settings")
        chart_layout = QFormLayout(chart_group)
        
        self.chart_theme_combo = QComboBox()
        self.chart_theme_combo.addItems(['Default', 'Professional', 'Colorful', 'Minimal'])
        chart_layout.addRow("مظهر الرسوم البيانية / Chart Theme:", self.chart_theme_combo)
        
        self.chart_animation_checkbox = QCheckBox("تحريك الرسوم البيانية / Animate Charts")
        self.chart_animation_checkbox.setChecked(self.config.get('chart_animation', True))
        chart_layout.addRow(self.chart_animation_checkbox)
        
        self.content_layout.addWidget(chart_group)
        
    def show_notifications_settings(self):
        """Show notifications settings"""
        # System Notifications
        system_group = QGroupBox("إشعارات النظام / System Notifications")
        system_layout = QFormLayout(system_group)
        
        self.enable_notifications_checkbox = QCheckBox("تمكين الإشعارات / Enable Notifications")
        self.enable_notifications_checkbox.setChecked(self.config.get('enable_notifications', True))
        system_layout.addRow(self.enable_notifications_checkbox)
        
        self.notification_sound_checkbox = QCheckBox("صوت الإشعار / Notification Sound")
        self.notification_sound_checkbox.setChecked(self.config.get('notification_sound', True))
        system_layout.addRow(self.notification_sound_checkbox)
        
        self.notification_duration_spin = QSpinBox()
        self.notification_duration_spin.setRange(1, 30)
        self.notification_duration_spin.setValue(self.config.get('notification_duration', 5))
        self.notification_duration_spin.setSuffix(" ثانية / seconds")
        system_layout.addRow("مدة الإشعار / Notification Duration:", self.notification_duration_spin)
        
        self.content_layout.addWidget(system_group)
        
        # Email Notifications
        email_group = QGroupBox("إشعارات البريد الإلكتروني / Email Notifications")
        email_layout = QFormLayout(email_group)
        
        self.enable_email_notifications_checkbox = QCheckBox("تمكين إشعارات البريد / Enable Email Notifications")
        self.enable_email_notifications_checkbox.setChecked(self.config.get('enable_email_notifications', False))
        email_layout.addRow(self.enable_email_notifications_checkbox)
        
        self.smtp_server_edit = QLineEdit()
        self.smtp_server_edit.setText(self.config.get('smtp_server', ''))
        email_layout.addRow("خادم SMTP / SMTP Server:", self.smtp_server_edit)
        
        self.smtp_port_spin = QSpinBox()
        self.smtp_port_spin.setRange(1, 65535)
        self.smtp_port_spin.setValue(self.config.get('smtp_port', 587))
        email_layout.addRow("منفذ SMTP / SMTP Port:", self.smtp_port_spin)
        
        self.smtp_username_edit = QLineEdit()
        self.smtp_username_edit.setText(self.config.get('smtp_username', ''))
        email_layout.addRow("اسم المستخدم / Username:", self.smtp_username_edit)
        
        self.smtp_password_edit = QLineEdit()
        self.smtp_password_edit.setEchoMode(QLineEdit.Password)
        email_layout.addRow("كلمة المرور / Password:", self.smtp_password_edit)
        
        test_email_btn = QPushButton("اختبار البريد الإلكتروني / Test Email")
        test_email_btn.clicked.connect(self.test_email_settings)
        email_layout.addRow(test_email_btn)
        
        self.content_layout.addWidget(email_group)
        
    def show_integration_settings(self):
        """Show integration settings"""
        # External Systems
        external_group = QGroupBox("الأنظمة الخارجية / External Systems")
        external_layout = QFormLayout(external_group)
        
        self.enable_api_checkbox = QCheckBox("تمكين API / Enable API")
        self.enable_api_checkbox.setChecked(self.config.get('enable_api', False))
        external_layout.addRow(self.enable_api_checkbox)
        
        self.api_port_spin = QSpinBox()
        self.api_port_spin.setRange(1000, 65535)
        self.api_port_spin.setValue(self.config.get('api_port', 8080))
        external_layout.addRow("منفذ API / API Port:", self.api_port_spin)
        
        self.api_key_edit = QLineEdit()
        self.api_key_edit.setText(self.config.get('api_key', ''))
        external_layout.addRow("مفتاح API / API Key:", self.api_key_edit)
        
        generate_api_key_btn = QPushButton("إنشاء مفتاح جديد / Generate New Key")
        generate_api_key_btn.clicked.connect(self.generate_api_key)
        external_layout.addRow(generate_api_key_btn)
        
        self.content_layout.addWidget(external_group)
        
        # Accounting Software Integration
        accounting_group = QGroupBox("تكامل برامج المحاسبة / Accounting Software Integration")
        accounting_layout = QFormLayout(accounting_group)
        
        self.accounting_software_combo = QComboBox()
        self.accounting_software_combo.addItems(['None', 'QuickBooks', 'SAP', 'Oracle', 'Custom'])
        accounting_layout.addRow("برنامج المحاسبة / Accounting Software:", self.accounting_software_combo)
        
        self.sync_frequency_combo = QComboBox()
        self.sync_frequency_combo.addItems(['Manual', 'Hourly', 'Daily', 'Weekly'])
        accounting_layout.addRow("تكرار المزامنة / Sync Frequency:", self.sync_frequency_combo)
        
        self.content_layout.addWidget(accounting_group)
        
    def show_advanced_settings(self):
        """Show advanced settings"""
        # Performance Tuning
        performance_group = QGroupBox("ضبط الأداء / Performance Tuning")
        performance_layout = QFormLayout(performance_group)
        
        self.max_threads_spin = QSpinBox()
        self.max_threads_spin.setRange(1, 16)
        self.max_threads_spin.setValue(self.config.get('max_threads', 4))
        performance_layout.addRow("الحد الأقصى للخيوط / Max Threads:", self.max_threads_spin)
        
        self.memory_limit_spin = QSpinBox()
        self.memory_limit_spin.setRange(256, 8192)
        self.memory_limit_spin.setValue(self.config.get('memory_limit', 1024))
        self.memory_limit_spin.setSuffix(" MB")
        performance_layout.addRow("حد الذاكرة / Memory Limit:", self.memory_limit_spin)
        
        self.enable_caching_checkbox = QCheckBox("تمكين التخزين المؤقت / Enable Caching")
        self.enable_caching_checkbox.setChecked(self.config.get('enable_caching', True))
        performance_layout.addRow(self.enable_caching_checkbox)
        
        self.content_layout.addWidget(performance_group)
        
        # Debug Settings
        debug_group = QGroupBox("إعدادات التصحيح / Debug Settings")
        debug_layout = QFormLayout(debug_group)
        
        self.debug_mode_checkbox = QCheckBox("وضع التصحيح / Debug Mode")
        self.debug_mode_checkbox.setChecked(self.config.get('debug_mode', False))
        debug_layout.addRow(self.debug_mode_checkbox)
        
        self.log_level_combo = QComboBox()
        self.log_level_combo.addItems(['ERROR', 'WARNING', 'INFO', 'DEBUG'])
        self.log_level_combo.setCurrentText(self.config.get('log_level', 'INFO'))
        debug_layout.addRow("مستوى السجل / Log Level:", self.log_level_combo)
        
        self.enable_sql_logging_checkbox = QCheckBox("تسجيل استعلامات SQL / Log SQL Queries")
        self.enable_sql_logging_checkbox.setChecked(self.config.get('enable_sql_logging', False))
        debug_layout.addRow(self.enable_sql_logging_checkbox)
        
        self.content_layout.addWidget(debug_group)
        
        # Experimental Features
        experimental_group = QGroupBox("الميزات التجريبية / Experimental Features")
        experimental_layout = QFormLayout(experimental_group)
        
        self.enable_ai_features_checkbox = QCheckBox("تمكين ميزات الذكاء الاصطناعي / Enable AI Features")
        self.enable_ai_features_checkbox.setChecked(self.config.get('enable_ai_features', False))
        experimental_layout.addRow(self.enable_ai_features_checkbox)
        
        self.enable_voice_commands_checkbox = QCheckBox("تمكين الأوامر الصوتية / Enable Voice Commands")
        self.enable_voice_commands_checkbox.setChecked(self.config.get('enable_voice_commands', False))
        experimental_layout.addRow(self.enable_voice_commands_checkbox)
        
        self.content_layout.addWidget(experimental_group)
        
    def show_about_info(self):
        """Show about information"""
        about_layout = QVBoxLayout()
        
        # Application info
        app_info = QLabel("""
        <h2 style="color: #0078d4;">نظام إدارة مصنع الحسن ستون</h2>
        <h3 style="color: #0078d4;">Al-Hassan Stone Factory Management System</h3>
        
        <p><b>الإصدار / Version:</b> 2.0.0</p>
        <p><b>تاريخ الإصدار / Release Date:</b> 2024</p>
        <p><b>المطور / Developer:</b> Al-Hassan Stone Factory</p>
        
        <h4>الميزات الرئيسية / Key Features:</h4>
        <ul>
            <li>إدارة شاملة للعملاء والموردين / Comprehensive customer and supplier management</li>
            <li>تتبع المخزون والكتل / Inventory and block tracking</li>
            <li>تخطيط الإنتاج المتقدم / Advanced production planning</li>
            <li>مراقبة الجودة / Quality control</li>
            <li>إدارة الصيانة / Maintenance management</li>
            <li>التقارير والتحليلات / Reports and analytics</li>
            <li>دعم متعدد اللغات / Multi-language support</li>
            <li>النسخ الاحتياطي التلقائي / Automatic backup</li>
        </ul>
        
        <h4>التقنيات المستخدمة / Technologies Used:</h4>
        <ul>
            <li>Python 3.8+</li>
            <li>PyQt5</li>
            <li>SQLite</li>
            <li>Cryptography</li>
        </ul>
        
        <p style="color: #888888;">© 2024 Al-Hassan Stone Factory. All rights reserved.</p>
        """)
        
        about_layout.addWidget(about_info)
        
        # System information
        system_info_group = QGroupBox("معلومات النظام / System Information")
        system_info_layout = QFormLayout(system_info_group)
        
        import platform
        import sys
        
        system_info_layout.addRow("نظام التشغيل / OS:", QLabel(platform.system() + " " + platform.release()))
        system_info_layout.addRow("إصدار Python / Python Version:", QLabel(sys.version.split()[0]))
        system_info_layout.addRow("معمارية النظام / Architecture:", QLabel(platform.machine()))
        
        about_layout.addWidget(system_info_group)
        
        # License information
        license_group = QGroupBox("معلومات الترخيص / License Information")
        license_layout = QVBoxLayout(license_group)
        
        license_text = QLabel("""
        هذا البرنامج مرخص لشركة الحسن ستون فقط.
        This software is licensed exclusively to Al-Hassan Stone Factory.
        
        جميع الحقوق محفوظة. لا يجوز نسخ أو توزيع هذا البرنامج بدون إذن كتابي.
        All rights reserved. This software may not be copied or distributed without written permission.
        """)
        license_text.setWordWrap(True)
        license_layout.addWidget(license_text)
        
        about_layout.addWidget(license_group)
        
        self.content_layout.addLayout(about_layout)
        
    def load_settings(self):
        """Load current settings"""
        # This method will be called to populate all settings fields
        pass
        
    def apply_settings(self):
        """Apply all settings changes"""
        try:
            # Get current category
            current_item = self.category_list.currentItem()
            if not current_item:
                return
                
            category_key = current_item.data(Qt.UserRole)
            
            # Apply settings based on category
            if category_key == 'general':
                self.apply_general_settings()
            elif category_key == 'appearance':
                self.apply_appearance_settings()
            elif category_key == 'language':
                self.apply_language_settings()
            elif category_key == 'database':
                self.apply_database_settings()
            elif category_key == 'security':
                self.apply_security_settings()
            elif category_key == 'backup':
                self.apply_backup_settings()
            elif category_key == 'printing':
                self.apply_printing_settings()
            elif category_key == 'reports':
                self.apply_reports_settings()
            elif category_key == 'notifications':
                self.apply_notifications_settings()
            elif category_key == 'integration':
                self.apply_integration_settings()
            elif category_key == 'advanced':
                self.apply_advanced_settings()
                
            QMessageBox.information(self, "نجح / Success", 
                                  "تم حفظ الإعدادات بنجاح\nSettings saved successfully")
                                  
        except Exception as e:
            QMessageBox.critical(self, "خطأ / Error", 
                               f"فشل في حفظ الإعدادات\nFailed to save settings:\n{str(e)}")
            
    def apply_general_settings(self):
        """Apply general settings"""
        if hasattr(self, 'company_name_edit'):
            self.config.set('company_name', self.company_name_edit.text())
            self.config.set('company_address', self.company_address_edit.toPlainText())
            self.config.set('company_phone', self.company_phone_edit.text())
            self.config.set('company_email', self.company_email_edit.text())
            self.config.set('auto_save', self.auto_save_checkbox.isChecked())
            self.config.set('auto_save_interval', self.auto_save_interval_spin.value())
            self.config.set('confirm_delete', self.confirm_delete_checkbox.isChecked())
            self.config.set('show_splash', self.show_splash_checkbox.isChecked())
            self.config.set('default_currency', self.default_currency_combo.currentText())
            self.config.set('default_tax_rate', self.default_tax_rate_spin.value())
            self.config.set('default_payment_terms', self.default_payment_terms_spin.value())
            
    def apply_appearance_settings(self):
        """Apply appearance settings"""
        if hasattr(self, 'theme_combo'):
            theme_map = {0: 'dark', 1: 'light', 2: 'auto'}
            theme = theme_map.get(self.theme_combo.currentIndex(), 'dark')
            self.config.set('theme', theme)
            self.theme_changed.emit(theme)
            
            self.config.set('ui_font_size', self.ui_font_size_spin.value())
            self.config.set('report_font_size', self.report_font_size_spin.value())
            self.config.set('remember_window_size', self.remember_window_size_checkbox.isChecked())
            self.config.set('start_maximized', self.start_maximized_checkbox.isChecked())
            self.config.set('show_toolbar', self.show_toolbar_checkbox.isChecked())
            self.config.set('show_statusbar', self.show_statusbar_checkbox.isChecked())
            
    def apply_language_settings(self):
        """Apply language settings"""
        if hasattr(self, 'language_combo'):
            selected_lang = self.language_combo.currentData()
            if selected_lang:
                self.config.set_language(selected_lang)
                self.language_changed.emit(selected_lang)
                
            culture = self.culture_combo.currentData()
            if culture:
                self.config.set('culture', culture)
                cultural_manager.set_culture(culture)
                
    def apply_database_settings(self):
        """Apply database settings"""
        if hasattr(self, 'db_path_edit'):
            self.config.set_database_path(self.db_path_edit.text())
            self.config.set('db_cache_size', self.cache_size_spin.value())
            self.config.set('db_page_size', int(self.page_size_combo.currentText()))
            
    def apply_security_settings(self):
        """Apply security settings"""
        if hasattr(self, 'session_timeout_spin'):
            self.config.set('session_timeout', self.session_timeout_spin.value())
            self.config.set('max_login_attempts', self.max_login_attempts_spin.value())
            self.config.set('lockout_duration', self.lockout_duration_spin.value())
            self.config.set('min_password_length', self.min_password_length_spin.value())
            self.config.set('require_uppercase', self.require_uppercase_checkbox.isChecked())
            self.config.set('require_numbers', self.require_numbers_checkbox.isChecked())
            self.config.set('require_symbols', self.require_symbols_checkbox.isChecked())
            self.config.set('encrypt_sensitive_data', self.encrypt_sensitive_data_checkbox.isChecked())
            self.config.set('encryption_algorithm', self.encryption_algorithm_combo.currentText())
            self.config.set('enable_activity_logging', self.enable_activity_logging_checkbox.isChecked())
            self.config.set('log_retention_days', self.log_retention_days_spin.value())
            
    def apply_backup_settings(self):
        """Apply backup settings"""
        if hasattr(self, 'enable_auto_backup_checkbox'):
            self.config.set('enable_auto_backup', self.enable_auto_backup_checkbox.isChecked())
            
            frequency_map = {0: 'daily', 1: 'weekly', 2: 'monthly'}
            frequency = frequency_map.get(self.backup_frequency_combo.currentIndex(), 'daily')
            self.config.set('backup_frequency', frequency)
            
            self.config.set('backup_time', self.backup_time_edit.time().toString('hh:mm'))
            self.config.set_backup_directory(self.backup_directory_edit.text())
            self.config.set('max_backup_files', self.max_backup_files_spin.value())
            self.config.set('backup_compression', self.backup_compression_checkbox.isChecked())
            self.config.set('enable_cloud_backup', self.enable_cloud_backup_checkbox.isChecked())
            
    def apply_printing_settings(self):
        """Apply printing settings"""
        if hasattr(self, 'paper_size_combo'):
            self.config.set('paper_size', self.paper_size_combo.currentText())
            self.config.set('print_margins', self.print_margins_spin.value())
            self.config.set('print_color', self.print_color_checkbox.isChecked())
            
    def apply_reports_settings(self):
        """Apply reports settings"""
        if hasattr(self, 'default_report_format_combo'):
            self.config.set('default_report_format', self.default_report_format_combo.currentText())
            self.config.set('include_logo', self.include_logo_checkbox.isChecked())
            self.config.set('report_header', self.report_header_edit.toPlainText())
            self.config.set('report_footer', self.report_footer_edit.toPlainText())
            self.config.set('chart_animation', self.chart_animation_checkbox.isChecked())
            
    def apply_notifications_settings(self):
        """Apply notifications settings"""
        if hasattr(self, 'enable_notifications_checkbox'):
            self.config.set('enable_notifications', self.enable_notifications_checkbox.isChecked())
            self.config.set('notification_sound', self.notification_sound_checkbox.isChecked())
            self.config.set('notification_duration', self.notification_duration_spin.value())
            self.config.set('enable_email_notifications', self.enable_email_notifications_checkbox.isChecked())
            self.config.set('smtp_server', self.smtp_server_edit.text())
            self.config.set('smtp_port', self.smtp_port_spin.value())
            self.config.set('smtp_username', self.smtp_username_edit.text())
            
    def apply_integration_settings(self):
        """Apply integration settings"""
        if hasattr(self, 'enable_api_checkbox'):
            self.config.set('enable_api', self.enable_api_checkbox.isChecked())
            self.config.set('api_port', self.api_port_spin.value())
            self.config.set('api_key', self.api_key_edit.text())
            
    def apply_advanced_settings(self):
        """Apply advanced settings"""
        if hasattr(self, 'max_threads_spin'):
            self.config.set('max_threads', self.max_threads_spin.value())
            self.config.set('memory_limit', self.memory_limit_spin.value())
            self.config.set('enable_caching', self.enable_caching_checkbox.isChecked())
            self.config.set('debug_mode', self.debug_mode_checkbox.isChecked())
            self.config.set('log_level', self.log_level_combo.currentText())
            self.config.set('enable_sql_logging', self.enable_sql_logging_checkbox.isChecked())
            self.config.set('enable_ai_features', self.enable_ai_features_checkbox.isChecked())
            self.config.set('enable_voice_commands', self.enable_voice_commands_checkbox.isChecked())
            
    def cancel_changes(self):
        """Cancel settings changes"""
        reply = QMessageBox.question(
            self, "إلغاء التغييرات / Cancel Changes",
            "هل تريد إلغاء جميع التغييرات؟\nDo you want to cancel all changes?",
            QMessageBox.Yes | QMessageBox.No,
            QMessageBox.No
        )
        
        if reply == QMessageBox.Yes:
            self.load_settings()
            
    def reset_to_defaults(self):
        """Reset all settings to defaults"""
        reply = QMessageBox.question(
            self, "استعادة الافتراضي / Reset to Defaults",
            "هل تريد استعادة جميع الإعدادات إلى القيم الافتراضية؟\nDo you want to reset all settings to default values?",
            QMessageBox.Yes | QMessageBox.No,
            QMessageBox.No
        )
        
        if reply == QMessageBox.Yes:
            # Clear all settings
            self.config.settings.clear()
            
            # Reload interface
            current_item = self.category_list.currentItem()
            if current_item:
                category_key = current_item.data(Qt.UserRole)
                self.show_category_settings(category_key)
                
            QMessageBox.information(self, "تم / Done", 
                                  "تم استعادة الإعدادات الافتراضية\nDefault settings restored")
                                  
    def export_settings(self):
        """Export settings to file"""
        file_path, _ = QFileDialog.getSaveFileName(
            self, "تصدير الإعدادات / Export Settings",
            f"settings_backup_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json",
            "JSON Files (*.json)"
        )
        
        if file_path:
            try:
                settings_data = {}
                
                # Export all settings
                for key in self.config.settings.allKeys():
                    settings_data[key] = self.config.settings.value(key)
                    
                with open(file_path, 'w', encoding='utf-8') as f:
                    json.dump(settings_data, f, indent=2, ensure_ascii=False)
                    
                QMessageBox.information(self, "نجح / Success", 
                                      f"تم تصدير الإعدادات بنجاح\nSettings exported successfully to:\n{file_path}")
                                      
            except Exception as e:
                QMessageBox.critical(self, "خطأ / Error", 
                                   f"فشل في تصدير الإعدادات\nFailed to export settings:\n{str(e)}")
                                   
    def import_settings(self):
        """Import settings from file"""
        file_path, _ = QFileDialog.getOpenFileName(
            self, "استيراد الإعدادات / Import Settings",
            "", "JSON Files (*.json)"
        )
        
        if file_path:
            try:
                with open(file_path, 'r', encoding='utf-8') as f:
                    settings_data = json.load(f)
                    
                # Import settings
                for key, value in settings_data.items():
                    self.config.settings.setValue(key, value)
                    
                # Reload interface
                current_item = self.category_list.currentItem()
                if current_item:
                    category_key = current_item.data(Qt.UserRole)
                    self.show_category_settings(category_key)
                    
                QMessageBox.information(self, "نجح / Success", 
                                      "تم استيراد الإعدادات بنجاح\nSettings imported successfully")
                                      
            except Exception as e:
                QMessageBox.critical(self, "خطأ / Error", 
                                   f"فشل في استيراد الإعدادات\nFailed to import settings:\n{str(e)}")
                                   
    # Color and font selection methods
    def choose_primary_color(self):
        """Choose primary color"""
        color = QColorDialog.getColor(QColor("#0078d4"), self, "اختيار اللون الأساسي / Choose Primary Color")
        if color.isValid():
            self.config.set('primary_color', color.name())
            self.primary_color_btn.setStyleSheet(f"background-color: {color.name()};")
            
    def choose_accent_color(self):
        """Choose accent color"""
        color = QColorDialog.getColor(QColor("#28a745"), self, "اختيار اللون الثانوي / Choose Accent Color")
        if color.isValid():
            self.config.set('accent_color', color.name())
            self.accent_color_btn.setStyleSheet(f"background-color: {color.name()};")
            
    def choose_main_font(self):
        """Choose main font"""
        font, ok = QFontDialog.getFont(QFont("Arial", 10), self, "اختيار الخط الأساسي / Choose Main Font")
        if ok:
            self.config.set('main_font_family', font.family())
            self.config.set('main_font_size', font.pointSize())
            self.main_font_btn.setText(f"{font.family()} - {font.pointSize()}pt")
            
    # Database methods
    def browse_database_path(self):
        """Browse for database path"""
        file_path, _ = QFileDialog.getSaveFileName(
            self, "اختيار مسار قاعدة البيانات / Choose Database Path",
            self.config.get_database_path(),
            "SQLite Database (*.db)"
        )
        
        if file_path:
            self.db_path_edit.setText(file_path)
            
    def test_database_connection(self):
        """Test database connection"""
        try:
            db_path = self.db_path_edit.text()
            conn = sqlite3.connect(db_path)
            conn.execute("SELECT 1")
            conn.close()
            
            QMessageBox.information(self, "نجح / Success", 
                                  "تم الاتصال بقاعدة البيانات بنجاح\nDatabase connection successful")
                                  
        except Exception as e:
            QMessageBox.critical(self, "خطأ / Error", 
                               f"فشل في الاتصال بقاعدة البيانات\nDatabase connection failed:\n{str(e)}")
                               
    def load_database_info(self):
        """Load database information"""
        try:
            db_path = self.config.get_database_path()
            if os.path.exists(db_path):
                file_size = os.path.getsize(db_path)
                file_size_mb = file_size / (1024 * 1024)
                
                conn = sqlite3.connect(db_path)
                cursor = conn.cursor()
                
                # Get table count
                cursor.execute("SELECT COUNT(*) FROM sqlite_master WHERE type='table'")
                table_count = cursor.fetchone()[0]
                
                # Get total records (approximate)
                cursor.execute("""
                    SELECT SUM(seq) FROM sqlite_sequence
                """)
                result = cursor.fetchone()
                total_records = result[0] if result and result[0] else 0
                
                conn.close()
                
                info_text = f"""
حجم قاعدة البيانات / Database Size: {file_size_mb:.2f} MB
عدد الجداول / Tables: {table_count}
إجمالي السجلات / Total Records: {total_records:,}
آخر تعديل / Last Modified: {datetime.fromtimestamp(os.path.getmtime(db_path)).strftime('%Y-%m-%d %H:%M:%S')}
                """
                
                self.db_info_label.setText(info_text.strip())
                
            else:
                self.db_info_label.setText("قاعدة البيانات غير موجودة\nDatabase not found")
                
        except Exception as e:
            self.db_info_label.setText(f"خطأ في تحميل معلومات قاعدة البيانات\nError loading database info:\n{str(e)}")
            
    def vacuum_database(self):
        """Vacuum database"""
        try:
            conn = sqlite3.connect(self.config.get_database_path())
            conn.execute("VACUUM")
            conn.close()
            
            QMessageBox.information(self, "نجح / Success", 
                                  "تم ضغط قاعدة البيانات بنجاح\nDatabase vacuumed successfully")
            self.load_database_info()
            
        except Exception as e:
            QMessageBox.critical(self, "خطأ / Error", 
                               f"فشل في ضغط قاعدة البيانات\nFailed to vacuum database:\n{str(e)}")
                               
    def reindex_database(self):
        """Reindex database"""
        try:
            conn = sqlite3.connect(self.config.get_database_path())
            conn.execute("REINDEX")
            conn.close()
            
            QMessageBox.information(self, "نجح / Success", 
                                  "تم إعادة فهرسة قاعدة البيانات بنجاح\nDatabase reindexed successfully")
                                  
        except Exception as e:
            QMessageBox.critical(self, "خطأ / Error", 
                               f"فشل في إعادة فهرسة قاعدة البيانات\nFailed to reindex database:\n{str(e)}")
                               
    def analyze_database(self):
        """Analyze database"""
        try:
            conn = sqlite3.connect(self.config.get_database_path())
            conn.execute("ANALYZE")
            conn.close()
            
            QMessageBox.information(self, "نجح / Success", 
                                  "تم تحليل قاعدة البيانات بنجاح\nDatabase analyzed successfully")
                                  
        except Exception as e:
            QMessageBox.critical(self, "خطأ / Error", 
                               f"فشل في تحليل قاعدة البيانات\nFailed to analyze database:\n{str(e)}")
                               
    # Backup methods
    def browse_backup_directory(self):
        """Browse for backup directory"""
        directory = QFileDialog.getExistingDirectory(
            self, "اختيار مجلد النسخ الاحتياطي / Choose Backup Directory",
            self.config.get_backup_directory()
        )
        
        if directory:
            self.backup_directory_edit.setText(directory)
            
    def setup_cloud_credentials(self):
        """Setup cloud backup credentials"""
        QMessageBox.information(self, "قريباً / Coming Soon", 
                              "إعداد بيانات اعتماد النسخ السحابي سيتوفر قريباً\nCloud backup credentials setup coming soon")
                              
    # Security methods
    def generate_encryption_key(self):
        """Generate new encryption key"""
        reply = QMessageBox.question(
            self, "إنشاء مفتاح جديد / Generate New Key",
            "تحذير: إنشاء مفتاح تشفير جديد سيجعل البيانات المشفرة الحالية غير قابلة للقراءة.\nWarning: Generating a new encryption key will make current encrypted data unreadable.\n\nهل تريد المتابعة؟\nDo you want to continue?",
            QMessageBox.Yes | QMessageBox.No,
            QMessageBox.No
        )
        
        if reply == QMessageBox.Yes:
            try:
                from cryptography.fernet import Fernet
                new_key = Fernet.generate_key()
                
                # Save new key
                key_file = "config/encryption.key"
                os.makedirs(os.path.dirname(key_file), exist_ok=True)
                with open(key_file, 'wb') as f:
                    f.write(new_key)
                    
                QMessageBox.information(self, "نجح / Success", 
                                      "تم إنشاء مفتاح التشفير الجديد بنجاح\nNew encryption key generated successfully")
                                      
            except Exception as e:
                QMessageBox.critical(self, "خطأ / Error", 
                                   f"فشل في إنشاء مفتاح التشفير\nFailed to generate encryption key:\n{str(e)}")
                                   
    # Integration methods
    def generate_api_key(self):
        """Generate new API key"""
        import secrets
        import string
        
        alphabet = string.ascii_letters + string.digits
        api_key = ''.join(secrets.choice(alphabet) for _ in range(32))
        
        self.api_key_edit.setText(api_key)
        
    def test_email_settings(self):
        """Test email settings"""
        QMessageBox.information(self, "قريباً / Coming Soon", 
                              "اختبار إعدادات البريد الإلكتروني سيتوفر قريباً\nEmail settings test coming soon")
                              
    def apply_settings_style(self):
        """Apply settings-specific styling"""
        self.setStyleSheet("""
            QWidget {
                background-color: #2b2b2b;
                color: #ffffff;
                font-family: 'Segoe UI', Arial, sans-serif;
            }
            
            QGroupBox {
                font-weight: bold;
                border: 2px solid #555555;
                border-radius: 8px;
                margin-top: 1ex;
                padding-top: 10px;
                margin-bottom: 10px;
            }
            
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 10px;
                padding: 0 5px 0 5px;
                color: #0078d4;
            }
            
            QLineEdit, QTextEdit, QComboBox, QSpinBox, QDoubleSpinBox {
                background-color: #404040;
                border: 1px solid #555555;
                border-radius: 4px;
                padding: 6px;
                color: white;
            }
            
            QLineEdit:focus, QTextEdit:focus, QComboBox:focus, QSpinBox:focus, QDoubleSpinBox:focus {
                border-color: #0078d4;
            }
            
            QPushButton {
                background-color: #0078d4;
                color: white;
                border: none;
                padding: 8px 16px;
                border-radius: 4px;
                font-weight: bold;
            }
            
            QPushButton:hover {
                background-color: #106ebe;
            }
            
            QPushButton:pressed {
                background-color: #005a9e;
            }
            
            QCheckBox {
                spacing: 8px;
            }
            
            QCheckBox::indicator {
                width: 18px;
                height: 18px;
                border-radius: 3px;
                border: 2px solid #555555;
                background-color: #404040;
            }
            
            QCheckBox::indicator:checked {
                background-color: #0078d4;
                border-color: #0078d4;
            }
            
            QCheckBox::indicator:checked::after {
                content: "✓";
                color: white;
                font-weight: bold;
            }
            
            QScrollArea {
                border: none;
                background-color: transparent;
            }
            
            QScrollBar:vertical {
                background-color: #404040;
                width: 12px;
                border-radius: 6px;
            }
            
            QScrollBar::handle:vertical {
                background-color: #0078d4;
                border-radius: 6px;
                min-height: 20px;
            }
            
            QScrollBar::handle:vertical:hover {
                background-color: #106ebe;
            }
        """)
