"""
Build script to create standalone executable using PyInstaller and cx_Freeze
Run: python build_exe.py
"""

import PyInstaller.__main__
import os
import shutil
import sys
from cx_Freeze import setup, Executable

def build_executable():
    """Build standalone executable"""
    
    # Clean previous builds
    if os.path.exists('dist'):
        shutil.rmtree('dist')
    if os.path.exists('build'):
        shutil.rmtree('build')
        
    # PyInstaller arguments
    pyinstaller_args = [
        'main.py',
        '--name=AlHassanStoneFactory',
        '--windowed',
        '--onefile',
        '--icon=assets/icon.ico',
        '--add-data=assets;assets',
        '--add-data=config;config',
        '--hidden-import=PyQt5.sip',
        '--hidden-import=sqlite3',
        '--distpath=dist',
        '--workpath=build',
        '--specpath=build',
        '--clean'
    ]
    
    # Run PyInstaller
    PyInstaller.__main__.run(pyinstaller_args)
    
    # cx_Freeze setup
    build_exe_options = {
        "packages": [
            "PyQt5", "sqlite3", "datetime", "json", "hashlib", "os", "sys",
            "cryptography", "PIL", "reportlab", "openpyxl", "babel", "pytz", "qrcode"
        ],
        "excludes": ["tkinter", "matplotlib", "numpy"],
        "include_files": [
            ("translations/", "translations/"),
            ("assets/", "assets/"),
            ("config/", "config/"),
            ("README.md", "README.md")
        ],
        "optimize": 2,
        "zip_include_packages": ["*"],
        "zip_exclude_packages": []
    }

    # GUI applications require a different base on Windows (the default is for a console application).
    base = None
    if sys.platform == "win32":
        base = "Win32GUI"

    setup(
        name="Al-Hassan Stone Factory Management System",
        version="2.0.0",
        description="Comprehensive Stone Factory Management System",
        author="Al-Hassan Stone Factory",
        options={"build_exe": build_exe_options},
        executables=[
            Executable(
                "main.py",
                base=base,
                target_name="AlHassanStone.exe",
                icon="assets/icon.ico" if os.path.exists("assets/icon.ico") else None,
                shortcut_name="Al-Hassan Stone Factory",
                shortcut_dir="DesktopFolder"
            )
        ]
    )
    
    print("Build completed successfully!")
    print("Executable location: dist/AlHassanStone.exe")

if __name__ == "__main__":
    build_executable()

