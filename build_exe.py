"""
Build script to create standalone executable using PyInstaller and cx_Freeze
Run: python build_exe.py
"""

import PyInstaller.__main__
import os
import shutil

def build_executable():
    """Build standalone executable"""

    # Clean previous builds
    try:
        if os.path.exists('dist'):
            shutil.rmtree('dist')
    except PermissionError:
        print("Warning: Could not remove dist directory. It may be in use.")
        print("Please close any running instances of the application and try again.")
        return

    try:
        if os.path.exists('build'):
            shutil.rmtree('build')
    except PermissionError:
        print("Warning: Could not remove build directory. Continuing anyway.")

    # PyInstaller arguments
    pyinstaller_args = [
        'main.py',
        '--name=AlHassanStoneFactory',
        '--windowed',
        '--onefile',
        '--hidden-import=PyQt5.sip',
        '--hidden-import=sqlite3',
        '--hidden-import=PyQt5.QtCore',
        '--hidden-import=PyQt5.QtGui',
        '--hidden-import=PyQt5.QtWidgets',
        '--distpath=dist',
        '--workpath=build',
        '--specpath=build',
        '--clean'
    ]

    # Add icon if it exists (commented out due to permission issues)
    # if os.path.exists('assets/icon.ico'):
    #     pyinstaller_args.append('--icon=assets/icon.ico')

    # Add data directories if they exist (commented out for now to avoid path issues)
    # if os.path.exists('translations'):
    #     pyinstaller_args.append('--add-data=translations;translations')

    print("Building executable with PyInstaller...")
    print("Arguments:", ' '.join(pyinstaller_args))

    # Run PyInstaller
    PyInstaller.__main__.run(pyinstaller_args)

    print("Build completed successfully!")
    print("Executable location: dist/AlHassanStoneFactory.exe")

if __name__ == "__main__":
    build_executable()

