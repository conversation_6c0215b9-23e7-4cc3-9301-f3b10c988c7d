from PyQt5.QtWidgets import (QWidget, QVBoxLayout, QHBoxLayout, QPushButton, 
                            QTableWidget, QTableWidgetItem, QLineEdit, QLabel,
                            QFormLayout, QDialog, QMessageBox, QComboBox,
                            QGroupBox, QDateEdit, QTextEdit, QHeaderView,
                            QSpinBox, QDoubleSpinBox, QTabWidget, QProgressBar)
from PyQt5.QtCore import Qt, QDate, QTimer
from PyQt5.QtGui import QFont, QColor
import sqlite3
from datetime import datetime, timedelta

class InventoryAdjustmentDialog(QDialog):
    def __init__(self, parent=None, translator=None):
        super().__init__(parent)
        self.translator = translator
        self.init_ui()
        
    def init_ui(self):
        self.setWindowTitle("تعديل المخزون")
        self.setModal(True)
        self.resize(400, 300)
        
        layout = QVBoxLayout()
        
        form_layout = QFormLayout()
        
        self.item_combo = QComboBox()
        self.adjustment_type_combo = QComboBox()
        self.adjustment_type_combo.addItems(["إضافة", "خصم", "تصحيح"])
        
        self.quantity_edit = QSpinBox()
        self.quantity_edit.setMaximum(9999)
        
        self.reason_edit = QTextEdit()
        self.notes_edit = QTextEdit()
        
        form_layout.addRow("الصنف:", self.item_combo)
        form_layout.addRow("نوع التعديل:", self.adjustment_type_combo)
        form_layout.addRow("الكمية:", self.quantity_edit)
        form_layout.addRow("السبب:", self.reason_edit)
        form_layout.addRow("ملاحظات:", self.notes_edit)
        
        layout.addLayout(form_layout)
        
        # Buttons
        button_layout = QHBoxLayout()
        self.save_btn = QPushButton("حفظ")
        self.cancel_btn = QPushButton("إلغاء")
        
        self.save_btn.clicked.connect(self.accept)
        self.cancel_btn.clicked.connect(self.reject)
        
        button_layout.addWidget(self.save_btn)
        button_layout.addWidget(self.cancel_btn)
        layout.addLayout(button_layout)
        
        self.setLayout(layout)
        
    def get_data(self):
        return {
            'item_id': self.item_combo.currentData(),
            'adjustment_type': self.adjustment_type_combo.currentText(),
            'quantity': self.quantity_edit.value(),
            'reason': self.reason_edit.toPlainText(),
            'notes': self.notes_edit.toPlainText()
        }

class InventoryModule(QWidget):
    def __init__(self, db_manager, translator, parent=None):
        super().__init__(parent)
        self.db_manager = db_manager
        self.translator = translator
        self.init_ui()
        self.load_inventory()
        
        # Auto-refresh timer
        self.refresh_timer = QTimer()
        self.refresh_timer.timeout.connect(self.load_inventory)
        self.refresh_timer.start(30000)  # Refresh every 30 seconds
        
    def init_ui(self):
        layout = QVBoxLayout()
        
        # Header
        header_layout = QHBoxLayout()
        title_label = QLabel("إدارة المخزون")
        title_label.setFont(QFont("Arial", 16, QFont.Bold))
        header_layout.addWidget(title_label)
        header_layout.addStretch()
        
        # Search and filters
        self.search_edit = QLineEdit()
        self.search_edit.setPlaceholderText("البحث في المخزون...")
        self.search_edit.textChanged.connect(self.search_inventory)
        
        self.category_filter = QComboBox()
        self.category_filter.addItems(["الكل", "كتل", "بلاطات", "مواد خام", "أدوات"])
        self.category_filter.currentTextChanged.connect(self.filter_inventory)
        
        self.stock_level_filter = QComboBox()
        self.stock_level_filter.addItems(["الكل", "مخزون منخفض", "نفد المخزون", "مخزون عادي"])
        self.stock_level_filter.currentTextChanged.connect(self.filter_inventory)
        
        header_layout.addWidget(QLabel("البحث:"))
        header_layout.addWidget(self.search_edit)
        header_layout.addWidget(QLabel("الفئة:"))
        header_layout.addWidget(self.category_filter)
        header_layout.addWidget(QLabel("مستوى المخزون:"))
        header_layout.addWidget(self.stock_level_filter)
        
        layout.addLayout(header_layout)
        
        # Buttons
        button_layout = QHBoxLayout()
        self.adjust_btn = QPushButton("تعديل المخزون")
        self.reorder_btn = QPushButton("طلب إعادة تموين")
        self.refresh_btn = QPushButton("تحديث")
        self.export_btn = QPushButton("تصدير")
        self.alerts_btn = QPushButton("تنبيهات المخزون")
        
        self.adjust_btn.clicked.connect(self.adjust_inventory)
        self.reorder_btn.clicked.connect(self.create_reorder)
        self.refresh_btn.clicked.connect(self.load_inventory)
        self.export_btn.clicked.connect(self.export_inventory)
        self.alerts_btn.clicked.connect(self.show_alerts)
        
        button_layout.addWidget(self.adjust_btn)
        button_layout.addWidget(self.reorder_btn)
        button_layout.addWidget(self.refresh_btn)
        button_layout.addWidget(self.export_btn)
        button_layout.addWidget(self.alerts_btn)
        button_layout.addStretch()
        
        layout.addLayout(button_layout)
        
        # Create tabs
        tab_widget = QTabWidget()
        
        # Current Stock Tab
        stock_tab = QWidget()
        stock_layout = QVBoxLayout()
        
        self.stock_table = QTableWidget()
        self.stock_table.setColumnCount(9)
        self.stock_table.setHorizontalHeaderLabels([
            "الرقم", "الصنف", "الفئة", "الكمية الحالية", "الحد الأدنى", 
            "الحد الأقصى", "متوسط التكلفة", "القيمة الإجمالية", "الحالة"
        ])
        
        # Set table properties
        self.stock_table.setSelectionBehavior(QTableWidget.SelectRows)
        self.stock_table.setAlternatingRowColors(True)
        self.stock_table.horizontalHeader().setStretchLastSection(True)
        self.stock_table.horizontalHeader().setSectionResizeMode(QHeaderView.Stretch)
        
        stock_layout.addWidget(self.stock_table)
        stock_tab.setLayout(stock_layout)
        tab_widget.addTab(stock_tab, "المخزون الحالي")
        
        # Movement History Tab
        movement_tab = QWidget()
        movement_layout = QVBoxLayout()
        
        self.movement_table = QTableWidget()
        self.movement_table.setColumnCount(7)
        self.movement_table.setHorizontalHeaderLabels([
            "التاريخ", "الصنف", "النوع", "الكمية", "السبب", "المستخدم", "ملاحظات"
        ])
        
        self.movement_table.setSelectionBehavior(QTableWidget.SelectRows)
        self.movement_table.setAlternatingRowColors(True)
        self.movement_table.horizontalHeader().setStretchLastSection(True)
        self.movement_table.horizontalHeader().setSectionResizeMode(QHeaderView.Stretch)
        
        movement_layout.addWidget(self.movement_table)
        movement_tab.setLayout(movement_layout)
        tab_widget.addTab(movement_tab, "حركة المخزون")
        
        layout.addWidget(tab_widget)
        
        # Statistics
        stats_layout = QHBoxLayout()
        self.total_items_label = QLabel("إجمالي الأصناف: 0")
        self.low_stock_label = QLabel("مخزون منخفض: 0")
        self.out_of_stock_label = QLabel("نفد المخزون: 0")
        self.total_value_label = QLabel("القيمة الإجمالية: 0 ريال")
        
        stats_layout.addWidget(self.total_items_label)
        stats_layout.addWidget(self.low_stock_label)
        stats_layout.addWidget(self.out_of_stock_label)
        stats_layout.addWidget(self.total_value_label)
        stats_layout.addStretch()
        
        layout.addLayout(stats_layout)
        
        self.setLayout(layout)
        
    def load_inventory(self):
        self.load_stock()
        self.load_movements()
        
    def load_stock(self):
        try:
            conn = self.db_manager.get_connection()
            cursor = conn.cursor()
            
            # Load current stock levels
            cursor.execute("""
                SELECT i.id, i.name, i.category, i.current_quantity, i.min_quantity,
                       i.max_quantity, i.average_cost, 
                       (i.current_quantity * i.average_cost) as total_value,
                       CASE 
                           WHEN i.current_quantity = 0 THEN 'نفد المخزون'
                           WHEN i.current_quantity <= i.min_quantity THEN 'مخزون منخفض'
                           ELSE 'مخزون عادي'
                       END as status
                FROM inventory_items i
                ORDER BY i.name
            """)
            
            items = cursor.fetchall()
            
            self.stock_table.setRowCount(len(items))
            
            for row, item in enumerate(items):
                for col, value in enumerate(item):
                    if col in [6, 7]:  # Cost and value columns
                        value = f"{value:,.2f} ريال"
                    
                    table_item = QTableWidgetItem(str(value))
                    table_item.setTextAlignment(Qt.AlignCenter)
                    
                    # Color coding for status
                    if col == 8:  # status column
                        if value == "نفد المخزون":
                            table_item.setBackground(QColor(255, 200, 200))
                        elif value == "مخزون منخفض":
                            table_item.setBackground(QColor(255, 255, 200))
                        else:
                            table_item.setBackground(QColor(200, 255, 200))
                    
                    self.stock_table.setItem(row, col, table_item)
            
            # Update statistics
            self.update_statistics(items)
            
            conn.close()
            
        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"خطأ في تحميل المخزون: {str(e)}")
            
    def load_movements(self):
        try:
            conn = self.db_manager.get_connection()
            cursor = conn.cursor()
            
            cursor.execute("""
                SELECT im.movement_date, ii.name, im.movement_type, im.quantity,
                       im.reason, im.user_name, im.notes
                FROM inventory_movements im
                JOIN inventory_items ii ON im.item_id = ii.id
                ORDER BY im.movement_date DESC
                LIMIT 100
            """)
            
            movements = cursor.fetchall()
            
            self.movement_table.setRowCount(len(movements))
            
            for row, movement in enumerate(movements):
                for col, value in enumerate(movement):
                    if col == 0:  # Date column
                        value = str(value)[:19]  # Show date and time
                    
                    item = QTableWidgetItem(str(value))
                    item.setTextAlignment(Qt.AlignCenter)
                    
                    # Color coding for movement type
                    if col == 2:  # movement type column
                        if value == "إضافة":
                            item.setBackground(QColor(200, 255, 200))
                        elif value == "خصم":
                            item.setBackground(QColor(255, 200, 200))
                        else:
                            item.setBackground(QColor(200, 200, 255))
                    
                    self.movement_table.setItem(row, col, item)
            
            conn.close()
            
        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"خطأ في تحميل حركة المخزون: {str(e)}")
            
    def update_statistics(self, items):
        total_items = len(items)
        low_stock = sum(1 for item in items if item[8] == "مخزون منخفض")
        out_of_stock = sum(1 for item in items if item[8] == "نفد المخزون")
        total_value = sum(item[7] for item in items if isinstance(item[7], (int, float)))
        
        self.total_items_label.setText(f"إجمالي الأصناف: {total_items}")
        self.low_stock_label.setText(f"مخزون منخفض: {low_stock}")
        self.out_of_stock_label.setText(f"نفد المخزون: {out_of_stock}")
        self.total_value_label.setText(f"القيمة الإجمالية: {total_value:,.2f} ريال")
        
    def search_inventory(self):
        search_text = self.search_edit.text().lower()
        
        for row in range(self.stock_table.rowCount()):
            show_row = False
            for col in range(self.stock_table.columnCount()):
                item = self.stock_table.item(row, col)
                if item and search_text in item.text().lower():
                    show_row = True
                    break
            self.stock_table.setRowHidden(row, not show_row)
            
    def filter_inventory(self):
        category_filter = self.category_filter.currentText()
        stock_level_filter = self.stock_level_filter.currentText()
        
        for row in range(self.stock_table.rowCount()):
            show_row = True
            
            # Filter by category
            if category_filter != "الكل":
                category_item = self.stock_table.item(row, 2)  # category column
                if category_item and category_item.text() != category_filter:
                    show_row = False
            
            # Filter by stock level
            if stock_level_filter != "الكل" and show_row:
                status_item = self.stock_table.item(row, 8)  # status column
                if status_item:
                    if stock_level_filter == "مخزون منخفض" and status_item.text() != "مخزون منخفض":
                        show_row = False
                    elif stock_level_filter == "نفد المخزون" and status_item.text() != "نفد المخزون":
                        show_row = False
                    elif stock_level_filter == "مخزون عادي" and status_item.text() != "مخزون عادي":
                        show_row = False
            
            self.stock_table.setRowHidden(row, not show_row)
            
    def adjust_inventory(self):
        dialog = InventoryAdjustmentDialog(self, self.translator)
        
        # Load items into combo box
        try:
            conn = self.db_manager.get_connection()
            cursor = conn.cursor()
            cursor.execute("SELECT id, name FROM inventory_items ORDER BY name")
            items = cursor.fetchall()
            
            for item in items:
                dialog.item_combo.addItem(item[1], item[0])
                
            conn.close()
        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"خطأ في تحميل الأصناف: {str(e)}")
            return
        
        if dialog.exec_() == QDialog.Accepted:
            data = dialog.get_data()
            self.save_inventory_adjustment(data)
            
    def save_inventory_adjustment(self, data):
        try:
            conn = self.db_manager.get_connection()
            cursor = conn.cursor()
            
            # Get current quantity
            cursor.execute("SELECT current_quantity FROM inventory_items WHERE id=?", 
                         (data['item_id'],))
            result = cursor.fetchone()
            
            if not result:
                QMessageBox.warning(self, "خطأ", "الصنف غير موجود")
                return
                
            current_quantity = result[0]
            
            # Calculate new quantity
            if data['adjustment_type'] == "إضافة":
                new_quantity = current_quantity + data['quantity']
            elif data['adjustment_type'] == "خصم":
                new_quantity = max(0, current_quantity - data['quantity'])
            else:  # تصحيح
                new_quantity = data['quantity']
            
            # Update inventory
            cursor.execute("""
                UPDATE inventory_items 
                SET current_quantity=?, last_updated=CURRENT_TIMESTAMP
                WHERE id=?
            """, (new_quantity, data['item_id']))
            
            # Record movement
            cursor.execute("""
                INSERT INTO inventory_movements 
                (item_id, movement_type, quantity, reason, notes, user_name)
                VALUES (?, ?, ?, ?, ?, ?)
            """, (
                data['item_id'], data['adjustment_type'], data['quantity'],
                data['reason'], data['notes'], "المستخدم الحالي"
            ))
            
            conn.commit()
            conn.close()
            
            QMessageBox.information(self, "نجح", "تم تعديل المخزون بنجاح")
            self.load_inventory()
            
        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"خطأ في تعديل المخزون: {str(e)}")
            
    def create_reorder(self):
        QMessageBox.information(self, "طلب إعادة تموين", "سيتم تطوير ميزة طلب إعادة التموين قريباً")
        
    def export_inventory(self):
        QMessageBox.information(self, "تصدير", "سيتم تطوير ميزة التصدير قريباً")
        
    def show_alerts(self):
        try:
            conn = self.db_manager.get_connection()
            cursor = conn.cursor()
            
            # Get low stock and out of stock items
            cursor.execute("""
                SELECT name, current_quantity, min_quantity
                FROM inventory_items
                WHERE current_quantity <= min_quantity
                ORDER BY current_quantity
            """)
            
            alerts = cursor.fetchall()
            conn.close()
            
            if not alerts:
                QMessageBox.information(self, "تنبيهات المخزون", "لا توجد تنبيهات حالياً")
                return
            
            alert_text = "تنبيهات المخزون:\n\n"
            for alert in alerts:
                if alert[1] == 0:
                    alert_text += f"⚠️ نفد المخزون: {alert[0]}\n"
                else:
                    alert_text += f"⚠️ مخزون منخفض: {alert[0]} (الكمية: {alert[1]}, الحد الأدنى: {alert[2]})\n"
            
            QMessageBox.warning(self, "تنبيهات المخزون", alert_text)
            
        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"خطأ في جلب التنبيهات: {str(e)}")
