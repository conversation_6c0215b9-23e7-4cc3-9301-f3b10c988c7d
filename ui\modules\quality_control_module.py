from PyQt5.QtWidgets import (QWidget, QVBoxLayout, QHBoxLayout, QTableWidget, 
                            QTableWidgetItem, QPushButton, QLineEdit, QLabel,
                            QDialog, QFormLayout, QMessageBox, QHeaderView,
                            QDateEdit, QDoubleSpinBox, QComboBox, QTextEdit,
                            QSpinBox, QTabWidget, QGroupBox, QGridLayout,
                            QSlider, QProgressBar)
from PyQt5.QtCore import Qt, QDate, pyqtSignal
from PyQt5.QtGui import QFont, QColor, QPixmap, QPainter
from datetime import datetime
import json

class QualityControlModule(QWidget):
    def __init__(self, db_manager, user_data):
        super().__init__()
        self.db_manager = db_manager
        self.user_data = user_data
        self.init_ui()
        self.load_inspections()
        
    def init_ui(self):
        layout = QVBoxLayout()
        
        # Header
        header_layout = QHBoxLayout()
        
        title = QLabel("مراقبة الجودة / Quality Control")
        title.setStyleSheet("font-size: 16pt; font-weight: bold; color: #0078d4;")
        header_layout.addWidget(title)
        
        header_layout.addStretch()
        
        # New inspection button
        new_inspection_btn = QPushButton("فحص جديد / New Inspection")
        new_inspection_btn.setStyleSheet("background-color: #28a745;")
        new_inspection_btn.clicked.connect(self.create_new_inspection)
        header_layout.addWidget(new_inspection_btn)
        
        # Quality report button
        quality_report_btn = QPushButton("تقرير الجودة / Quality Report")
        quality_report_btn.setStyleSheet("background-color: #17a2b8;")
        quality_report_btn.clicked.connect(self.generate_quality_report)
        header_layout.addWidget(quality_report_btn)
        
        layout.addLayout(header_layout)
        
        # Create tabs
        self.tab_widget = QTabWidget()
        
        # Inspections tab
        inspections_tab = self.create_inspections_tab()
        self.tab_widget.addTab(inspections_tab, "الفحوصات / Inspections")
        
        # Quality metrics tab
        metrics_tab = self.create_metrics_tab()
        self.tab_widget.addTab(metrics_tab, "مؤشرات الجودة / Quality Metrics")
        
        # Defects tracking tab
        defects_tab = self.create_defects_tab()
        self.tab_widget.addTab(defects_tab, "تتبع العيوب / Defects Tracking")
        
        layout.addWidget(self.tab_widget)
        
        self.setLayout(layout)
        
    def create_inspections_tab(self):
        """Create inspections tab"""
        widget = QWidget()
        layout = QVBoxLayout(widget)
        
        # Filter controls
        filter_layout = QHBoxLayout()
        
        status_label = QLabel("الحالة / Status:")
        self.status_filter = QComboBox()
        self.status_filter.addItems([
            "الكل / All", "مقبول / Passed", "مرفوض / Failed", "يحتاج إعادة فحص / Retest"
        ])
        self.status_filter.currentTextChanged.connect(self.filter_inspections)
        
        grade_label = QLabel("الدرجة / Grade:")
        self.grade_filter = QComboBox()
        self.grade_filter.addItems([
            "الكل / All", "A", "B", "C", "D"
        ])
        self.grade_filter.currentTextChanged.connect(self.filter_inspections)
        
        filter_layout.addWidget(status_label)
        filter_layout.addWidget(self.status_filter)
        filter_layout.addWidget(grade_label)
        filter_layout.addWidget(self.grade_filter)
        filter_layout.addStretch()
        
        layout.addLayout(filter_layout)
        
        # Inspections table
        self.inspections_table = QTableWidget()
        self.inspections_table.setColumnCount(8)
        self.inspections_table.setHorizontalHeaderLabels([
            "رقم الفحص / Inspection No.", "رقم البلاطة / Slab No.", 
            "المفتش / Inspector", "تاريخ الفحص / Inspection Date",
            "الدرجة الكلية / Overall Grade", "جودة السطح / Surface Quality",
            "الحالة / Status", "الإجراءات / Actions"
        ])
        
        # Set column widths
        header = self.inspections_table.horizontalHeader()
        header.setSectionResizeMode(QHeaderView.Stretch)
        
        layout.addWidget(self.inspections_table)
        
        return widget
        
    def create_metrics_tab(self):
        """Create quality metrics tab"""
        widget = QWidget()
        layout = QVBoxLayout(widget)
        
        # Quality metrics cards
        metrics_layout = QGridLayout()
        
        # Pass rate card
        pass_rate_card = self.create_metric_card("معدل النجاح / Pass Rate", "0%", "#28a745")
        metrics_layout.addWidget(pass_rate_card, 0, 0)
        
        # Average grade card
        avg_grade_card = self.create_metric_card("متوسط الدرجة / Average Grade", "A", "#17a2b8")
        metrics_layout.addWidget(avg_grade_card, 0, 1)
        
        # Defect rate card
        defect_rate_card = self.create_metric_card("معدل العيوب / Defect Rate", "0%", "#ffc107")
        metrics_layout.addWidget(defect_rate_card, 0, 2)
        
        # Total inspections card
        total_inspections_card = self.create_metric_card("إجمالي الفحوصات / Total Inspections", "0", "#6f42c1")
        metrics_layout.addWidget(total_inspections_card, 1, 0)
        
        layout.addLayout(metrics_layout)
        
        # Quality trend chart placeholder
        chart_group = QGroupBox("اتجاه الجودة / Quality Trend")
        chart_layout = QVBoxLayout(chart_group)
        
        self.quality_chart_label = QLabel("سيتم إضافة مخطط الجودة هنا\nQuality chart will be added here")
        self.quality_chart_label.setAlignment(Qt.AlignCenter)
        self.quality_chart_label.setStyleSheet("background-color: #404040; padding: 50px; border-radius: 8px;")
        chart_layout.addWidget(self.quality_chart_label)
        
        layout.addWidget(chart_group)
        
        return widget
        
    def create_defects_tab(self):
        """Create defects tracking tab"""
        widget = QWidget()
        layout = QVBoxLayout(widget)
        
        # Defects summary
        summary_layout = QHBoxLayout()
        
        # Common defects list
        defects_group = QGroupBox("العيوب الشائعة / Common Defects")
        defects_layout = QVBoxLayout(defects_group)
        
        self.defects_list = QTableWidget()
        self.defects_list.setColumnCount(3)
        self.defects_list.setHorizontalHeaderLabels([
            "نوع العيب / Defect Type", "التكرار / Frequency", "النسبة / Percentage"
        ])
        
        defects_layout.addWidget(self.defects_list)
        
        summary_layout.addWidget(defects_group)
        
        # Corrective actions
        actions_group = QGroupBox("الإجراءات التصحيحية / Corrective Actions")
        actions_layout = QVBoxLayout(actions_group)
        
        self.actions_list = QTableWidget()
        self.actions_list.setColumnCount(3)
        self.actions_list.setHorizontalHeaderLabels([
            "العيب / Defect", "الإجراء / Action", "الحالة / Status"
        ])
        
        actions_layout.addWidget(self.actions_list)
        
        summary_layout.addWidget(actions_group)
        
        layout.addLayout(summary_layout)
        
        return widget
        
    def create_metric_card(self, title, value, color):
        """Create metric card widget"""
        card = QGroupBox()
        card.setStyleSheet(f"""
            QGroupBox {{
                background-color: {color};
                border-radius: 8px;
                font-weight: bold;
                color: white;
                padding: 15px;
            }}
        """)
        
        layout = QVBoxLayout(card)
        
        title_label = QLabel(title)
        title_label.setFont(QFont("Arial", 10))
        title_label.setAlignment(Qt.AlignCenter)
        title_label.setWordWrap(True)
        
        value_label = QLabel(value)
        value_label.setFont(QFont("Arial", 18, QFont.Bold))
        value_label.setAlignment(Qt.AlignCenter)
        
        layout.addWidget(title_label)
        layout.addWidget(value_label)
        
        return card
        
    def load_inspections(self):
        """Load quality inspections from database"""
        conn = self.db_manager.get_connection()
        cursor = conn.cursor()
        
        cursor.execute("""
            SELECT qi.id, qi.inspection_number, s.slab_number, u.full_name as inspector_name,
                   qi.inspection_date, qi.overall_grade, qi.surface_quality, qi.status
            FROM quality_inspections qi
            JOIN slabs s ON qi.slab_id = s.id
            JOIN users u ON qi.inspector_id = u.id
            ORDER BY qi.created_at DESC
        """)
        
        inspections = cursor.fetchall()
        
        self.inspections_table.setRowCount(len(inspections))
        
        for row, inspection in enumerate(inspections):
            self.inspections_table.setItem(row, 0, QTableWidgetItem(inspection['inspection_number']))
            self.inspections_table.setItem(row, 1, QTableWidgetItem(inspection['slab_number']))
            self.inspections_table.setItem(row, 2, QTableWidgetItem(inspection['inspector_name']))
            self.inspections_table.setItem(row, 3, QTableWidgetItem(inspection['inspection_date']))
            
            # Grade with color coding
            grade_item = QTableWidgetItem(inspection['overall_grade'])
            if inspection['overall_grade'] == 'A':
                grade_item.setBackground(QColor('#28a745'))
            elif inspection['overall_grade'] == 'B':
                grade_item.setBackground(QColor('#17a2b8'))
            elif inspection['overall_grade'] == 'C':
                grade_item.setBackground(QColor('#ffc107'))
            else:
                grade_item.setBackground(QColor('#dc3545'))
            
            self.inspections_table.setItem(row, 4, grade_item)
            self.inspections_table.setItem(row, 5, QTableWidgetItem(f"{inspection['surface_quality']}/5"))
            
            # Status with color coding
            status_item = QTableWidgetItem(inspection['status'])
            if inspection['status'] == 'passed':
                status_item.setBackground(QColor('#28a745'))
            elif inspection['status'] == 'failed':
                status_item.setBackground(QColor('#dc3545'))
            else:
                status_item.setBackground(QColor('#ffc107'))
            
            self.inspections_table.setItem(row, 6, status_item)
            
            # Action buttons
            actions_widget = QWidget()
            actions_layout = QHBoxLayout(actions_widget)
            actions_layout.setContentsMargins(5, 5, 5, 5)
            
            view_btn = QPushButton("عرض / View")
            view_btn.setFixedSize(60, 25)
            view_btn.clicked.connect(lambda checked, iid=inspection['id']: self.view_inspection(iid))
            
            edit_btn = QPushButton("تعديل / Edit")
            edit_btn.setFixedSize(60, 25)
            edit_btn.clicked.connect(lambda checked, iid=inspection['id']: self.edit_inspection(iid))
            
            actions_layout.addWidget(view_btn)
            actions_layout.addWidget(edit_btn)
            
            self.inspections_table.setCellWidget(row, 7, actions_widget)
            
        conn.close()
        
    def create_new_inspection(self):
        """Create new quality inspection"""
        dialog = QualityInspectionDialog(self.db_manager, self.user_data)
        if dialog.exec_() == QDialog.Accepted:
            self.load_inspections()
            
    def view_inspection(self, inspection_id):
        """View inspection details"""
        dialog = QualityInspectionDialog(self.db_manager, self.user_data, inspection_id, view_only=True)
        dialog.exec_()
        
    def edit_inspection(self, inspection_id):
        """Edit inspection"""
        dialog = QualityInspectionDialog(self.db_manager, self.user_data, inspection_id)
        if dialog.exec_() == QDialog.Accepted:
            self.load_inspections()
            
    def filter_inspections(self):
        """Filter inspections by status and grade"""
        status_filter = self.status_filter.currentText()
        grade_filter = self.grade_filter.currentText()
        
        for row in range(self.inspections_table.rowCount()):
            show_row = True
            
            # Filter by status
            if not ("الكل" in status_filter or "All" in status_filter):
                status_item = self.inspections_table.item(row, 6)
                if status_item:
                    if not any(keyword in status_filter.lower() 
                             for keyword in status_item.text().lower().split()):
                        show_row = False
                        
            # Filter by grade
            if show_row and not ("الكل" in grade_filter or "All" in grade_filter):
                grade_item = self.inspections_table.item(row, 4)
                if grade_item and grade_filter not in grade_item.text():
                    show_row = False
                    
            self.inspections_table.setRowHidden(row, not show_row)
            
    def generate_quality_report(self):
        """Generate quality control report"""
        dialog = QualityReportDialog(self.db_manager)
        dialog.exec_()

class QualityInspectionDialog(QDialog):
    def __init__(self, db_manager, user_data, inspection_id=None, view_only=False):
        super().__init__()
        self.db_manager = db_manager
        self.user_data = user_data
        self.inspection_id = inspection_id
        self.view_only = view_only
        self.init_ui()
        
        if inspection_id:
            self.load_inspection_data()
            
    def init_ui(self):
        self.setWindowTitle("فحص الجودة / Quality Inspection")
        self.setFixedSize(600, 700)
        
        layout = QVBoxLayout()
        
        # Inspection header
        header_group = QGroupBox("معلومات الفحص / Inspection Information")
        header_layout = QFormLayout(header_group)
        
        self.inspection_number_input = QLineEdit()
        self.inspection_number_input.setText(self.generate_inspection_number())
        self.inspection_number_input.setReadOnly(True)
        header_layout.addRow("رقم الفحص / Inspection No.:", self.inspection_number_input)
        
        # Slab selection
        self.slab_combo = QComboBox()
        self.load_slabs()
        header_layout.addRow("البلاطة / Slab:", self.slab_combo)
        
        # Inspector (current user)
        self.inspector_label = QLabel(self.user_data['full_name'])
        header_layout.addRow("المفتش / Inspector:", self.inspector_label)
        
        # Inspection date
        self.inspection_date_input = QDateEdit()
        self.inspection_date_input.setDate(QDate.currentDate())
        self.inspection_date_input.setCalendarPopup(True)
        header_layout.addRow("تاريخ الفحص / Inspection Date:", self.inspection_date_input)
        
        layout.addWidget(header_group)
        
        # Quality criteria
        criteria_group = QGroupBox("معايير الجودة / Quality Criteria")
        criteria_layout = QFormLayout(criteria_group)
        
        # Surface quality (1-5 scale)
        surface_layout = QHBoxLayout()
        self.surface_quality_slider = QSlider(Qt.Horizontal)
        self.surface_quality_slider.setRange(1, 5)
        self.surface_quality_slider.setValue(5)
        self.surface_quality_slider.valueChanged.connect(self.update_surface_label)
        
        self.surface_quality_label = QLabel("5")
        surface_layout.addWidget(self.surface_quality_slider)
        surface_layout.addWidget(self.surface_quality_label)
        
        criteria_layout.addRow("جودة السطح / Surface Quality (1-5):", surface_layout)
        
        # Dimensional accuracy
        dimensional_layout = QHBoxLayout()
        self.dimensional_accuracy_slider = QSlider(Qt.Horizontal)
        self.dimensional_accuracy_slider.setRange(1, 5)
        self.dimensional_accuracy_slider.setValue(5)
        self.dimensional_accuracy_slider.valueChanged.connect(self.update_dimensional_label)
        
        self.dimensional_accuracy_label = QLabel("5")
        dimensional_layout.addWidget(self.dimensional_accuracy_slider)
        dimensional_layout.addWidget(self.dimensional_accuracy_label)
        
        criteria_layout.addRow("دقة الأبعاد / Dimensional Accuracy (1-5):", dimensional_layout)
        
        # Color consistency
        color_layout = QHBoxLayout()
        self.color_consistency_slider = QSlider(Qt.Horizontal)
        self.color_consistency_slider.setRange(1, 5)
        self.color_consistency_slider.setValue(5)
        self.color_consistency_slider.valueChanged.connect(self.update_color_label)
        
        self.color_consistency_label = QLabel("5")
        color_layout.addWidget(self.color_consistency_slider)
        color_layout.addWidget(self.color_consistency_label)
        
        criteria_layout.addRow("ثبات اللون / Color Consistency (1-5):", color_layout)
        
        # Overall grade
        self.overall_grade_combo = QComboBox()
        self.overall_grade_combo.addItems(["A", "B", "C", "D"])
        criteria_layout.addRow("الدرجة الكلية / Overall Grade:", self.overall_grade_combo)
        
        layout.addWidget(criteria_group)
        
        # Defects section
        defects_group = QGroupBox("العيوب / Defects")
        defects_layout = QVBoxLayout(defects_group)
        
        self.defects_input = QTextEdit()
        self.defects_input.setMaximumHeight(80)
        self.defects_input.setPlaceholderText("وصف العيوب إن وجدت / Describe defects if any")
        defects_layout.addWidget(self.defects_input)
        
        layout.addWidget(defects_group)
        
        # Corrective actions
        actions_group = QGroupBox("الإجراءات التصحيحية / Corrective Actions")
        actions_layout = QVBoxLayout(actions_group)
        
        self.corrective_actions_input = QTextEdit()
        self.corrective_actions_input.setMaximumHeight(80)
        self.corrective_actions_input.setPlaceholderText("الإجراءات التصحيحية المطلوبة / Required corrective actions")
        actions_layout.addWidget(self.corrective_actions_input)
        
        layout.addWidget(actions_group)
        
        # Status
        status_group = QGroupBox("حالة الفحص / Inspection Status")
        status_layout = QFormLayout(status_group)
        
        self.status_combo = QComboBox()
        self.status_combo.addItems([
            "مقبول / Passed", "مرفوض / Failed", "يحتاج إعادة فحص / Retest Required"
        ])
        status_layout.addRow("الحالة / Status:", self.status_combo)
        
        layout.addWidget(status_group)
        
        # Buttons
        if not self.view_only:
            buttons_layout = QHBoxLayout()
            
            save_btn = QPushButton("حفظ / Save")
            save_btn.clicked.connect(self.save_inspection)
            
            cancel_btn = QPushButton("إلغاء / Cancel")
            cancel_btn.clicked.connect(self.reject)
            
            buttons_layout.addWidget(save_btn)
            buttons_layout.addWidget(cancel_btn)
            
            layout.addLayout(buttons_layout)
        else:
            # View only mode
            close_btn = QPushButton("إغلاق / Close")
            close_btn.clicked.connect(self.accept)
            layout.addWidget(close_btn)
            
            # Disable all inputs
            self.setWindowTitle("عرض فحص الجودة / View Quality Inspection")
            for widget in self.findChildren((QLineEdit, QTextEdit, QComboBox, QSlider, QDateEdit)):
                widget.setEnabled(False)
        
        self.setLayout(layout)
        
    def generate_inspection_number(self):
        """Generate unique inspection number"""
        timestamp = datetime.now().strftime("%Y%m%d%H%M%S")
        return f"QI-{timestamp}"
        
    def load_slabs(self):
        """Load slabs for inspection"""
        conn = self.db_manager.get_connection()
        cursor = conn.cursor()
        
        cursor.execute("""
            SELECT id, slab_number, granite_type, status
            FROM slabs
            WHERE status IN ('in_stock', 'quality_check')
            ORDER BY slab_number
        """)
        
        slabs = cursor.fetchall()
        
        self.slab_combo.addItem("اختر البلاطة / Select Slab", None)
        for slab in slabs:
            display_text = f"{slab['slab_number']} - {slab['granite_type']}"
            self.slab_combo.addItem(display_text, slab['id'])
            
        conn.close()
        
    def update_surface_label(self, value):
        """Update surface quality label"""
        self.surface_quality_label.setText(str(value))
        
    def update_dimensional_label(self, value):
        """Update dimensional accuracy label"""
        self.dimensional_accuracy_label.setText(str(value))
        
    def update_color_label(self, value):
        """Update color consistency label"""
        self.color_consistency_label.setText(str(value))
        
    def load_inspection_data(self):
        """Load existing inspection data"""
        conn = self.db_manager.get_connection()
        cursor = conn.cursor()
        
        cursor.execute("SELECT * FROM quality_inspections WHERE id = ?", (self.inspection_id,))
        inspection = cursor.fetchone()
        
        if inspection:
            self.inspection_number_input.setText(inspection['inspection_number'])
            
            # Set slab
            for i in range(self.slab_combo.count()):
                if self.slab_combo.itemData(i) == inspection['slab_id']:
                    self.slab_combo.setCurrentIndex(i)
                    break
                    
            self.inspection_date_input.setDate(QDate.fromString(inspection['inspection_date'], "yyyy-MM-dd"))
            self.surface_quality_slider.setValue(inspection['surface_quality'])
            self.dimensional_accuracy_slider.setValue(inspection['dimensional_accuracy'])
            self.color_consistency_slider.setValue(inspection['color_consistency'])
            self.overall_grade_combo.setCurrentText(inspection['overall_grade'])
            self.defects_input.setPlainText(inspection['defects'] or '')
            self.corrective_actions_input.setPlainText(inspection['corrective_actions'] or '')
            
            # Set status
            status_map = {
                'passed': 0,
                'failed': 1,
                'retest': 2
            }
            self.status_combo.setCurrentIndex(status_map.get(inspection['status'], 0))
            
        conn.close()
        
    def save_inspection(self):
        """Save quality inspection"""
        slab_id = self.slab_combo.currentData()
        
        if not slab_id:
            QMessageBox.warning(self, "تحذير / Warning", "يرجى اختيار البلاطة\nPlease select slab")
            return
            
        conn = self.db_manager.get_connection()
        cursor = conn.cursor()
        
        try:
            status_map = ['passed', 'failed', 'retest']
            status = status_map[self.status_combo.currentIndex()]
            
            if self.inspection_id:
                # Update existing inspection
                cursor.execute("""
                    UPDATE quality_inspections 
                    SET slab_id = ?, inspection_date = ?, overall_grade = ?,
                        surface_quality = ?, dimensional_accuracy = ?, color_consistency = ?,
                        defects = ?, corrective_actions = ?, status = ?
                    WHERE id = ?
                """, (slab_id, self.inspection_date_input.date().toString("yyyy-MM-dd"),
                     self.overall_grade_combo.currentText(),
                     self.surface_quality_slider.value(),
                     self.dimensional_accuracy_slider.value(),
                     self.color_consistency_slider.value(),
                     self.defects_input.toPlainText(),
                     self.corrective_actions_input.toPlainText(),
                     status, self.inspection_id))
            else:
                # Add new inspection
                cursor.execute("""
                    INSERT INTO quality_inspections 
                    (inspection_number, slab_id, inspector_id, inspection_date, overall_grade,
                     surface_quality, dimensional_accuracy, color_consistency, defects,
                     corrective_actions, status)
                    VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                """, (self.inspection_number_input.text(), slab_id, self.user_data['id'],
                     self.inspection_date_input.date().toString("yyyy-MM-dd"),
                     self.overall_grade_combo.currentText(),
                     self.surface_quality_slider.value(),
                     self.dimensional_accuracy_slider.value(),
                     self.color_consistency_slider.value(),
                     self.defects_input.toPlainText(),
                     self.corrective_actions_input.toPlainText(),
                     status))
            
            # Update slab quality grade
            cursor.execute("""
                UPDATE slabs 
                SET quality_grade = ?, status = ?
                WHERE id = ?
            """, (self.overall_grade_combo.currentText(),
                 'quality_approved' if status == 'passed' else 'quality_rejected',
                 slab_id))
            
            conn.commit()
            
            QMessageBox.information(self, "نجح / Success", 
                                  "تم حفظ فحص الجودة بنجاح\nQuality inspection saved successfully")
            
            self.accept()
            
        except Exception as e:
            QMessageBox.critical(self, "خطأ / Error", f"Error saving inspection: {str(e)}")
        finally:
            conn.close()

class QualityReportDialog(QDialog):
    def __init__(self, db_manager):
        super().__init__()
        self.db_manager = db_manager
        self.init_ui()
        self.generate_report()
        
    def init_ui(self):
        self.setWindowTitle("تقرير الجودة / Quality Report")
        self.setFixedSize(700, 600)
        
        layout = QVBoxLayout()
        
        # Report controls
        controls_layout = QHBoxLayout()
        
        from_date_label = QLabel("من / From:")
        self.from_date_input = QDateEdit()
        self.from_date_input.setDate(QDate.currentDate().addDays(-30))
        self.from_date_input.setCalendarPopup(True)
        
        to_date_label = QLabel("إلى / To:")
        self.to_date_input = QDateEdit()
        self.to_date_input.setDate(QDate.currentDate())
        self.to_date_input.setCalendarPopup(True)
        
        generate_btn = QPushButton("إنشاء التقرير / Generate Report")
        generate_btn.clicked.connect(self.generate_report)
        
        controls_layout.addWidget(from_date_label)
        controls_layout.addWidget(self.from_date_input)
        controls_layout.addWidget(to_date_label)
        controls_layout.addWidget(self.to_date_input)
        controls_layout.addWidget(generate_btn)
        controls_layout.addStretch()
        
        layout.addLayout(controls_layout)
        
        # Report display
        self.report_display = QTextEdit()
        self.report_display.setReadOnly(True)
        self.report_display.setFont(QFont("Courier", 10))
        layout.addWidget(self.report_display)
        
        # Close button
        close_btn = QPushButton("إغلاق / Close")
        close_btn.clicked.connect(self.accept)
        layout.addWidget(close_btn)
        
        self.setLayout(layout)
        
    def generate_report(self):
        """Generate quality control report"""
        from_date = self.from_date_input.date().toString("yyyy-MM-dd")
        to_date = self.to_date_input.date().toString("yyyy-MM-dd")
        
        conn = self.db_manager.get_connection()
        cursor = conn.cursor()
        
        # Get inspection statistics
        cursor.execute("""
            SELECT 
                COUNT(*) as total_inspections,
                SUM(CASE WHEN status = 'passed' THEN 1 ELSE 0 END) as passed_inspections,
                SUM(CASE WHEN status = 'failed' THEN 1 ELSE 0 END) as failed_inspections,
                SUM(CASE WHEN status = 'retest' THEN 1 ELSE 0 END) as retest_inspections,
                AVG(surface_quality) as avg_surface_quality,
                AVG(dimensional_accuracy) as avg_dimensional_accuracy,
                AVG(color_consistency) as avg_color_consistency
            FROM quality_inspections
            WHERE inspection_date BETWEEN ? AND ?
        """, (from_date, to_date))
        
        stats = cursor.fetchone()
        
        # Get grade distribution
        cursor.execute("""
            SELECT overall_grade, COUNT(*) as count
            FROM quality_inspections
            WHERE inspection_date BETWEEN ? AND ?
            GROUP BY overall_grade
            ORDER BY overall_grade
        """, (from_date, to_date))
        
        grade_distribution = cursor.fetchall()
        
        # Get common defects
        cursor.execute("""
            SELECT defects, COUNT(*) as frequency
            FROM quality_inspections
            WHERE inspection_date BETWEEN ? AND ? AND defects IS NOT NULL AND defects != ''
            GROUP BY defects
            ORDER BY frequency DESC
            LIMIT 5
        """, (from_date, to_date))
        
        common_defects = cursor.fetchall()
        
        # Generate report text
        report_text = f"""
تقرير مراقبة الجودة من {from_date} إلى {to_date}
Quality Control Report from {from_date} to {to_date}

═══════════════════════════════════════════════════════════════

إحصائيات عامة / General Statistics:
─────────────────────────────────────────────────────────────

إجمالي الفحوصات / Total Inspections: {stats['total_inspections'] or 0}
الفحوصات المقبولة / Passed Inspections: {stats['passed_inspections'] or 0}
الفحوصات المرفوضة / Failed Inspections: {stats['failed_inspections'] or 0}
يحتاج إعادة فحص / Retest Required: {stats['retest_inspections'] or 0}

معدل النجاح / Pass Rate: {((stats['passed_inspections'] or 0) / max(stats['total_inspections'] or 1, 1) * 100):.1f}%
معدل الرفض / Failure Rate: {((stats['failed_inspections'] or 0) / max(stats['total_inspections'] or 1, 1) * 100):.1f}%

متوسط المعايير / Average Criteria Scores:
─────────────────────────────────────────────────────────────

جودة السطح / Surface Quality: {(stats['avg_surface_quality'] or 0):.1f}/5
دقة الأبعاد / Dimensional Accuracy: {(stats['avg_dimensional_accuracy'] or 0):.1f}/5
ثبات اللون / Color Consistency: {(stats['avg_color_consistency'] or 0):.1f}/5

توزيع الدرجات / Grade Distribution:
─────────────────────────────────────────────────────────────

"""
        
        for grade in grade_distribution:
            percentage = (grade['count'] / max(stats['total_inspections'] or 1, 1)) * 100
            report_text += f"الدرجة {grade['overall_grade']} / Grade {grade['overall_grade']}: {grade['count']} ({percentage:.1f}%)\n"
            
        report_text += f"""

العيوب الشائعة / Common Defects:
─────────────────────────────────────────────────────────────

"""
        
        if common_defects:
            for i, defect in enumerate(common_defects, 1):
                report_text += f"{i}. {defect['defects']} - التكرار / Frequency: {defect['frequency']}\n"
        else:
            report_text += "لا توجد عيوب مسجلة / No defects recorded\n"
            
        report_text += f"""

التوصيات / Recommendations:
─────────────────────────────────────────────────────────────

"""
        
        # Add recommendations based on statistics
        if stats['total_inspections'] and stats['total_inspections'] > 0:
            pass_rate = (stats['passed_inspections'] or 0) / stats['total_inspections'] * 100
            
            if pass_rate < 80:
                report_text += "• معدل النجاح منخفض - يحتاج تحسين عمليات الإنتاج\n"
                report_text += "• Low pass rate - production processes need improvement\n"
                
            if (stats['avg_surface_quality'] or 0) < 4:
                report_text += "• جودة السطح تحتاج تحسين - مراجعة عمليات التشطيب\n"
                report_text += "• Surface quality needs improvement - review finishing processes\n"
                
            if (stats['avg_dimensional_accuracy'] or 0) < 4:
                report_text += "• دقة الأبعاد تحتاج تحسين - معايرة المعدات\n"
                report_text += "• Dimensional accuracy needs improvement - equipment calibration\n"
                
            if len(common_defects) > 0:
                report_text += "• التركيز على معالجة العيوب الشائعة المذكورة أعلاه\n"
                report_text += "• Focus on addressing the common defects listed above\n"
        else:
            report_text += "لا توجد بيانات كافية لتقديم توصيات\n"
            report_text += "Insufficient data to provide recommendations\n"
            
        self.report_display.setPlainText(report_text)
        conn.close()
