#!/usr/bin/env python3
"""
Modern Theme System for Al-Hassan Stone Factory Management System
نظام التنسيق الحديث لنظام إدارة مصنع الحسن ستون
"""

class ModernTheme:
    """Modern theme with beautiful styling"""
    
    # Color Palette - لوحة الألوان
    COLORS = {
        # Primary Colors - الألوان الأساسية
        'primary': '#0078d4',
        'primary_dark': '#106ebe',
        'primary_light': '#40e0d0',
        
        # Secondary Colors - الألوان الثانوية
        'secondary': '#6c757d',
        'success': '#28a745',
        'warning': '#ffc107',
        'danger': '#dc3545',
        'info': '#17a2b8',
        
        # Background Colors - ألوان الخلفية
        'bg_primary': '#ffffff',
        'bg_secondary': '#f8f9fa',
        'bg_dark': '#343a40',
        'bg_light': '#e9ecef',
        
        # Text Colors - ألوان النص
        'text_primary': '#212529',
        'text_secondary': '#6c757d',
        'text_muted': '#868e96',
        'text_white': '#ffffff',
        
        # Border Colors - ألوان الحدود
        'border_light': '#dee2e6',
        'border_medium': '#adb5bd',
        'border_dark': '#495057',
        
        # Gradient Colors - الألوان المتدرجة
        'gradient_start': '#667eea',
        'gradient_end': '#764ba2',
    }
    
    @staticmethod
    def get_main_stylesheet():
        """Get main application stylesheet"""
        return f"""
        /* ===== MAIN APPLICATION STYLES ===== */
        QMainWindow {{
            background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                stop:0 {ModernTheme.COLORS['bg_secondary']},
                stop:1 {ModernTheme.COLORS['bg_light']});
            color: {ModernTheme.COLORS['text_primary']};
            font-family: 'Segoe UI', 'Tahoma', 'Arial', sans-serif;
            font-size: 10pt;
        }}
        
        /* ===== SIDEBAR STYLES ===== */
        QFrame#sidebar {{
            background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                stop:0 {ModernTheme.COLORS['primary']},
                stop:1 {ModernTheme.COLORS['primary_dark']});
            border: none;
            border-radius: 0px;
            min-width: 250px;
            max-width: 250px;
        }}
        
        /* ===== BUTTON STYLES ===== */
        QPushButton {{
            background-color: {ModernTheme.COLORS['primary']};
            color: {ModernTheme.COLORS['text_white']};
            border: none;
            border-radius: 8px;
            padding: 12px 20px;
            font-weight: 600;
            font-size: 10pt;
            min-height: 20px;
        }}
        
        QPushButton:hover {{
            background-color: {ModernTheme.COLORS['primary_dark']};
            transform: translateY(-1px);
        }}
        
        QPushButton:pressed {{
            background-color: {ModernTheme.COLORS['primary_dark']};
            transform: translateY(1px);
        }}
        
        QPushButton:disabled {{
            background-color: {ModernTheme.COLORS['secondary']};
            color: {ModernTheme.COLORS['text_muted']};
        }}
        
        /* ===== SUCCESS BUTTON ===== */
        QPushButton#success {{
            background-color: {ModernTheme.COLORS['success']};
        }}
        
        QPushButton#success:hover {{
            background-color: #218838;
        }}
        
        /* ===== WARNING BUTTON ===== */
        QPushButton#warning {{
            background-color: {ModernTheme.COLORS['warning']};
            color: {ModernTheme.COLORS['text_primary']};
        }}
        
        QPushButton#warning:hover {{
            background-color: #e0a800;
        }}
        
        /* ===== DANGER BUTTON ===== */
        QPushButton#danger {{
            background-color: {ModernTheme.COLORS['danger']};
        }}
        
        QPushButton#danger:hover {{
            background-color: #c82333;
        }}
        
        /* ===== INPUT FIELD STYLES ===== */
        QLineEdit {{
            background-color: {ModernTheme.COLORS['bg_primary']};
            border: 2px solid {ModernTheme.COLORS['border_light']};
            border-radius: 8px;
            padding: 12px 16px;
            font-size: 10pt;
            color: {ModernTheme.COLORS['text_primary']};
            selection-background-color: {ModernTheme.COLORS['primary_light']};
        }}
        
        QLineEdit:focus {{
            border-color: {ModernTheme.COLORS['primary']};
            background-color: {ModernTheme.COLORS['bg_primary']};
            box-shadow: 0 0 0 3px rgba(0, 120, 212, 0.1);
        }}
        
        QLineEdit:hover {{
            border-color: {ModernTheme.COLORS['border_medium']};
        }}
        
        /* ===== TEXT AREA STYLES ===== */
        QTextEdit, QPlainTextEdit {{
            background-color: {ModernTheme.COLORS['bg_primary']};
            border: 2px solid {ModernTheme.COLORS['border_light']};
            border-radius: 8px;
            padding: 12px;
            font-size: 10pt;
            color: {ModernTheme.COLORS['text_primary']};
            selection-background-color: {ModernTheme.COLORS['primary_light']};
        }}
        
        QTextEdit:focus, QPlainTextEdit:focus {{
            border-color: {ModernTheme.COLORS['primary']};
        }}
        
        /* ===== COMBO BOX STYLES ===== */
        QComboBox {{
            background-color: {ModernTheme.COLORS['bg_primary']};
            border: 2px solid {ModernTheme.COLORS['border_light']};
            border-radius: 8px;
            padding: 8px 12px;
            font-size: 10pt;
            color: {ModernTheme.COLORS['text_primary']};
            min-height: 20px;
        }}
        
        QComboBox:focus {{
            border-color: {ModernTheme.COLORS['primary']};
        }}
        
        QComboBox::drop-down {{
            border: none;
            width: 30px;
            border-radius: 0px 8px 8px 0px;
        }}
        
        QComboBox::down-arrow {{
            image: none;
            border-left: 6px solid transparent;
            border-right: 6px solid transparent;
            border-top: 6px solid {ModernTheme.COLORS['text_secondary']};
            margin-right: 8px;
        }}
        
        QComboBox QAbstractItemView {{
            background-color: {ModernTheme.COLORS['bg_primary']};
            border: 1px solid {ModernTheme.COLORS['border_light']};
            border-radius: 8px;
            selection-background-color: {ModernTheme.COLORS['primary']};
            selection-color: {ModernTheme.COLORS['text_white']};
            padding: 4px;
        }}
        """
    
    @staticmethod
    def get_table_stylesheet():
        """Get table widget stylesheet"""
        return f"""
        /* ===== TABLE STYLES ===== */
        QTableWidget {{
            background-color: {ModernTheme.COLORS['bg_primary']};
            border: 1px solid {ModernTheme.COLORS['border_light']};
            border-radius: 12px;
            gridline-color: {ModernTheme.COLORS['border_light']};
            font-size: 9pt;
            selection-background-color: {ModernTheme.COLORS['primary_light']};
        }}
        
        QTableWidget::item {{
            padding: 12px 8px;
            border-bottom: 1px solid {ModernTheme.COLORS['border_light']};
        }}
        
        QTableWidget::item:selected {{
            background-color: {ModernTheme.COLORS['primary']};
            color: {ModernTheme.COLORS['text_white']};
        }}
        
        QTableWidget::item:hover {{
            background-color: {ModernTheme.COLORS['bg_light']};
        }}
        
        QHeaderView::section {{
            background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                stop:0 {ModernTheme.COLORS['primary']},
                stop:1 {ModernTheme.COLORS['primary_dark']});
            color: {ModernTheme.COLORS['text_white']};
            padding: 12px 8px;
            border: none;
            font-weight: 600;
            font-size: 9pt;
        }}
        
        QHeaderView::section:first {{
            border-top-left-radius: 12px;
        }}
        
        QHeaderView::section:last {{
            border-top-right-radius: 12px;
        }}
        """
    
    @staticmethod
    def get_card_stylesheet():
        """Get card/frame stylesheet"""
        return f"""
        /* ===== CARD STYLES ===== */
        QFrame.card {{
            background-color: {ModernTheme.COLORS['bg_primary']};
            border: 1px solid {ModernTheme.COLORS['border_light']};
            border-radius: 16px;
            padding: 20px;
            margin: 8px;
        }}
        
        QFrame.card:hover {{
            border-color: {ModernTheme.COLORS['primary']};
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
        }}
        
        /* ===== STATS CARD ===== */
        QFrame.stats-card {{
            background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                stop:0 {ModernTheme.COLORS['gradient_start']},
                stop:1 {ModernTheme.COLORS['gradient_end']});
            border: none;
            border-radius: 16px;
            padding: 24px;
            margin: 8px;
        }}
        
        QLabel.stats-number {{
            color: {ModernTheme.COLORS['text_white']};
            font-size: 28pt;
            font-weight: 700;
        }}
        
        QLabel.stats-label {{
            color: {ModernTheme.COLORS['text_white']};
            font-size: 11pt;
            font-weight: 500;
        }}
        """

    @staticmethod
    def get_sidebar_stylesheet():
        """Get sidebar stylesheet"""
        return f"""
        /* ===== SIDEBAR NAVIGATION ===== */
        QPushButton.nav-button {{
            background-color: transparent;
            color: {ModernTheme.COLORS['text_white']};
            border: none;
            border-radius: 8px;
            padding: 16px 20px;
            text-align: left;
            font-size: 11pt;
            font-weight: 500;
            margin: 4px 8px;
        }}

        QPushButton.nav-button:hover {{
            background-color: rgba(255, 255, 255, 0.1);
            transform: translateX(4px);
        }}

        QPushButton.nav-button:pressed {{
            background-color: rgba(255, 255, 255, 0.2);
        }}

        QPushButton.nav-button.active {{
            background-color: {ModernTheme.COLORS['bg_primary']};
            color: {ModernTheme.COLORS['primary']};
            font-weight: 600;
        }}

        QLabel.nav-title {{
            color: {ModernTheme.COLORS['text_white']};
            font-size: 14pt;
            font-weight: 700;
            padding: 20px;
            margin-bottom: 10px;
        }}

        QLabel.nav-subtitle {{
            color: rgba(255, 255, 255, 0.8);
            font-size: 9pt;
            padding: 0px 20px;
            margin-bottom: 20px;
        }}
        """

    @staticmethod
    def get_dialog_stylesheet():
        """Get dialog stylesheet"""
        return f"""
        /* ===== DIALOG STYLES ===== */
        QDialog {{
            background-color: {ModernTheme.COLORS['bg_primary']};
            border: 2px solid {ModernTheme.COLORS['border_light']};
            border-radius: 16px;
        }}

        QDialog QLabel {{
            color: {ModernTheme.COLORS['text_primary']};
            font-size: 10pt;
        }}

        QDialog QLabel.title {{
            font-size: 16pt;
            font-weight: 700;
            color: {ModernTheme.COLORS['primary']};
            margin-bottom: 16px;
        }}

        /* ===== MESSAGE BOX STYLES ===== */
        QMessageBox {{
            background-color: {ModernTheme.COLORS['bg_primary']};
            border-radius: 12px;
        }}

        QMessageBox QLabel {{
            color: {ModernTheme.COLORS['text_primary']};
            font-size: 11pt;
            padding: 16px;
        }}
        """

    @staticmethod
    def get_menu_stylesheet():
        """Get menu stylesheet"""
        return f"""
        /* ===== MENU STYLES ===== */
        QMenuBar {{
            background-color: {ModernTheme.COLORS['bg_primary']};
            border-bottom: 1px solid {ModernTheme.COLORS['border_light']};
            padding: 4px;
        }}

        QMenuBar::item {{
            background-color: transparent;
            color: {ModernTheme.COLORS['text_primary']};
            padding: 8px 16px;
            border-radius: 6px;
            margin: 2px;
        }}

        QMenuBar::item:selected {{
            background-color: {ModernTheme.COLORS['primary']};
            color: {ModernTheme.COLORS['text_white']};
        }}

        QMenu {{
            background-color: {ModernTheme.COLORS['bg_primary']};
            border: 1px solid {ModernTheme.COLORS['border_light']};
            border-radius: 8px;
            padding: 8px;
        }}

        QMenu::item {{
            background-color: transparent;
            color: {ModernTheme.COLORS['text_primary']};
            padding: 8px 16px;
            border-radius: 6px;
            margin: 2px;
        }}

        QMenu::item:selected {{
            background-color: {ModernTheme.COLORS['primary']};
            color: {ModernTheme.COLORS['text_white']};
        }}

        QMenu::separator {{
            height: 1px;
            background-color: {ModernTheme.COLORS['border_light']};
            margin: 4px 8px;
        }}
        """

    @staticmethod
    def get_progress_stylesheet():
        """Get progress bar stylesheet"""
        return f"""
        /* ===== PROGRESS BAR STYLES ===== */
        QProgressBar {{
            background-color: {ModernTheme.COLORS['bg_light']};
            border: none;
            border-radius: 8px;
            text-align: center;
            font-weight: 600;
            color: {ModernTheme.COLORS['text_primary']};
            height: 20px;
        }}

        QProgressBar::chunk {{
            background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                stop:0 {ModernTheme.COLORS['primary']},
                stop:1 {ModernTheme.COLORS['primary_light']});
            border-radius: 8px;
        }}
        """

    @staticmethod
    def get_complete_stylesheet():
        """Get complete application stylesheet"""
        return (
            ModernTheme.get_main_stylesheet() +
            ModernTheme.get_table_stylesheet() +
            ModernTheme.get_card_stylesheet() +
            ModernTheme.get_sidebar_stylesheet() +
            ModernTheme.get_dialog_stylesheet() +
            ModernTheme.get_menu_stylesheet() +
            ModernTheme.get_progress_stylesheet()
        )
