from PyQt5.QtWidgets import (QDialog, QVBoxLayout, QHBoxLayout, QLabel, 
                            QComboBox, QPushButton, QGroupBox, QFormLayout,
                            QCheckBox, QLineEdit, QTabWidget, QWidget,
                            QSpinBox, QDateEdit, QTimeEdit, QTextEdit)
from PyQt5.QtCore import Qt, pyqtSignal, QDate, QTime
from PyQt5.QtGui import QFont
from utils.config import Config
from utils.translator import translator
from utils.cultural_manager import cultural_manager
from datetime import datetime, date

class CulturalPreferencesDialog(QDialog):
    preferences_changed = pyqtSignal(str)
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.config = Config()
        self.init_ui()
        self.load_current_preferences()
        
    def init_ui(self):
        self.setWindowTitle(translator.tr("cultural_preferences"))
        self.setFixedSize(600, 500)
        self.setModal(True)
        
        layout = QVBoxLayout()
        
        # Title
        title = QLabel(translator.tr("cultural_preferences"))
        title.setFont(QFont("Arial", 16, QFont.Bold))
        title.setAlignment(Qt.AlignCenter)
        title.setStyleSheet("color: #0078d4; margin-bottom: 20px;")
        layout.addWidget(title)
        
        # Create tabs
        self.tab_widget = QTabWidget()
        
        # General tab
        general_tab = self.create_general_tab()
        self.tab_widget.addTab(general_tab, translator.tr("general"))
        
        # Numbers & Currency tab
        numbers_tab = self.create_numbers_tab()
        self.tab_widget.addTab(numbers_tab, translator.tr("numbers_currency"))
        
        # Date & Time tab
        datetime_tab = self.create_datetime_tab()
        self.tab_widget.addTab(datetime_tab, translator.tr("date_time"))
        
        # Preview tab
        preview_tab = self.create_preview_tab()
        self.tab_widget.addTab(preview_tab, translator.tr("preview"))
        
        layout.addWidget(self.tab_widget)
        
        # Buttons
        buttons_layout = QHBoxLayout()
        
        apply_btn = QPushButton(translator.tr("apply"))
        apply_btn.setFont(QFont("Arial", 10, QFont.Bold))
        apply_btn.clicked.connect(self.apply_preferences)
        
        reset_btn = QPushButton(translator.tr("reset_defaults"))
        reset_btn.setFont(QFont("Arial", 10))
        reset_btn.clicked.connect(self.reset_to_defaults)
        
        cancel_btn = QPushButton(translator.tr("cancel"))
        cancel_btn.setFont(QFont("Arial", 10))
        cancel_btn.clicked.connect(self.reject)
        
        buttons_layout.addWidget(apply_btn)
        buttons_layout.addWidget(reset_btn)
        buttons_layout.addStretch()
        buttons_layout.addWidget(cancel_btn)
        
        layout.addLayout(buttons_layout)
        
        self.setLayout(layout)
        
        # Connect signals
        self.tab_widget.currentChanged.connect(self.update_preview)
        
    def create_general_tab(self):
        """Create general preferences tab"""
        widget = QWidget()
        layout = QVBoxLayout(widget)
        
        # Culture selection
        culture_group = QGroupBox(translator.tr("culture_region"))
        culture_layout = QFormLayout(culture_group)
        
        self.culture_combo = QComboBox()
        self.populate_cultures()
        self.culture_combo.currentTextChanged.connect(self.on_culture_changed)
        culture_layout.addRow(translator.tr("culture") + ":", self.culture_combo)
        
        layout.addWidget(culture_group)
        
        # Calendar system
        calendar_group = QGroupBox(translator.tr("calendar_system"))
        calendar_layout = QFormLayout(calendar_group)
        
        self.calendar_combo = QComboBox()
        self.populate_calendar_systems()
        calendar_layout.addRow(translator.tr("calendar") + ":", self.calendar_combo)
        
        layout.addWidget(calendar_group)
        
        # Week preferences
        week_group = QGroupBox(translator.tr("week_preferences"))
        week_layout = QFormLayout(week_group)
        
        self.week_start_combo = QComboBox()
        self.week_start_combo.addItems([
            translator.tr("sunday"),
            translator.tr("monday"),
            translator.tr("tuesday"),
            translator.tr("wednesday"),
            translator.tr("thursday"),
            translator.tr("friday"),
            translator.tr("saturday")
        ])
        week_layout.addRow(translator.tr("week_starts_on") + ":", self.week_start_combo)
        
        layout.addWidget(week_group)
        
        layout.addStretch()
        
        return widget
        
    def create_numbers_tab(self):
        """Create numbers and currency tab"""
        widget = QWidget()
        layout = QVBoxLayout(widget)
        
        # Number format
        number_group = QGroupBox(translator.tr("number_format"))
        number_layout = QFormLayout(number_group)
        
        self.number_system_combo = QComboBox()
        self.populate_number_systems()
        number_layout.addRow(translator.tr("number_system") + ":", self.number_system_combo)
        
        self.decimal_separator_edit = QLineEdit()
        self.decimal_separator_edit.setMaxLength(1)
        number_layout.addRow(translator.tr("decimal_separator") + ":", self.decimal_separator_edit)
        
        self.thousand_separator_edit = QLineEdit()
        self.thousand_separator_edit.setMaxLength(1)
        number_layout.addRow(translator.tr("thousand_separator") + ":", self.thousand_separator_edit)
        
        layout.addWidget(number_group)
        
        # Currency format
        currency_group = QGroupBox(translator.tr("currency_format"))
        currency_layout = QFormLayout(currency_group)
        
        self.currency_symbol_edit = QLineEdit()
        currency_layout.addRow(translator.tr("currency_symbol") + ":", self.currency_symbol_edit)
        
        self.currency_position_combo = QComboBox()
        self.currency_position_combo.addItems([
            translator.tr("before_amount"),
            translator.tr("after_amount")
        ])
        currency_layout.addRow(translator.tr("symbol_position") + ":", self.currency_position_combo)
        
        layout.addWidget(currency_group)
        
        layout.addStretch()
        
        return widget
        
    def create_datetime_tab(self):
        """Create date and time tab"""
        widget = QWidget()
        layout = QVBoxLayout(widget)
        
        # Date format
        date_group = QGroupBox(translator.tr("date_format"))
        date_layout = QFormLayout(date_group)
        
        self.date_format_combo = QComboBox()
        self.date_format_combo.addItems([
            "dd/MM/yyyy",
            "MM/dd/yyyy", 
            "yyyy/MM/dd",
            "dd.MM.yyyy",
            "dd-MM-yyyy"
        ])
        date_layout.addRow(translator.tr("date_format") + ":", self.date_format_combo)
        
        layout.addWidget(date_group)
        
        # Time format
        time_group = QGroupBox(translator.tr("time_format"))
        time_layout = QFormLayout(time_group)
        
        self.time_format_combo = QComboBox()
        self.time_format_combo.addItems([
            translator.tr("12_hour"),
            translator.tr("24_hour")
        ])
        time_layout.addRow(translator.tr("time_format") + ":", self.time_format_combo)
        
        layout.addWidget(time_group)
        
        layout.addStretch()
        
        return widget
        
    def create_preview_tab(self):
        """Create preview tab"""
        widget = QWidget()
        layout = QVBoxLayout(widget)
        
        preview_label = QLabel(translator.tr("preview_formatting"))
        preview_label.setFont(QFont("Arial", 12, QFont.Bold))
        layout.addWidget(preview_label)
        
        self.preview_text = QTextEdit()
        self.preview_text.setReadOnly(True)
        self.preview_text.setStyleSheet("""
            QTextEdit {
                background-color: #404040;
                border: 1px solid #555555;
                border-radius: 4px;
                padding: 10px;
                font-family: monospace;
            }
        """)
        layout.addWidget(self.preview_text)
        
        return widget
        
    def populate_cultures(self):
        """Populate culture combo box"""
        cultures = cultural_manager.get_available_cultures()
        
        for code, name in cultures.items():
            self.culture_combo.addItem(f"{name} ({code})", code)
            
    def populate_calendar_systems(self):
        """Populate calendar systems"""
        calendars = cultural_manager.get_calendar_systems()
        
        for code, name in calendars.items():
            self.calendar_combo.addItem(name, code)
            
    def populate_number_systems(self):
        """Populate number systems"""
        systems = {
            'western': 'Western (0123456789)',
            'arabic-indic': 'Arabic-Indic (٠١٢٣٤٥٦٧٨٩)',
            'persian': 'Persian (۰۱۲۳۴۵۶۷۸۹)',
            'urdu': 'Urdu (۰۱۲۳۴۵۶۷۸۹)',
            'devanagari': 'Devanagari (०१२३४५६७८९)'
        }
        
        for code, name in systems.items():
            self.number_system_combo.addItem(name, code)
            
    def load_current_preferences(self):
        """Load current cultural preferences"""
        current_culture = self.config.get('culture', 'en-US')
        
        # Set culture
        for i in range(self.culture_combo.count()):
            if self.culture_combo.itemData(i) == current_culture:
                self.culture_combo.setCurrentIndex(i)
                break
                
        # Load other preferences
        cultural_manager.set_culture(current_culture)
        
        # Set calendar system
        calendar_system = cultural_manager.calendar_system
        for i in range(self.calendar_combo.count()):
            if self.calendar_combo.itemData(i) == calendar_system:
                self.calendar_combo.setCurrentIndex(i)
                break
                
        # Set number system
        number_format = cultural_manager.number_format
        for i in range(self.number_system_combo.count()):
            if self.number_system_combo.itemData(i) == number_format:
                self.number_system_combo.setCurrentIndex(i)
                break
                
        # Set separators
        preset = cultural_manager.cultural_presets.get(current_culture, {})
        self.decimal_separator_edit.setText(preset.get('decimal_separator', '.'))
        self.thousand_separator_edit.setText(preset.get('thousand_separator', ','))
        
        # Set currency
        self.currency_symbol_edit.setText(preset.get('currency_symbol', '$'))
        currency_position = preset.get('currency_position', 'before')
        self.currency_position_combo.setCurrentIndex(0 if currency_position == 'before' else 1)
        
        # Set date format
        date_format = preset.get('date_format', 'dd/MM/yyyy')
        index = self.date_format_combo.findText(date_format)
        if index >= 0:
            self.date_format_combo.setCurrentIndex(index)
            
        # Set time format
        time_format = preset.get('time_format', '24h')
        self.time_format_combo.setCurrentIndex(0 if time_format == '12h' else 1)
        
        # Set week start
        week_start = preset.get('week_start', 'sunday')
        week_days = ['sunday', 'monday', 'tuesday', 'wednesday', 'thursday', 'friday', 'saturday']
        if week_start in week_days:
            self.week_start_combo.setCurrentIndex(week_days.index(week_start))
            
        self.update_preview()
        
    def on_culture_changed(self):
        """Handle culture change"""
        culture_code = self.culture_combo.currentData()
        if culture_code:
            cultural_manager.set_culture(culture_code)
            self.load_current_preferences()
            
    def update_preview(self):
        """Update preview text"""
        if not hasattr(self, 'preview_text'):
            return
            
        # Sample data
        sample_number = 1234567.89
        sample_currency = 15000.50
        sample_date = date.today()
        sample_time = datetime.now().time()
        
        # Apply current settings temporarily
        temp_culture = self.culture_combo.currentData()
        if temp_culture:
            cultural_manager.set_culture(temp_culture)
            
        # Override with current form values
        cultural_manager.number_format = self.number_system_combo.currentData() or 'western'
        cultural_manager.calendar_system = self.calendar_combo.currentData() or 'gregorian'
        
        # Format samples
        formatted_number = cultural_manager.format_number(sample_number)
        formatted_currency = cultural_manager.format_currency(sample_currency)
        formatted_date_short = cultural_manager.format_date(sample_date, 'short')
        formatted_date_long = cultural_manager.format_date(sample_date, 'long')
        formatted_time = cultural_manager.format_time(sample_time)
        
        # Create preview text
        preview_text = f"""
{translator.tr("formatting_preview")}:

{translator.tr("numbers")}:
  {translator.tr("large_number")}: {formatted_number}
  {translator.tr("currency")}: {formatted_currency}

{translator.tr("dates")}:
  {translator.tr("short_date")}: {formatted_date_short}
  {translator.tr("long_date")}: {formatted_date_long}

{translator.tr("time")}:
  {translator.tr("current_time")}: {formatted_time}

{translator.tr("calendar_system")}: {cultural_manager.calendar_system.title()}
{translator.tr("number_system")}: {cultural_manager.number_format.title()}

{translator.tr("week_info")}:
  {translator.tr("week_starts")}: {cultural_manager.get_week_start_day().title()}
  {translator.tr("weekend_days")}: {', '.join(cultural_manager.get_weekend_days())}
        """
        
        self.preview_text.setPlainText(preview_text.strip())
        
    def apply_preferences(self):
        """Apply cultural preferences"""
        culture_code = self.culture_combo.currentData()
        
        if culture_code:
            # Save to config
            self.config.set('culture', culture_code)
            
            # Update cultural manager
            cultural_manager.set_culture(culture_code)
            
            # Save custom preferences
            custom_prefs = {
                'calendar_system': self.calendar_combo.currentData(),
                'number_format': self.number_system_combo.currentData(),
                'decimal_separator': self.decimal_separator_edit.text(),
                'thousand_separator': self.thousand_separator_edit.text(),
                'currency_symbol': self.currency_symbol_edit.text(),
                'currency_position': 'before' if self.currency_position_combo.currentIndex() == 0 else 'after',
                'date_format': self.date_format_combo.currentText(),
                'time_format': '12h' if self.time_format_combo.currentIndex() == 0 else '24h',
                'week_start': ['sunday', 'monday', 'tuesday', 'wednesday', 'thursday', 'friday', 'saturday'][self.week_start_combo.currentIndex()]
            }
            
            # Save custom preferences
            for key, value in custom_prefs.items():
                self.config.set(f'culture_{key}', value)
            
            # Emit signal
            self.preferences_changed.emit(culture_code)
            
            self.accept()
            
    def reset_to_defaults(self):
        """Reset to default preferences"""
        culture_code = self.culture_combo.currentData()
        if culture_code:
            cultural_manager.set_culture(culture_code)
            self.load_current_preferences()
