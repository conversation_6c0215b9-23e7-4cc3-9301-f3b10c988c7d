#!/usr/bin/env python3
"""
Enhanced Buttons for Al-Hassan Stone Factory
أزرار محسنة لنظام إدارة مصنع الحسن ستون
"""

from PyQt5.QtWidgets import QPushButton, QHBoxLayout, QVBoxLayout, QWidget, QLabel
from PyQt5.QtCore import Qt, pyqtSignal, QPropertyAnimation, QEasingCurve, QRect
from PyQt5.QtGui import QFont, QIcon, QPainter, QColor, QBrush, QPen

from ui.styles.modern_theme import ModernTheme

class ActionButton(QPushButton):
    """Enhanced action button with icon and description"""
    
    def __init__(self, title, description="", icon="", button_type="primary", parent=None):
        super().__init__(parent)
        self.title = title
        self.description = description
        self.icon = icon
        self.button_type = button_type
        self.setup_button()
        
    def setup_button(self):
        """Setup the button appearance and behavior"""
        # Set button text
        if self.icon:
            button_text = f"{self.icon} {self.title}"
        else:
            button_text = self.title
            
        if self.description:
            button_text += f"\n{self.description}"
            
        self.setText(button_text)
        
        # Set minimum size
        self.setMinimumSize(200, 80)
        
        # Apply styling based on type
        self.apply_button_style()
        
        # Set font
        self.setFont(QFont("Arial", 10, QFont.Bold))
        
    def apply_button_style(self):
        """Apply styling based on button type"""
        styles = {
            "primary": {
                "bg": ModernTheme.COLORS['primary'],
                "hover": ModernTheme.COLORS['primary_dark'],
                "text": "white"
            },
            "success": {
                "bg": ModernTheme.COLORS['success'],
                "hover": "#218838",
                "text": "white"
            },
            "warning": {
                "bg": ModernTheme.COLORS['warning'],
                "hover": "#e0a800",
                "text": "black"
            },
            "danger": {
                "bg": ModernTheme.COLORS['danger'],
                "hover": "#c82333",
                "text": "white"
            },
            "info": {
                "bg": ModernTheme.COLORS['info'],
                "hover": "#138496",
                "text": "white"
            },
            "secondary": {
                "bg": ModernTheme.COLORS['secondary'],
                "hover": "#545b62",
                "text": "white"
            }
        }
        
        style = styles.get(self.button_type, styles["primary"])
        
        self.setStyleSheet(f"""
            QPushButton {{
                background-color: {style['bg']};
                color: {style['text']};
                border: none;
                border-radius: 12px;
                padding: 16px 20px;
                font-weight: 600;
                text-align: center;
            }}
            QPushButton:hover {{
                background-color: {style['hover']};
            }}
            QPushButton:pressed {{
                background-color: {style['hover']};
            }}
        """)

class IconButton(QPushButton):
    """Icon-only button with tooltip"""
    
    def __init__(self, icon, tooltip="", size=40, color=None, parent=None):
        super().__init__(parent)
        self.icon_text = icon
        self.tooltip_text = tooltip
        self.button_size = size
        self.color = color or ModernTheme.COLORS['primary']
        self.setup_button()
        
    def setup_button(self):
        """Setup the icon button"""
        self.setText(self.icon_text)
        self.setFixedSize(self.button_size, self.button_size)
        self.setToolTip(self.tooltip_text)
        
        self.setStyleSheet(f"""
            QPushButton {{
                background-color: {self.color};
                color: white;
                border: none;
                border-radius: {self.button_size // 2}px;
                font-size: {self.button_size // 3}pt;
                font-weight: bold;
            }}
            QPushButton:hover {{
                background-color: {self.darken_color(self.color)};
            }}
            QPushButton:pressed {{
                background-color: {self.darken_color(self.color)};
            }}
        """)
        
    def darken_color(self, color):
        """Darken color for hover effect"""
        color_map = {
            ModernTheme.COLORS['primary']: ModernTheme.COLORS['primary_dark'],
            ModernTheme.COLORS['success']: '#218838',
            ModernTheme.COLORS['warning']: '#e0a800',
            ModernTheme.COLORS['danger']: '#c82333',
            ModernTheme.COLORS['info']: '#138496',
        }
        return color_map.get(color, '#2c3e50')

class ToggleButton(QPushButton):
    """Toggle button with on/off states"""
    
    toggled_custom = pyqtSignal(bool)
    
    def __init__(self, text_on="ON", text_off="OFF", parent=None):
        super().__init__(parent)
        self.text_on = text_on
        self.text_off = text_off
        self.is_on = False
        self.setup_button()
        
    def setup_button(self):
        """Setup the toggle button"""
        self.setCheckable(True)
        self.setMinimumSize(100, 40)
        self.update_appearance()
        self.clicked.connect(self.handle_toggle)
        
    def handle_toggle(self):
        """Handle toggle state change"""
        self.is_on = self.isChecked()
        self.update_appearance()
        self.toggled_custom.emit(self.is_on)
        
    def update_appearance(self):
        """Update button appearance based on state"""
        if self.is_on:
            self.setText(self.text_on)
            bg_color = ModernTheme.COLORS['success']
            hover_color = '#218838'
        else:
            self.setText(self.text_off)
            bg_color = ModernTheme.COLORS['secondary']
            hover_color = '#545b62'
            
        self.setStyleSheet(f"""
            QPushButton {{
                background-color: {bg_color};
                color: white;
                border: none;
                border-radius: 20px;
                padding: 8px 16px;
                font-weight: 600;
            }}
            QPushButton:hover {{
                background-color: {hover_color};
            }}
        """)

class ButtonGroup(QWidget):
    """Group of related buttons"""
    
    def __init__(self, title="", orientation="horizontal", parent=None):
        super().__init__(parent)
        self.title = title
        self.orientation = orientation
        self.buttons = []
        self.setup_group()
        
    def setup_group(self):
        """Setup the button group"""
        if self.orientation == "horizontal":
            self.layout = QHBoxLayout(self)
        else:
            self.layout = QVBoxLayout(self)
            
        self.layout.setSpacing(10)
        self.layout.setContentsMargins(10, 10, 10, 10)
        
        if self.title:
            title_label = QLabel(self.title)
            title_label.setFont(QFont("Arial", 12, QFont.Bold))
            title_label.setStyleSheet(f"color: {ModernTheme.COLORS['text_primary']};")
            self.layout.addWidget(title_label)
            
    def add_button(self, button):
        """Add a button to the group"""
        self.buttons.append(button)
        self.layout.addWidget(button)
        
    def add_action_button(self, title, description="", icon="", button_type="primary", callback=None):
        """Add an action button to the group"""
        button = ActionButton(title, description, icon, button_type)
        if callback:
            button.clicked.connect(callback)
        self.add_button(button)
        return button
        
    def add_icon_button(self, icon, tooltip="", size=40, color=None, callback=None):
        """Add an icon button to the group"""
        button = IconButton(icon, tooltip, size, color)
        if callback:
            button.clicked.connect(callback)
        self.add_button(button)
        return button

class FloatingActionButton(QPushButton):
    """Floating action button (FAB)"""
    
    def __init__(self, icon="➕", size=60, color=None, parent=None):
        super().__init__(parent)
        self.icon_text = icon
        self.button_size = size
        self.color = color or ModernTheme.COLORS['primary']
        self.setup_button()
        
    def setup_button(self):
        """Setup the floating action button"""
        self.setText(self.icon_text)
        self.setFixedSize(self.button_size, self.button_size)
        
        self.setStyleSheet(f"""
            QPushButton {{
                background-color: {self.color};
                color: white;
                border: none;
                border-radius: {self.button_size // 2}px;
                font-size: {self.button_size // 3}pt;
                font-weight: bold;
                box-shadow: 0 4px 8px rgba(0, 0, 0, 0.3);
            }}
            QPushButton:hover {{
                background-color: {self.darken_color(self.color)};
            }}
            QPushButton:pressed {{
                background-color: {self.darken_color(self.color)};
            }}
        """)
        
    def darken_color(self, color):
        """Darken color for hover effect"""
        color_map = {
            ModernTheme.COLORS['primary']: ModernTheme.COLORS['primary_dark'],
            ModernTheme.COLORS['success']: '#218838',
            ModernTheme.COLORS['warning']: '#e0a800',
            ModernTheme.COLORS['danger']: '#c82333',
        }
        return color_map.get(color, '#2c3e50')

# Predefined button configurations for common actions
COMMON_BUTTONS = {
    "save": {
        "title": "حفظ",
        "description": "Save",
        "icon": "💾",
        "type": "success"
    },
    "edit": {
        "title": "تعديل",
        "description": "Edit",
        "icon": "✏️",
        "type": "warning"
    },
    "delete": {
        "title": "حذف",
        "description": "Delete",
        "icon": "🗑️",
        "type": "danger"
    },
    "view": {
        "title": "عرض",
        "description": "View",
        "icon": "👁️",
        "type": "info"
    },
    "print": {
        "title": "طباعة",
        "description": "Print",
        "icon": "🖨️",
        "type": "secondary"
    },
    "export": {
        "title": "تصدير",
        "description": "Export",
        "icon": "📤",
        "type": "primary"
    },
    "add": {
        "title": "إضافة",
        "description": "Add New",
        "icon": "➕",
        "type": "success"
    },
    "search": {
        "title": "بحث",
        "description": "Search",
        "icon": "🔍",
        "type": "info"
    }
}

def create_common_button(button_key, callback=None):
    """Create a common button by key"""
    if button_key not in COMMON_BUTTONS:
        raise ValueError(f"Unknown button key: {button_key}")
        
    config = COMMON_BUTTONS[button_key]
    button = ActionButton(
        title=config["title"],
        description=config["description"],
        icon=config["icon"],
        button_type=config["type"]
    )
    
    if callback:
        button.clicked.connect(callback)
        
    return button
