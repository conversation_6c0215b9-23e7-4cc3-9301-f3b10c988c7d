# Al-Hassan Stone Factory Management System
## نظام إدارة مصنع الحسن ستون

A comprehensive desktop application for managing stone factory operations including inventory, sales, customers, and financial reporting.

## Features / المميزات

### Core Modules / الوحدات الأساسية
- **User Management** / إدارة المستخدمين
- **Dashboard** / لوحة التحكم  
- **Customer & Supplier Management** / إدارة العملاء والموردين
- **Truck & Block Entry** / تسجيل الشاحنات والكتل
- **Block Cutting Module** / وحدة تقطيع الكتل
- **Sales & Invoicing** / المبيعات والفوترة
- **Inventory Management** / إدارة المخزون
- **Expenses Tracking** / تتبع المصروفات
- **Comprehensive Reports** / التقارير الشاملة
- **Activity Logging** / سجل الأنشطة

### Technical Features / المميزات التقنية
- **Modern PyQt5 Interface** / واجهة PyQt5 عصرية
- **SQLite Database** / قاعدة بيانات SQLite
- **Arabic/English Support** / دعم العربية والإنجليزية
- **Dark Theme** / المظهر الداكن
- **Export to CSV/Excel** / تصدير إلى CSV/Excel
- **Standalone Executable** / ملف تنفيذي مستقل

## Multi-Language Support / الدعم متعدد اللغات

The system now supports **10 languages** with full RTL (Right-to-Left) support:

### Supported Languages / اللغات المدعومة
- **العربية (Arabic)** - RTL Support
- **English** - Default
- **Français (French)**
- **Español (Spanish)**
- **Deutsch (German)**
- **Italiano (Italian)**
- **Türkçe (Turkish)**
- **Русский (Russian)**
- **中文 (Chinese)**
- **اردو (Urdu)** - RTL Support

### Language Features / مميزات اللغة
- **Dynamic Language Switching** / تبديل اللغة الديناميكي
- **RTL Layout Support** / دعم التخطيط من اليمين لليسار
- **Localized Number Formats** / تنسيقات الأرقام المحلية
- **Date/Time Formatting** / تنسيق التاريخ والوقت
- **Currency Formatting** / تنسيق العملة
- **Font Optimization** / تحسين الخطوط

### Changing Language / تغيير اللغة
1. Click the **Language** button on login screen
2. Select desired language from dropdown
3. Preview changes in real-time
4. Apply changes (restart may be required)

### Adding New Languages / إضافة لغات جديدة
1. Create translation file in `translations/` folder
2. Add language to supported languages list
3. Update RTL languages if needed
4. Test thoroughly with the new language

## Installation / التثبيت

### Requirements / المتطلبات
- Python 3.11+
- PyQt5
- SQLite3

### Setup / الإعداد
\`\`\`bash
# Install dependencies
pip install -r requirements.txt

# Run application
python main.py

# Build executable
python build_exe.py
\`\`\`

## Usage / الاستخدام

### Default Login / تسجيل الدخول الافتراضي
- **Username**: admin
- **Password**: admin123

### Main Features / الميزات الرئيسية

#### 1. Customer Management / إدارة العملاء
- Add/Edit/Delete customers
- Track customer balances
- Customer transaction history

#### 2. Truck Registration / تسجيل الشاحنات
- Record truck arrivals
- Track granite blocks per truck
- Calculate costs and weights

#### 3. Block Management / إدارة الكتل
- Register granite blocks
- Track block dimensions and types
- Monitor cutting operations

#### 4. Sales & Invoicing / المبيعات والفوترة
- Create professional invoices
- Track payments and outstanding amounts
- Customer account statements

#### 5. Inventory Control / التحكم في المخزون
- Real-time stock levels
- Track slab locations and status
- Inventory movement reports

#### 6. Financial Reports / التقارير المالية
- Sales reports by date range
- Expense tracking by category
- Profit/loss statements
- Customer account reports

## Database Structure / هيكل قاعدة البيانات

### Main Tables / الجداول الرئيسية
- `users` - User accounts and roles
- `customers` - Customer information
- `suppliers` - Supplier details
- `trucks` - Truck registrations
- `blocks` - Granite blocks
- `slabs` - Cut slabs inventory
- `sales` - Sales transactions
- `sale_items` - Invoice line items
- `expenses` - Operating expenses
- `activity_logs` - System activity logs

## File Structure / هيكل الملفات

\`\`\`
al-hassan-stone-factory/
├── main.py                 # Main application entry point
├── database/
│   └── db_manager.py      # Database management
├── ui/
│   ├── login_window.py    # Login interface
│   ├── main_dashboard.py  # Main dashboard
│   └── modules/           # Feature modules
│       ├── customers_module.py
│       ├── trucks_module.py
│       ├── blocks_module.py
│       ├── sales_module.py
│       ├── inventory_module.py
│       ├── expenses_module.py
│       └── reports_module.py
├── utils/
│   └── config.py          # Configuration management
├── assets/                # Images and icons
├── config/                # Configuration files
├── data/                  # Database files
├── requirements.txt       # Python dependencies
├── build_exe.py          # Executable builder
└── README.md             # This file
\`\`\`

## Building Executable / بناء الملف التنفيذي

To create a standalone executable:

\`\`\`bash
python build_exe.py
\`\`\`

This will create `AlHassanStoneFactory.exe` in the `dist/` folder.

## User Roles / أدوار المستخدمين

- **Admin** / المدير: Full system access
- **Manager** / المدير: Operations and reports
- **Accountant** / المحاسب: Financial operations
- **Operator** / المشغل: Basic operations

## Support / الدعم

For technical support or feature requests, please contact the development team.

## License / الترخيص

This software is proprietary to Al-Hassan Stone Factory.

---

**Version**: 1.0.0  
**Last Updated**: 2024  
**Developer**: Al-Hassan Stone Development Team
